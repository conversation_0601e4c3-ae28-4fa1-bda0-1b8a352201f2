import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useExerciseV2Actions } from '../useExerciseV2Actions'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useRestTimer } from '@/components/workout-v2/RestTimer'
import { useAuthStore } from '@/stores/authStore'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Mock dependencies
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/components/workout-v2/RestTimer')
vi.mock('@/stores/authStore')
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

describe('useExerciseV2Actions - Save Button', () => {
  const mockSaveSet = vi.fn()
  const mockNextSet = vi.fn()
  const mockStartRestTimer = vi.fn()
  const mockSetSaveError = vi.fn()

  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    Timer: 0,
    IsFinished: false,
  }

  const mockWorkoutSession = {
    id: 'session-123',
    exercises: [],
  }

  const mockSetData = {
    reps: 10,
    weight: 100,
    duration: 0,
  }

  const mockCurrentSet: WorkoutLogSerieModel = {
    Id: 1,
    ExerciseId: 1,
    Reps: 10,
    Weight: { Kg: 45, Lb: 100 },
    IsWarmups: false,
    IsNext: true,
    IsFinished: false,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useWorkout).mockReturnValue({
      saveSet: mockSaveSet,
    } as any)
    vi.mocked(useWorkoutStore).mockReturnValue({
      nextSet: mockNextSet,
    } as any)
    vi.mocked(useRestTimer).mockReturnValue({
      startRestTimer: mockStartRestTimer,
    } as any)
    vi.mocked(useAuthStore).mockReturnValue({
      getCachedUserInfo: () => ({ MassUnit: 'lbs' }),
    } as any)
  })

  it('should call saveSet when save button is clicked with valid data', async () => {
    // Given: Valid currentExercise and workoutSession
    const allSets = [
      mockCurrentSet,
      { ...mockCurrentSet, Id: 2, IsNext: false }, // Add another set so hasMoreSets is true
    ]

    const { result } = renderHook(() =>
      useExerciseV2Actions({
        currentExercise: mockExercise,
        workoutSession: mockWorkoutSession,
        setData: mockSetData,
        currentSetIndex: 0,
        allSets,
        isWarmup: false,
        isLastSet: false,
        isFirstWorkSet: false,
        currentSet: mockCurrentSet,
        setSaveError: mockSetSaveError,
      })
    )

    // When: handleCompleteSet is called (save button clicked)
    await act(async () => {
      await result.current.handleCompleteSet()
    })

    // Then: saveSet should be called with correct parameters
    expect(mockSaveSet).toHaveBeenCalledWith({
      exerciseId: 1,
      reps: 10,
      weight: 100,
      isWarmup: false,
      setNumber: 1,
      duration: undefined,
      RIR: undefined,
    })
    expect(mockNextSet).toHaveBeenCalled()
    expect(mockStartRestTimer).toHaveBeenCalledWith(90, {
      reps: 10,
      weight: 100,
      unit: 'lbs',
    }) // 90s for work sets with next set info
  })

  it('should NOT call saveSet when currentExercise is null', async () => {
    // Given: currentExercise is null
    const { result } = renderHook(() =>
      useExerciseV2Actions({
        currentExercise: null,
        workoutSession: mockWorkoutSession,
        setData: mockSetData,
        currentSetIndex: 0,
        allSets: [mockCurrentSet],
        isWarmup: false,
        isLastSet: false,
        isFirstWorkSet: false,
        currentSet: mockCurrentSet,
        setSaveError: mockSetSaveError,
      })
    )

    // When: handleCompleteSet is called
    await act(async () => {
      await result.current.handleCompleteSet()
    })

    // Then: saveSet should NOT be called
    expect(mockSaveSet).not.toHaveBeenCalled()
    expect(mockNextSet).not.toHaveBeenCalled()
    expect(mockSetSaveError).not.toHaveBeenCalled()
  })

  it('should NOT call saveSet when workoutSession is null', async () => {
    // Given: workoutSession is null
    const { result } = renderHook(() =>
      useExerciseV2Actions({
        currentExercise: mockExercise,
        workoutSession: null,
        setData: mockSetData,
        currentSetIndex: 0,
        allSets: [mockCurrentSet],
        isWarmup: false,
        isLastSet: false,
        isFirstWorkSet: false,
        currentSet: mockCurrentSet,
        setSaveError: mockSetSaveError,
      })
    )

    // When: handleCompleteSet is called
    await act(async () => {
      await result.current.handleCompleteSet()
    })

    // Then: saveSet should NOT be called
    expect(mockSaveSet).not.toHaveBeenCalled()
    expect(mockNextSet).not.toHaveBeenCalled()
    expect(mockSetSaveError).not.toHaveBeenCalled()
  })

  it('should handle saveSet errors gracefully', async () => {
    // Given: saveSet will throw an error
    const errorMessage = 'Network error'
    mockSaveSet.mockRejectedValueOnce(new Error(errorMessage))

    const { result } = renderHook(() =>
      useExerciseV2Actions({
        currentExercise: mockExercise,
        workoutSession: mockWorkoutSession,
        setData: mockSetData,
        currentSetIndex: 0,
        allSets: [mockCurrentSet],
        isWarmup: false,
        isLastSet: false,
        isFirstWorkSet: false,
        currentSet: mockCurrentSet,
        setSaveError: mockSetSaveError,
      })
    )

    // When: handleCompleteSet is called
    await act(async () => {
      await result.current.handleCompleteSet()
    })

    // Then: Error should be handled
    expect(mockSaveSet).toHaveBeenCalled()
    expect(mockSetSaveError).toHaveBeenCalledWith(errorMessage)
    expect(mockNextSet).not.toHaveBeenCalled() // Should not progress on error
  })

  it('should use 30s rest timer for warmup sets', async () => {
    // Given: Current set is a warmup
    const warmupSet = { ...mockCurrentSet, IsWarmups: true }
    const allSets = [
      warmupSet,
      { ...mockCurrentSet, Id: 2, IsNext: false, IsWarmups: false }, // Add work set after warmup
    ]

    const { result } = renderHook(() =>
      useExerciseV2Actions({
        currentExercise: mockExercise,
        workoutSession: mockWorkoutSession,
        setData: mockSetData,
        currentSetIndex: 0,
        allSets,
        isWarmup: true,
        isLastSet: false,
        isFirstWorkSet: false,
        currentSet: warmupSet,
        setSaveError: mockSetSaveError,
      })
    )

    // When: handleCompleteSet is called
    await act(async () => {
      await result.current.handleCompleteSet()
    })

    // Then: Should use 30s rest timer for warmups
    expect(mockStartRestTimer).toHaveBeenCalledWith(30, {
      reps: 10,
      weight: 100,
      unit: 'lbs',
    })
  })

  it('should add debug logging when currentExercise or workoutSession is missing', async () => {
    // Given: Spy on console methods
    const consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    const consoleWarnSpy = vi
      .spyOn(console, 'warn')
      .mockImplementation(() => {})

    const { result } = renderHook(() =>
      useExerciseV2Actions({
        currentExercise: null,
        workoutSession: null,
        setData: mockSetData,
        currentSetIndex: 0,
        allSets: [mockCurrentSet],
        isWarmup: false,
        isLastSet: false,
        isFirstWorkSet: false,
        currentSet: mockCurrentSet,
        setSaveError: mockSetSaveError,
      })
    )

    // When: handleCompleteSet is called
    await act(async () => {
      await result.current.handleCompleteSet()
    })

    // Then: Should log debug information about save button click
    expect(consoleLogSpy).toHaveBeenCalledWith(
      '[ExerciseV2] Save button clicked - checking required data:',
      expect.objectContaining({
        hasCurrentExercise: false,
        hasWorkoutSession: false,
        workoutSessionId: 'null',
      })
    )

    // And: Should log warning about missing data
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      '[ExerciseV2] Cannot save set - missing required data:',
      expect.objectContaining({
        hasCurrentExercise: false,
        hasWorkoutSession: false,
      })
    )

    consoleLogSpy.mockRestore()
    consoleWarnSpy.mockRestore()
  })
})
