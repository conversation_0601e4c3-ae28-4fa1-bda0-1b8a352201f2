import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import { ExercisePageV2Client } from '../ExercisePageV2Client'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { useExercisePageInitialization } from '@/hooks/useExercisePageInitialization'
import { useExerciseV2Actions } from '@/hooks/useExerciseV2Actions'
import { useRouter } from 'next/navigation'

// Mock all dependencies
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/stores/authStore')
vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/hooks/useExercisePageInitialization')
vi.mock('@/hooks/useExerciseV2Actions')
vi.mock('@/hooks/useRIR')
vi.mock('next/navigation')
vi.mock('@/components/workout-v2/RestTimer', () => ({
  RestTimer: () => null,
  useRestTimer: () => ({ startRestTimer: vi.fn() }),
}))

const mockExercise = {
  Id: 1,
  Label: 'Bench Press',
  IsTimeBased: false,
  IsFinished: false,
}

const mockRecommendation = {
  Id: 1,
  ExerciseId: 1,
  Reps: 10,
  Weight: { Lb: 135, Kg: 61 },
  Series: 3,
  WarmupsCount: 2,
  IsNormalSets: true,
  NbPauses: 0,
}

const mockSetData = {
  reps: 10,
  weight: 135,
  duration: 45,
}

describe('ExercisePageV2Client - RIR Functionality', () => {
  const mockPush = vi.fn()
  const mockSaveSet = vi.fn()
  const mockHandleCompleteSet = vi.fn()
  const mockHandleSkipSet = vi.fn()
  const mockHandleRIRSelect = vi.fn()
  const mockHandleRIRCancel = vi.fn()

  // Define defaultSetScreenLogicReturn at describe level
  const defaultSetScreenLogicReturn = {
    currentExercise: mockExercise,
    exercises: [mockExercise],
    currentSetIndex: 0, // First warmup
    isSaving: false,
    saveError: null,
    showComplete: false,
    showExerciseComplete: false,
    recommendation: mockRecommendation,
    isLoading: false,
    error: null,
    isLastExercise: false,
    isLastSet: false,
    isWarmup: true, // Start with warmup
    isFirstWorkSet: false,
    completedSets: [],
    setData: mockSetData,
    setSetData: vi.fn(),
    setSaveError: vi.fn(),
    handleSaveSet: vi.fn(),
    refetchRecommendation: vi.fn(),
    showRIRPicker: false,
    handleRIRSelect: mockHandleRIRSelect,
    handleRIRCancel: mockHandleRIRCancel,
  } as any

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock router
    vi.mocked(useRouter).mockReturnValue({
      push: mockPush,
      replace: vi.fn(),
      back: vi.fn(),
      refresh: vi.fn(),
      prefetch: vi.fn(),
      forward: vi.fn(),
    })

    // Mock auth store
    vi.mocked(useAuthStore).mockReturnValue({
      getCachedUserInfo: () => ({ MassUnit: 'lbs' }),
    } as any)

    // Mock workout hook
    vi.mocked(useWorkout).mockReturnValue({
      isLoadingWorkout: false,
      workoutError: null,
      workoutSession: { id: '123' },
      saveSet: mockSaveSet,
    } as any)

    // Mock workout store
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadingStates: new Map(),
      exercises: [mockExercise],
      nextSet: vi.fn(),
    } as any)

    // Mock initialization hook
    vi.mocked(useExercisePageInitialization).mockReturnValue({
      isInitializing: false,
      loadingError: null,
      retryInitialization: vi.fn(),
    })

    // Mock set screen logic - default to warmup set
    vi.mocked(useSetScreenLogic).mockReturnValue(defaultSetScreenLogicReturn)

    // Mock exercise v2 actions
    vi.mocked(useExerciseV2Actions).mockReturnValue({
      handleCompleteSet: mockHandleCompleteSet,
      handleSkipSet: mockHandleSkipSet,
    })
  })

  describe('RIR Picker Display Logic', () => {
    it('should show RIR picker after saving first work set', async () => {
      // Update mock to simulate first work set
      vi.mocked(useSetScreenLogic).mockReturnValue({
        ...defaultSetScreenLogicReturn,
        currentSetIndex: 2, // After 2 warmups
        isWarmup: false,
        isFirstWorkSet: true,
        showRIRPicker: true, // This should be shown after save
      } as any)

      render(
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      )

      // Verify RIR picker is displayed
      await waitFor(() => {
        expect(screen.getByText('How hard was that?')).toBeInTheDocument()
      })

      // Verify RIR options are displayed
      expect(screen.getByText('Very hard (0 left)')).toBeInTheDocument()
      expect(screen.getByText('Could do 1-2 more')).toBeInTheDocument()
      expect(screen.getByText('Could do 3-4 more')).toBeInTheDocument()
    })

    it('should NOT show RIR picker for warmup sets', async () => {
      render(
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      )

      // Click save button
      const saveButton = await screen.findByText('Save set')
      await userEvent.click(saveButton)

      // Verify RIR picker is NOT displayed
      expect(screen.queryByText('How hard was that?')).not.toBeInTheDocument()
    })

    it('should NOT show RIR picker for time-based exercises', async () => {
      // Update mock for time-based exercise
      vi.mocked(useSetScreenLogic).mockReturnValue({
        ...defaultSetScreenLogicReturn,
        currentExercise: { ...mockExercise, IsTimeBased: true },
        currentSetIndex: 2,
        isWarmup: false,
        isFirstWorkSet: true,
        showRIRPicker: false, // Should not show for time-based
      } as any)

      render(
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      )

      // Verify RIR picker is NOT displayed
      expect(screen.queryByText('How hard was that?')).not.toBeInTheDocument()
    })
  })

  describe('RIR Selection Handling', () => {
    beforeEach(() => {
      // Set up for RIR picker display
      vi.mocked(useSetScreenLogic).mockReturnValue({
        ...defaultSetScreenLogicReturn,
        currentSetIndex: 2,
        isWarmup: false,
        isFirstWorkSet: true,
        showRIRPicker: true,
      } as any)
    })

    it('should handle RIR selection correctly', async () => {
      render(
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      )

      // Wait for RIR picker
      await waitFor(() => {
        expect(screen.getByText('How hard was that?')).toBeInTheDocument()
      })

      // Select RIR option
      const rirOption = screen.getByText('Could do 1-2 more')
      await userEvent.click(rirOption)

      // Verify handler was called
      expect(mockHandleRIRSelect).toHaveBeenCalledWith('1-2')
    })

    it('should handle RIR cancel correctly', async () => {
      render(
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      )

      // Wait for RIR picker
      await waitFor(() => {
        expect(screen.getByText('How hard was that?')).toBeInTheDocument()
      })

      // Click cancel
      const cancelButton = screen.getByRole('button', { name: /cancel/i })
      await userEvent.click(cancelButton)

      // Verify handler was called
      expect(mockHandleRIRCancel).toHaveBeenCalled()
    })

    it('should close RIR picker on backdrop click', async () => {
      render(
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      )

      // Wait for RIR picker
      await waitFor(() => {
        expect(screen.getByText('How hard was that?')).toBeInTheDocument()
      })

      // Click backdrop
      const backdrop = screen.getByRole('presentation')
      await userEvent.click(backdrop)

      // Verify handler was called
      expect(mockHandleRIRCancel).toHaveBeenCalled()
    })
  })

  describe('Integration with Save Flow', () => {
    it('should trigger RIR picker through handleCompleteSet', async () => {
      // Mock handleSaveSet to trigger RIR picker
      const mockHandleSaveSet = vi.fn()
      vi.mocked(useSetScreenLogic).mockReturnValue({
        ...defaultSetScreenLogicReturn,
        handleSaveSet: mockHandleSaveSet,
        isFirstWorkSet: true,
        isWarmup: false,
      } as any)

      render(
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      )

      // Find and click save button
      const saveButton = await screen.findByText('Save set')
      await userEvent.click(saveButton)

      // Wait for async operations
      await waitFor(() => {
        expect(mockHandleSaveSet).toHaveBeenCalled()
      })
    })
  })

  describe('Edge Cases', () => {
    it('should handle rapid save button clicks', async () => {
      const mockHandleSaveSet = vi.fn()
      vi.mocked(useSetScreenLogic).mockReturnValue({
        ...defaultSetScreenLogicReturn,
        handleSaveSet: mockHandleSaveSet,
      } as any)

      render(
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      )

      const saveButton = await screen.findByText('Save set')

      // Rapid clicks
      await userEvent.click(saveButton)
      await userEvent.click(saveButton)
      await userEvent.click(saveButton)

      // Wait for async operations and check call count
      await waitFor(() => {
        expect(mockHandleSaveSet).toHaveBeenCalled()
      })

      // Due to the animation and isAnimating state, rapid clicks should be debounced
      expect(mockHandleSaveSet).toHaveBeenCalledTimes(1)
    })

    it('should handle navigation during RIR display', async () => {
      // Set up RIR display
      vi.mocked(useSetScreenLogic).mockReturnValue({
        ...defaultSetScreenLogicReturn,
        showRIRPicker: true,
      } as any)

      const { unmount } = render(
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      )

      // Verify RIR is displayed
      expect(screen.getByText('How hard was that?')).toBeInTheDocument()

      // Simulate navigation (unmount)
      unmount()

      // Should not throw any errors
      expect(true).toBe(true)
    })
  })
})
