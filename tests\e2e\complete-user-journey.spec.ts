/* eslint-disable no-console */
import { test, expect } from '@playwright/test'

test.describe('Complete User Journey E2E Test', () => {
  // Use iPhone 13 viewport for mobile-first testing
  test.use({
    viewport: { width: 390, height: 844 },
    userAgent:
      'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
  })

  test('should complete full user journey from login to workout completion', async ({
    page,
  }) => {
    // TEST-005A: Complete User Journey E2E Test
    console.log('Starting complete user journey test...')

    // Step 1: Navigate to login page
    await page.goto('/')

    // Step 2: Login with real credentials
    console.log('Step 2: Logging in with test credentials...')
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.locator('input[type="password"]').fill('Dr123456')

    // Take screenshot before login (create directory if needed)
    await page.screenshot({
      path: 'tests/e2e/screenshots/01-login-form.png',
      fullPage: true,
    })

    // Click login button
    await page.getByRole('button', { name: /Login/i }).click()

    // Step 3: Verify login success screen appears and disappears
    console.log('Step 3: Verifying login success screen...')

    // Wait for success screen
    const successScreen = page.locator('[data-testid="quick-success-screen"]')
    await expect(successScreen).toBeVisible({ timeout: 5000 })

    // Verify success message
    await expect(page.getByText('Welcome back!')).toBeVisible()

    // Wait for success screen to disappear (should be 0.5s based on QuickSuccessScreen)
    await expect(successScreen).not.toBeVisible({ timeout: 2000 })

    // Step 4: Navigate to Program page and verify data loads
    console.log('Step 4: Verifying program page...')
    await expect(page).toHaveURL('/program', { timeout: 10000 })

    // Wait for loading to complete
    await page
      .waitForSelector('.animate-pulse', { state: 'hidden', timeout: 10000 })
      .catch(() => {})

    // Take screenshot of program page
    await page.screenshot({
      path: 'tests/e2e/screenshots/02-program-page.png',
      fullPage: true,
    })

    // Step 5: Verify program name, progress, and stats are displayed
    console.log('Step 5: Verifying program data...')

    // Check for program header (h1 should exist) - get the second h1 which is the program name
    const programName = page.getByRole('heading', { level: 1 }).nth(1)
    await expect(programName).toBeVisible()
    const programNameText = await programName.textContent()
    console.log(`Program name: ${programNameText}`)

    // Check for animated counter (workout count)
    const animatedCounter = page.getByTestId('animated-counter')
    await expect(animatedCounter).toBeVisible()

    // Check for stats grid (should have 2 stat cards)
    const statCards = page.locator('[data-testid="stat-card"]')
    await expect(statCards).toHaveCount(2)

    // Step 6: Verify "Continue to workout" button is visible and enabled
    console.log('Step 6: Checking Continue to Workout button...')
    const continueButton = page.getByRole('button', {
      name: /Continue to Workout/i,
    })
    await expect(continueButton).toBeVisible()
    await expect(continueButton).toBeEnabled()

    // Step 7: Click "Continue to workout" and navigate to Workout page
    console.log('Step 7: Navigating to workout page...')
    await continueButton.click()

    // Wait for navigation to workout page
    await expect(page).toHaveURL('/workout', { timeout: 10000 })

    // Step 8: Verify workout data loads
    console.log('Step 8: Verifying workout data...')

    // Wait for loading to complete
    await page
      .waitForSelector('.animate-pulse', { state: 'hidden', timeout: 10000 })
      .catch(() => {})

    // Take screenshot of workout page
    await page.screenshot({
      path: 'tests/e2e/screenshots/03-workout-page.png',
      fullPage: true,
    })

    // Check for workout name
    await expect(page.getByRole('heading', { name: /Workout/i })).toBeVisible()

    // Check for exercise list - look for h3 elements which contain exercise names
    const exerciseList = page.locator('h3')
    const exerciseCount = await exerciseList.count()
    console.log(`Found ${exerciseCount} exercise headings`)

    // If no h3 elements, try looking for any clickable exercise cards
    if (exerciseCount === 0) {
      const exerciseCards = page
        .locator('.cursor-pointer')
        .filter({ hasText: /exercise|press|curl|squat|row|fly/i })
      const cardCount = await exerciseCards.count()
      console.log(`Found ${cardCount} exercise cards`)
      expect(cardCount).toBeGreaterThan(0)
    } else {
      expect(exerciseCount).toBeGreaterThan(0)
    }

    // Step 9: Verify "Start Workout" button is visible and enabled
    console.log('Step 9: Checking Start Workout button...')
    const startButton = page.getByRole('button', { name: /Start Workout/i })
    await expect(startButton).toBeVisible()
    await expect(startButton).toBeEnabled()

    // Step 10: Click "Start Workout" to begin workout session
    console.log('Step 10: Starting workout...')
    await startButton.click()

    // Should navigate to first exercise
    await expect(page).toHaveURL(/\/workout\/exercise\/\d+/, { timeout: 10000 })

    // Step 11: Click on first exercise (if not already there)
    console.log('Step 11: Verifying exercise details screen...')

    // Take screenshot of exercise screen
    await page.screenshot({
      path: 'tests/e2e/screenshots/04-exercise-screen.png',
      fullPage: true,
    })

    // Check for exercise name - get the h2 which contains the exercise name
    await expect(page.getByRole('heading', { level: 2 })).toBeVisible()

    // Step 12: Enter random exercise data
    console.log('Step 12: Entering exercise data...')

    // Generate random values
    const randomReps = Math.floor(Math.random() * 10) + 5 // 5-14 reps
    const randomWeight = Math.floor(Math.random() * 50) + 20 // 20-69 lbs

    console.log(`Entering: ${randomReps} reps @ ${randomWeight} lbs`)

    // Find and fill reps input - look for input field with type number
    const repsInput = page.locator('input[type="number"]').first()
    await repsInput.clear()
    await repsInput.fill(randomReps.toString())

    // Find and fill weight input - look for second number input
    const weightInput = page.locator('input[type="number"]').nth(1)
    await weightInput.clear()
    await weightInput.fill(randomWeight.toString())

    // Step 13: Save the exercise data
    console.log('Step 13: Saving exercise data...')
    const saveButton = page.getByRole('button', {
      name: /Save Set|Save|Log Set/i,
    })
    await expect(saveButton).toBeEnabled()

    // Take screenshot before saving
    await page.screenshot({
      path: 'tests/e2e/screenshots/05-before-save.png',
      fullPage: true,
    })

    await saveButton.click()

    // Handle RIR picker if it appears (after first work set)
    try {
      console.log('Checking for RIR picker...')
      const rirPicker = page.locator('[data-testid="rir-picker"]')
      if (await rirPicker.isVisible({ timeout: 3000 })) {
        console.log('RIR picker appeared, selecting option...')
        // Select "Could do 1-2 more" option
        await page.getByText(/1-2 more|2-3 reps left/i).click()
      }
    } catch (e) {
      console.log('No RIR picker appeared, continuing...')
    }

    // Might navigate to rest timer
    try {
      await page.waitForURL('/workout/rest-timer', { timeout: 3000 })
      console.log('Rest timer appeared, skipping...')
      await page.getByRole('button', { name: /Skip Rest|Skip/i }).click()
    } catch (e) {
      console.log('No rest timer, continuing...')
    }

    // Step 14: Complete the workout
    console.log('Step 14: Looking for Complete Workout button...')

    // Try to find and click complete workout button
    let completeButton = page.getByRole('button', {
      name: /Complete Workout|Finish Workout|End Workout/i,
    })

    // If not found, we might need to complete more sets or exercises
    if (!(await completeButton.isVisible({ timeout: 3000 }))) {
      console.log(
        'Complete button not visible yet, checking if we need to finish exercise...'
      )

      // Check if we're still on exercise screen
      if (
        page.url().includes('/workout/exercise/') ||
        page.url().includes('/workout/exercise-v2/')
      ) {
        // Look for a finish exercise or next exercise button
        const finishExerciseButton = page.getByRole('button', {
          name: /Finish Exercise|Next Exercise|Done/i,
        })
        if (await finishExerciseButton.isVisible({ timeout: 3000 })) {
          await finishExerciseButton.click()
          await page.waitForTimeout(1000)
        }
      }

      // Now try to find complete workout button again
      completeButton = page.getByRole('button', {
        name: /Complete Workout|Finish Workout|End Workout/i,
      })
    }

    // Click complete workout if visible
    if (await completeButton.isVisible({ timeout: 5000 })) {
      await completeButton.click()
    } else {
      // If still not visible, navigate directly to complete page
      console.log('Navigating directly to workout complete page...')
      await page.goto('/workout/complete')
    }

    // Step 15: Verify workout completion page appears
    console.log('Step 15: Verifying workout completion...')
    await expect(page).toHaveURL('/workout/complete', { timeout: 10000 })

    // Take screenshot of completion page
    await page.screenshot({
      path: 'tests/e2e/screenshots/06-workout-complete.png',
      fullPage: true,
    })

    // Step 16: Verify success messages and workout summary
    console.log('Step 16: Verifying workout summary...')

    // Check for completion heading - get the first match
    await expect(
      page
        .getByRole('heading', { name: /Workout Complete|Great Job|Well Done/i })
        .first()
    ).toBeVisible()

    // Check for workout stats
    await expect(page.getByText(/Duration/i)).toBeVisible()
    await expect(page.getByText(/Sets/i).first()).toBeVisible()

    // Check for finish button - specifically look for "Finish Workout"
    const finishButton = page.getByRole('button', { name: 'Finish Workout' })
    await expect(finishButton).toBeVisible()

    // Step 17: Verify workout was saved to backend
    console.log('Step 17: Verifying data persistence...')

    // Click finish to save workout
    await finishButton.click()

    // Should redirect back to program or workout page
    await page.waitForURL(/\/(program|workout)/, { timeout: 10000 })

    // Final screenshot
    await page.screenshot({
      path: 'tests/e2e/screenshots/07-final-state.png',
      fullPage: true,
    })

    console.log('✅ Complete user journey test finished successfully!')
  })

  test('should handle network conditions and loading states', async ({
    page,
    context,
  }) => {
    console.log('Testing network conditions...')

    // Login first
    await page.goto('/')
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.locator('input[type="password"]').fill('Dr123456')
    await page.getByRole('button', { name: /Login/i }).click()

    // Wait for program page
    await page.waitForURL('/program', { timeout: 10000 })

    // Simulate slow 3G network
    console.log('Simulating slow 3G network...')
    await page.route('**/*', async (route) => {
      await new Promise((resolve) => {
        setTimeout(resolve, 1000)
      }) // 1 second delay
      await route.continue()
    })

    // Navigate to workout
    await page.getByRole('button', { name: /Continue to Workout/i }).click()

    // Should show loading states
    await expect(page.locator('.animate-pulse')).toBeVisible()

    // Clear network simulation
    await page.unroute('**/*')

    // Test offline mode
    console.log('Testing offline mode...')
    await context.setOffline(true)

    // Try to refresh
    await page.reload()

    // Should still show cached data or offline indicator
    const offlineIndicator = page.getByText(/offline|no connection/i)
    const cachedContent = page.getByRole('heading')

    // At least one should be visible
    await expect(offlineIndicator.or(cachedContent)).toBeVisible({
      timeout: 5000,
    })

    // Go back online
    await context.setOffline(false)

    console.log('✅ Network conditions test completed')
  })

  test('should handle errors gracefully', async ({ page }) => {
    console.log('Testing error handling...')

    // Mock API error responses
    await page.route('**/api/WorkoutLog/**', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' }),
      })
    })

    // Try to login and navigate
    await page.goto('/')
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.locator('input[type="password"]').fill('Dr123456')
    await page.getByRole('button', { name: /Login/i }).click()

    // Should handle error gracefully
    const errorMessage = page.getByText(/error|something went wrong|try again/i)
    const retryButton = page.getByRole('button', { name: /retry|try again/i })

    // At least one error handling element should be visible
    await expect(errorMessage.or(retryButton)).toBeVisible({ timeout: 10000 })

    console.log('✅ Error handling test completed')
  })
})
