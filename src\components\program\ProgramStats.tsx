import React from 'react'
import { SwipeableStatCard } from './SwipeableStatCard'
import type { UserStats } from '@/types'

interface ProgramStatsProps {
  stats: UserStats
  isLoadingStats: boolean
}

export function ProgramStats({ stats, isLoadingStats }: ProgramStatsProps) {
  // V2 Style: Single swipeable stat card replacing the grid layout
  return <SwipeableStatCard stats={stats} isLoading={isLoadingStats} />
}
