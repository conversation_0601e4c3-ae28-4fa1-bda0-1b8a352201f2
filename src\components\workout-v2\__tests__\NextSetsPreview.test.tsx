import { render, screen } from '@testing-library/react'
import { NextSetsPreview } from '@/components/workout-v2/NextSetsPreview'
import type { WorkoutLogSerieModel } from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'

// Extended type to include warmup properties
type ExtendedWorkoutLogSerieModel = WorkoutLogSerieModel &
  Partial<WorkoutLogSerieModelRef> & {
    WarmUpReps?: number
    WarmUpWeightSet?: { Lb: number; Kg: number }
    IsSkipped?: boolean
  }

describe('NextSetsPreview', () => {
  const mockNextSets: ExtendedWorkoutLogSerieModel[] = [
    {
      Id: 1,
      Reps: 10,
      Weight: { Kg: 50, Lb: 110 },
      IsFinished: false,
      IsNext: false,
      IsWarmups: false,
    },
    {
      Id: 2,
      Reps: 8,
      Weight: { Kg: 60, Lb: 132 },
      IsFinished: false,
      IsNext: false,
      IsWarmups: false,
    },
    {
      Id: 3,
      Reps: 6,
      Weight: { Kg: 70, Lb: 154 },
      IsFinished: false,
      IsNext: false,
      IsWarmups: false,
    },
  ]

  const mockWarmupSet: ExtendedWorkoutLogSerieModel = {
    Id: 4,
    WarmUpReps: 15,
    WarmUpWeightSet: { Kg: 20, Lb: 44 },
    IsFinished: false,
    IsNext: false,
    IsWarmups: true,
  }

  it('renders next sets preview with correct content', () => {
    render(
      <NextSetsPreview nextSets={mockNextSets} unit="kg" currentSetIndex={1} />
    )

    expect(screen.getByText('Next sets')).toBeInTheDocument()
    expect(screen.getByText('Set 3')).toBeInTheDocument()
    expect(screen.getByText('10 reps')).toBeInTheDocument()
    expect(screen.getByText('50 kg')).toBeInTheDocument()
  })

  it('displays weight in lbs when unit is lbs', () => {
    render(
      <NextSetsPreview nextSets={mockNextSets} unit="lbs" currentSetIndex={1} />
    )

    expect(screen.getByText('110 lbs')).toBeInTheDocument()
    expect(screen.getByText('132 lbs')).toBeInTheDocument()
    expect(screen.getByText('154 lbs')).toBeInTheDocument()
  })

  it('shows all sets when more than 3 are provided', () => {
    const manySets = [
      ...mockNextSets,
      {
        Id: 5,
        Reps: 4,
        Weight: { Kg: 80, Lb: 176 },
        IsFinished: false,
        IsNext: false,
        IsWarmups: false,
      },
      {
        Id: 6,
        Reps: 3,
        Weight: { Kg: 85, Lb: 187 },
        IsFinished: false,
        IsNext: false,
        IsWarmups: false,
      },
    ]

    render(
      <NextSetsPreview nextSets={manySets} unit="kg" currentSetIndex={1} />
    )

    // Should show all 5 sets
    expect(screen.getByText('10 reps')).toBeInTheDocument()
    expect(screen.getByText('8 reps')).toBeInTheDocument()
    expect(screen.getByText('6 reps')).toBeInTheDocument()
    expect(screen.getByText('4 reps')).toBeInTheDocument()
    expect(screen.getByText('3 reps')).toBeInTheDocument()

    // Should NOT show "+X more sets" indicator
    expect(screen.queryByText(/more sets/)).not.toBeInTheDocument()
  })

  it('handles warmup sets correctly', () => {
    render(
      <NextSetsPreview
        nextSets={[mockWarmupSet]}
        unit="kg"
        currentSetIndex={0}
      />
    )

    expect(screen.getByText('W1')).toBeInTheDocument()
    expect(screen.getByText('15 reps')).toBeInTheDocument()
    expect(screen.getByText('20 kg')).toBeInTheDocument()
  })

  it('displays numbered warmup sets (W1, W2, etc)', () => {
    // Test rationale: Warmup sets in preview should show numbered labels for clarity
    const multipleWarmupSets: ExtendedWorkoutLogSerieModel[] = [
      mockWarmupSet,
      {
        Id: 2,
        WarmUpReps: 12,
        WarmUpWeightSet: { Kg: 30, Lb: 66 },
        IsFinished: false,
        IsNext: false,
        IsWarmups: true,
      },
      {
        Id: 3,
        WarmUpReps: 10,
        WarmUpWeightSet: { Kg: 40, Lb: 88 },
        IsFinished: false,
        IsNext: false,
        IsWarmups: true,
      },
    ]

    render(
      <NextSetsPreview
        nextSets={multipleWarmupSets}
        unit="kg"
        currentSetIndex={0}
      />
    )

    // Should display W1, W2, W3 instead of just W
    expect(screen.getByText('W1')).toBeInTheDocument()
    expect(screen.getByText('W2')).toBeInTheDocument()
    expect(screen.getByText('W3')).toBeInTheDocument()
    // Should NOT display just 'W'
    expect(screen.queryAllByText('W')).toHaveLength(0)
  })

  it('displays single warmup set as W1', () => {
    // Test rationale: Even single warmup should show as W1 for consistency
    render(
      <NextSetsPreview
        nextSets={[mockWarmupSet]}
        unit="kg"
        currentSetIndex={0}
      />
    )

    // Should display W1 instead of just W
    expect(screen.getByText('W1')).toBeInTheDocument()
    expect(screen.queryByText('W')).not.toBeInTheDocument()
  })

  it('renders nothing when no next sets are provided', () => {
    const { container } = render(
      <NextSetsPreview nextSets={[]} unit="kg" currentSetIndex={3} />
    )

    expect(container.firstChild).toBeNull()
  })

  it('shows correct set numbers based on current index', () => {
    render(
      <NextSetsPreview nextSets={mockNextSets} unit="kg" currentSetIndex={5} />
    )

    expect(screen.getByText('Set 7')).toBeInTheDocument()
    expect(screen.getByText('Set 8')).toBeInTheDocument()
    expect(screen.getByText('Set 9')).toBeInTheDocument()
  })

  it('handles sets without weight data', () => {
    const setsWithoutWeight: ExtendedWorkoutLogSerieModel[] = [
      {
        Id: 1,
        Reps: 10,
        Weight: undefined,
        IsFinished: false,
        IsNext: false,
        IsWarmups: false,
      },
    ]

    render(
      <NextSetsPreview
        nextSets={setsWithoutWeight}
        unit="kg"
        currentSetIndex={0}
      />
    )

    expect(screen.getByText('10 reps')).toBeInTheDocument()
    expect(screen.getByText('0 kg')).toBeInTheDocument()
  })

  it('handles sets without reps data', () => {
    const setsWithoutReps: ExtendedWorkoutLogSerieModel[] = [
      {
        Id: 1,
        Reps: undefined,
        Weight: { Kg: 50, Lb: 110 },
        IsFinished: false,
        IsNext: false,
        IsWarmups: false,
      },
    ]

    render(
      <NextSetsPreview
        nextSets={setsWithoutReps}
        unit="kg"
        currentSetIndex={0}
      />
    )

    expect(screen.getByText('0 reps')).toBeInTheDocument()
    expect(screen.getByText('50 kg')).toBeInTheDocument()
  })

  it('shows all sets even when there are many (10+)', () => {
    const manySets: ExtendedWorkoutLogSerieModel[] = Array.from(
      { length: 10 },
      (_, i) => ({
        Id: i + 1,
        Reps: 10 - i,
        Weight: { Kg: 50 + i * 5, Lb: 110 + i * 11 },
        IsFinished: false,
        IsNext: false,
        IsWarmups: false,
      })
    )

    render(
      <NextSetsPreview nextSets={manySets} unit="kg" currentSetIndex={0} />
    )

    // Should show all 10 sets
    manySets.forEach((set, index) => {
      expect(screen.getByText(`Set ${index + 2}`)).toBeInTheDocument()
      expect(screen.getByText(`${set.Reps} reps`)).toBeInTheDocument()
    })

    // Should NOT show "+X more sets" indicator
    expect(screen.queryByText(/more sets/)).not.toBeInTheDocument()
  })

  it('renders without scrollable container', () => {
    const manySets: ExtendedWorkoutLogSerieModel[] = Array.from(
      { length: 8 },
      (_, i) => ({
        Id: i + 1,
        Reps: 10,
        Weight: { Kg: 50, Lb: 110 },
        IsFinished: false,
        IsNext: false,
        IsWarmups: false,
      })
    )

    const { container } = render(
      <NextSetsPreview nextSets={manySets} unit="kg" currentSetIndex={0} />
    )

    // Check that there's no scrollable container
    const scrollContainer = container.querySelector('.overflow-y-auto')
    expect(scrollContainer).not.toBeInTheDocument()

    // Verify compact spacing still exists
    const spacingContainer = container.querySelector('.space-y-1')
    expect(spacingContainer).toBeInTheDocument()
  })
})
