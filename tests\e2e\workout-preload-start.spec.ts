import { test, expect } from '@playwright/test'

test.describe('Workout Start Preload', () => {
  test.beforeEach(async ({ page }) => {
    // Login first
    await page.goto('/login')
    await page.fill('[name="username"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')
    await page.click('[type="submit"]')
    await page.waitForURL('/program')
  })

  test('should preload exercise recommendations when starting workout', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')

    // Wait for workout to load
    await expect(
      page.locator('[data-testid="workout-overview-container"]')
    ).toBeVisible()

    // Set up request monitoring
    const recommendationCalls: string[] = []
    page.on('request', (request) => {
      const url = request.url()
      if (url.includes('/GetRecommendation')) {
        recommendationCalls.push(url)
      }
    })

    // Click Start Workout button
    const startButton = page.locator('button:has-text("Start Workout")')
    await expect(startButton).toBeVisible()

    // Start workout
    await startButton.click()

    // Should show loading state on the button
    await expect(startButton).toBeDisabled()

    // Wait for navigation to exercise page
    await page.waitForURL(/\/workout\/exercise(-v2)?\/\d+/, { timeout: 10000 })

    // Verify recommendations were loaded before navigation
    expect(recommendationCalls.length).toBeGreaterThan(0)

    // Exercise page should load with data immediately visible
    await expect(page.locator('[data-testid="exercise-name"]')).toBeVisible({
      timeout: 1000,
    })

    // Should not show loading skeleton on exercise page
    await expect(
      page.locator('[data-testid="exercise-skeleton"]')
    ).not.toBeVisible()
  })

  test('should navigate to first exercise immediately without blank screen', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')

    // Wait for workout to load
    await expect(
      page.locator('[data-testid="workout-overview-container"]')
    ).toBeVisible()

    // Get the first exercise card
    const firstExercise = page.locator('[data-testid="exercise-card"]').first()
    await expect(firstExercise).toBeVisible()

    // Click Start Workout
    await page.locator('button:has-text("Start Workout")').click()

    // Should navigate to exercise page
    await page.waitForURL(/\/workout\/exercise(-v2)?\/\d+/)

    // Exercise content should be visible immediately (no blank screen)
    await expect(page.locator('[data-testid="exercise-name"]')).toBeVisible({
      timeout: 1000,
    })

    // Sets recommendation should be visible
    await expect(page.locator('text=/\\d+ sets?/')).toBeVisible({
      timeout: 2000,
    })
  })

  test('should handle slow recommendation loading gracefully', async ({
    page,
  }) => {
    // Slow down the network to simulate slow API
    await page.route('**/GetRecommendation**', async (route) => {
      await new Promise((resolve) => setTimeout(resolve, 3000))
      await route.continue()
    })

    // Navigate to workout page
    await page.goto('/workout')

    // Wait for workout to load
    await expect(
      page.locator('[data-testid="workout-overview-container"]')
    ).toBeVisible()

    // Click Start Workout
    const startButton = page.locator('button:has-text("Start Workout")')
    await startButton.click()

    // Button should show loading state
    await expect(startButton).toBeDisabled()

    // Should eventually navigate to exercise page
    await page.waitForURL(/\/workout\/exercise(-v2)?\/\d+/, { timeout: 15000 })

    // Exercise page should handle loading gracefully
    const exerciseName = page.locator('[data-testid="exercise-name"]')
    await expect(exerciseName).toBeVisible()
  })
})
