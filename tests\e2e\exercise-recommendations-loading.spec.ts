import { test, expect } from '@playwright/test'
import { login, startWorkout } from './helpers/auth'

test.describe('Exercise Recommendations Loading UX', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
    await page.goto('/workout')
    await startWorkout(page)
  })

  test('should show skeleton loader with transition message when loading recommendations', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Click on the first exercise
    const firstExercise = page
      .getByRole('button', { name: /exercise/i })
      .first()
    const exerciseName = await firstExercise.textContent()
    await firstExercise.click()

    // Check for skeleton loader and transition message
    // The loading state should appear briefly while recommendations load
    const loadingContainer = page.locator('.min-h-\\[100dvh\\].bg-bg-primary')

    // Check for transition message
    const transitionMessage = loadingContainer.locator(
      'text=Preparing your recommendations...'
    )

    // Check for skeleton elements (using the animate-pulse class)
    const skeletonElements = loadingContainer.locator('.animate-pulse')

    // Verify exercise name is shown during loading
    const exerciseNameElement = loadingContainer.locator(`text=${exerciseName}`)

    // Since loading is quick, we check if these elements exist at any point
    // We use a more lenient approach due to the fast loading
    await expect(async () => {
      const hasTransitionMessage = await transitionMessage
        .isVisible()
        .catch(() => false)
      const hasSkeletons = (await skeletonElements.count()) > 0
      const hasExerciseName = await exerciseNameElement
        .isVisible()
        .catch(() => false)

      // At least one of these should be true during the loading phase
      expect(
        hasTransitionMessage || hasSkeletons || hasExerciseName
      ).toBeTruthy()
    }).toPass({ timeout: 10000 })

    // Wait for the actual exercise page to load
    await page.waitForSelector(
      '[data-testid="exercise-page-container"], .exercise-page',
      {
        timeout: 10000,
      }
    )

    // Verify we're on the exercise page after loading
    await expect(
      page.locator('text=Save set').or(page.locator('text=Save'))
    ).toBeVisible({
      timeout: 5000,
    })
  })

  test('should show skeleton loader when navigating between exercises', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Click on the first exercise
    const firstExercise = page
      .getByRole('button', { name: /exercise/i })
      .first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForSelector(
      '[data-testid="exercise-page-container"], .exercise-page',
      {
        timeout: 10000,
      }
    )

    // Navigate to next exercise using navigation or by going back and selecting another
    await page.goBack()
    await page.waitForLoadState('networkidle')

    // Click on second exercise
    const secondExercise = page
      .getByRole('button', { name: /exercise/i })
      .nth(1)
    const secondExerciseName = await secondExercise.textContent()
    await secondExercise.click()

    // Check for loading state elements
    const loadingContainer = page.locator('.min-h-\\[100dvh\\].bg-bg-primary')

    // Since loading is quick, check if any loading elements appear
    await expect(async () => {
      const skeletonCount = await loadingContainer
        .locator('.animate-pulse')
        .count()
      const hasExerciseName = await loadingContainer
        .locator(`text=${secondExerciseName}`)
        .isVisible()
        .catch(() => false)

      // Should show some loading indication
      expect(skeletonCount > 0 || hasExerciseName).toBeTruthy()
    }).toPass({ timeout: 10000 })

    // Verify exercise page loads
    await expect(
      page.locator('text=Save set').or(page.locator('text=Save'))
    ).toBeVisible({
      timeout: 5000,
    })
  })
})
