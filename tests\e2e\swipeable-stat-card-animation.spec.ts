import { test, expect } from '@playwright/test'

test.describe('SwipeableStatCard Animation', () => {
  test('counter animates from 0 during loading on program page', async ({
    page,
  }) => {
    // Login first
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for login to complete
    await page.waitForURL('/program', { timeout: 10000 })

    // Check initial state - should show 0 if loading
    const statValue = page.locator('[data-testid="stat-value"]').first()

    // If it's loading, verify animation
    const loadingMessage = page.locator('text=Loading your workout...')
    const isLoading = await loadingMessage.isVisible().catch(() => false)

    if (isLoading) {
      // Get initial value
      const initialText = await statValue.textContent()
      const initialValue = parseInt(initialText?.replace(/,/g, '') || '0')

      console.log('Initial value:', initialValue)

      // Wait a bit for animation
      await page.waitForTimeout(1000)

      // Check value has increased
      const animatedText = await statValue.textContent()
      const animatedValue = parseInt(animatedText?.replace(/,/g, '') || '0')

      console.log('Animated value after 1s:', animatedValue)

      // Value should increase during animation
      expect(animatedValue).toBeGreaterThanOrEqual(initialValue)
    } else {
      // If not loading, just verify the stat displays correctly
      const value = await statValue.textContent()
      expect(value).toBeTruthy()
      console.log('Stats already loaded, value:', value)
    }
  })

  test('swipeable navigation works between stats', async ({ page }) => {
    // Assume already logged in from previous test or use existing session
    await page.goto('/program')

    // Wait for stats to load
    await page.waitForSelector('[data-testid="swipeable-stat-card"]')

    // Check we're on first stat (Week Streak)
    await expect(page.locator('text=Week Streak')).toBeVisible()

    // Click second indicator
    const indicators = page.locator('[data-testid="stat-indicator"]')
    await indicators.nth(1).click()

    // Should show Workouts
    await expect(page.locator('text=Workouts').first()).toBeVisible()

    // Click third indicator
    await indicators.nth(2).click()

    // Should show Lbs Lifted
    await expect(page.locator('text=Lbs Lifted')).toBeVisible()
  })
})
