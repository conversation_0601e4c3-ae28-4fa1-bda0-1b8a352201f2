import React from 'react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useLoginPrefetch } from '../useLoginPrefetch'

import { getUserWorkoutProgramInfo } from '@/services/api/workout'
import { workoutApi } from '@/api/workouts'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock the prefetch hook
vi.mock('../useUserStats', () => ({
  usePrefetchUserStats: vi.fn(() => ({
    prefetch: vi.fn(),
  })),
}))

// Mock workout API
vi.mock('@/services/api/workout', () => ({
  getUserWorkoutProgramInfo: vi.fn(),
}))

vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserWorkout: vi.fn(),
  },
}))

// Mock workout store
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(),
}))

describe('useLoginPrefetch - Exercise recommendations loading', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })

    // Set up default mocks
    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    })
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([
      { Id: 123, Exercises: [] },
    ])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: vi.fn(),
      setWorkout: vi.fn(),
      exercises: [],
    } as any)
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  it('should load exercise recommendations after workout data is loaded', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    }
    const mockWorkout = {
      Id: 123,
      Exercises: [
        { Id: 1, Name: 'Exercise 1' },
        { Id: 2, Name: 'Exercise 2' },
        { Id: 3, Name: 'Exercise 3' },
      ],
    }
    const mockLoadAllExerciseRecommendations = vi.fn()

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      setWorkout: vi.fn(),
      exercises: mockWorkout.Exercises,
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow promises to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that recommendations were loaded
    expect(mockLoadAllExerciseRecommendations).toHaveBeenCalledTimes(1)
  })

  it('should not load recommendations if no exercises in workout', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    }
    const mockWorkout = {
      Id: 123,
      Exercises: [], // No exercises
    }
    const mockLoadAllExerciseRecommendations = vi.fn()

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      setWorkout: vi.fn(),
      exercises: [],
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow promises to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that recommendations were NOT loaded
    expect(mockLoadAllExerciseRecommendations).not.toHaveBeenCalled()
  })

  it('should continue loading even if recommendations fail', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    }
    const mockWorkout = {
      Id: 123,
      Exercises: [{ Id: 1, Name: 'Exercise 1' }],
    }
    const mockLoadAllExerciseRecommendations = vi
      .fn()
      .mockRejectedValue(new Error('Recommendations failed'))

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      setWorkout: vi.fn(),
      exercises: mockWorkout.Exercises,
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow promises to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that the prefetch completes despite recommendation failure
    expect(result.current.isComplete).toBe(true)
    expect(result.current.error).toBeNull()
  })

  it('should set workout in store before loading recommendations', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    }
    const mockWorkout = {
      Id: 123,
      Exercises: [{ Id: 1, Name: 'Exercise 1' }],
    }
    const mockSetWorkout = vi.fn()
    const mockLoadAllExerciseRecommendations = vi.fn()

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      setWorkout: mockSetWorkout,
      exercises: mockWorkout.Exercises,
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow promises to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that setWorkout was called before loadAllExerciseRecommendations
    expect(mockSetWorkout).toHaveBeenCalledWith(mockWorkout)
    expect(mockSetWorkout).toHaveBeenCalledBefore(
      mockLoadAllExerciseRecommendations as any
    )
  })

  it('should NOT call setWorkout if workout already exists in store', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 123 },
      },
    }
    const existingWorkout = {
      Id: 123,
      Exercises: [{ Id: 1, Name: 'Exercise 1' }],
    }
    const mockWorkout = {
      Id: 123,
      Exercises: [{ Id: 1, Name: 'Exercise 1' }],
    }
    const mockSetWorkout = vi.fn()
    const mockLoadAllExerciseRecommendations = vi.fn()

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      setWorkout: mockSetWorkout,
      exercises: existingWorkout.Exercises,
      currentWorkout: existingWorkout, // Workout already exists
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow promises to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that setWorkout was NOT called since workout already exists
    expect(mockSetWorkout).not.toHaveBeenCalled()
    // But recommendations should still be loaded
    expect(mockLoadAllExerciseRecommendations).toHaveBeenCalledTimes(1)
  })

  it('should call setWorkout if existing workout has different ID', async () => {
    // Mock the responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 456 }, // Different ID
      },
    }
    const existingWorkout = {
      Id: 123, // Old workout ID
      Exercises: [{ Id: 1, Name: 'Exercise 1' }],
    }
    const mockWorkout = {
      Id: 456, // New workout ID
      Exercises: [{ Id: 2, Name: 'Exercise 2' }],
    }
    const mockSetWorkout = vi.fn()
    const mockLoadAllExerciseRecommendations = vi.fn()

    vi.mocked(getUserWorkoutProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      setWorkout: mockSetWorkout,
      exercises: existingWorkout.Exercises,
      currentWorkout: existingWorkout,
    } as any)

    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Allow promises to resolve
      await vi.runAllTimersAsync()
    })

    // Verify that setWorkout WAS called since it's a different workout
    expect(mockSetWorkout).toHaveBeenCalledWith(mockWorkout)
    expect(mockLoadAllExerciseRecommendations).toHaveBeenCalledTimes(1)
  })
})
