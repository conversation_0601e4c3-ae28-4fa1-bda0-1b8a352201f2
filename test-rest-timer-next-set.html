<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rest Timer Next Set Info Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .rest-timer-mockup {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #1a1a1a;
            color: white;
            padding: 16px;
            border-top: 1px solid #333;
        }
        .timer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .timer-title {
            font-size: 18px;
            font-weight: 600;
        }
        .timer-value {
            font-size: 32px;
            font-weight: bold;
            color: #4CAF50;
        }
        .skip-button {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: black;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 500;
            cursor: pointer;
        }
        .progress-bar {
            height: 8px;
            background: #333;
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }
        .progress-fill {
            height: 100%;
            background: #4CAF50;
            width: 60%;
            transition: width 0.3s;
        }
        .next-set-info {
            text-align: center;
            font-size: 14px;
            color: #999;
            margin-top: 8px;
        }
        .scenario {
            margin-bottom: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .scenario h3 {
            margin-top: 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Rest Timer Next Set Info Test</h1>
        <p>This page demonstrates the different display formats for the rest timer's next set information.</p>
        
        <div class="scenario">
            <h3>Scenario 1: Regular Set (With Weight)</h3>
            <p>Next set: 8 reps x 135 lbs</p>
            <button onclick="showScenario1()">Show Timer</button>
        </div>
        
        <div class="scenario">
            <h3>Scenario 2: Bodyweight Exercise</h3>
            <p>Next set: 15 reps (no weight)</p>
            <button onclick="showScenario2()">Show Timer</button>
        </div>
        
        <div class="scenario">
            <h3>Scenario 3: Metric Units (kg)</h3>
            <p>Next set: 10 reps x 60 kg</p>
            <button onclick="showScenario3()">Show Timer</button>
        </div>
        
        <div class="scenario">
            <h3>Scenario 4: No Next Set Info</h3>
            <p>Generic message (last set or no data)</p>
            <button onclick="showScenario4()">Show Timer</button>
        </div>
    </div>
    
    <div id="restTimer" class="rest-timer-mockup" style="display: none;">
        <div class="timer-header">
            <div>
                <div class="timer-title">Rest Timer</div>
                <div class="timer-value">1:30</div>
            </div>
            <button class="skip-button">Skip</button>
        </div>
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
        <div id="nextSetInfo" class="next-set-info">
            Get ready for your next set!
        </div>
    </div>
    
    <script>
        function showTimer(message) {
            const timer = document.getElementById('restTimer');
            const info = document.getElementById('nextSetInfo');
            info.textContent = message;
            timer.style.display = 'block';
            setTimeout(() => {
                timer.style.display = 'none';
            }, 5000);
        }
        
        function showScenario1() {
            showTimer('Get ready for: 8 x 135 lbs');
        }
        
        function showScenario2() {
            showTimer('Get ready for: 15 reps');
        }
        
        function showScenario3() {
            showTimer('Get ready for: 10 x 60 kg');
        }
        
        function showScenario4() {
            showTimer('Get ready for your next set!');
        }
    </script>
</body>
</html>