import { renderHook, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useWorkout } from '../useWorkout'
import { getExerciseRecommendation } from '@/services/api/workout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import { getUserSettings } from '@/services/userSettings'
import type { RecommendationModel } from '@/types'

// Mock dependencies
vi.mock('@/services/api/workout')
vi.mock('@/services/userSettings')
vi.mock('@/utils/logger', () => ({
  logger: {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
  },
}))

// Mock auth utils
vi.mock('@/lib/auth-utils', () => ({
  getCurrentUserEmail: vi.fn(() => '<EMAIL>'),
}))

// Create wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return function ({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }
}

describe('useWorkout - getRecommendation', () => {
  const mockUserEmail = '<EMAIL>'
  const mockWorkoutId = 123
  const mockExerciseId = 27474

  beforeEach(() => {
    vi.clearAllMocks()

    // Reset stores
    useAuthStore.setState({
      user: { email: mockUserEmail },
    })

    // Get initial state
    const initialState = useWorkoutStore.getState()

    useWorkoutStore.setState({
      ...initialState,
      currentWorkout: {
        Id: mockWorkoutId,
        Label: 'Test Workout',
        Exercises: [
          {
            Id: mockExerciseId,
            Label: 'Test Exercise',
            SetStyle: 'Normal',
            IsFlexibility: false,
          },
        ],
      },
      cachedData: {
        ...initialState.cachedData,
        exerciseRecommendations: {}, // Start with no cached data
        lastUpdated: {
          ...initialState.cachedData.lastUpdated,
          exerciseRecommendations: {},
        },
      },
      exerciseRecommendations: new Map(), // Empty Map
      hasHydrated: true,
    })

    // Mock user settings
    vi.mocked(getUserSettings).mockResolvedValue({
      isQuickMode: false,
      isStrengthPhase: false,
      isFreePlan: false,
      isFirstWorkoutOfStrengthPhase: false,
      lightSessionDays: 0,
    })
  })

  it('should fetch and return fresh data from API when no cache exists', async () => {
    // Mock API to return fresh data
    const freshRecommendation: RecommendationModel = {
      Reps: 12,
      Series: 3,
      Weight: { Kg: 20, Lb: 44 },
    }
    vi.mocked(getExerciseRecommendation).mockResolvedValue(freshRecommendation)

    const { result } = renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    // Call getRecommendation
    const recommendation =
      await result.current.getRecommendation(mockExerciseId)

    // Should return fresh data, not cached
    expect(recommendation).toEqual({
      Reps: 12,
      Series: 3,
      Weight: { Kg: 20, Lb: 44 },
    })

    // Should have called the API
    await waitFor(() => {
      expect(getExerciseRecommendation).toHaveBeenCalledWith({
        Username: mockUserEmail,
        ExerciseId: mockExerciseId,
        WorkoutId: mockWorkoutId,
        SetStyle: 'Normal',
        IsFlexibility: false,
        IsQuickMode: false,
        LightSessionDays: 0,
        SwapedExId: undefined,
        IsStrengthPhashe: false,
        IsFreePlan: false,
        IsFirstWorkoutOfStrengthPhase: false,
        VersionNo: 1,
      })
    })

    // Cache should be updated with fresh data
    await waitFor(() => {
      const state = useWorkoutStore.getState()
      expect(state.cachedData.exerciseRecommendations[mockExerciseId]).toEqual({
        Reps: 12,
        Series: 3,
        Weight: { Kg: 20, Lb: 44 },
      })
    })
  })

  it('should call the API when getRecommendation is invoked', async () => {
    // Mock API to return fresh data
    const freshRecommendation: RecommendationModel = {
      Reps: 12,
      Series: 3,
      Weight: { Kg: 20, Lb: 44 },
    }
    vi.mocked(getExerciseRecommendation).mockResolvedValue(freshRecommendation)

    const { result } = renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    // Call getRecommendation
    await result.current.getRecommendation(mockExerciseId)

    // Should have called the API
    await waitFor(() => {
      expect(getExerciseRecommendation).toHaveBeenCalled()
    })
  })

  it('should handle null recommendation from API', async () => {
    // Mock API to return null
    vi.mocked(getExerciseRecommendation).mockResolvedValue(null)

    const { result } = renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    // Call getRecommendation
    const recommendation =
      await result.current.getRecommendation(mockExerciseId)

    // Should return null
    expect(recommendation).toBeNull()

    // Cache should be updated with null
    await waitFor(() => {
      const state = useWorkoutStore.getState()
      expect(
        state.cachedData.exerciseRecommendations[mockExerciseId]
      ).toBeNull()
    })
  })

  it('should handle API errors gracefully', async () => {
    // Mock API to throw error
    vi.mocked(getExerciseRecommendation).mockRejectedValue(
      new Error('API Error')
    )

    const { result } = renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    // Call getRecommendation
    const recommendation =
      await result.current.getRecommendation(mockExerciseId)

    // Should return null on error
    expect(recommendation).toBeNull()
  })
})
