import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import React from 'react'
import { ExerciseCard } from '../ExerciseCard'
import type { ExerciseWorkSetsModel } from '@/types'

describe('ExerciseCard', () => {
  const mockExercise: ExerciseWorkSetsModel = {
    Id: 1,
    Label: 'Bench Press',
    BodyPartId: 2,
    IsFinished: false,
    IsNextExercise: false,
    isLoadingSets: false,
    setsError: null,
    lastSetsUpdate: Date.now(),
    sets: [
      {
        ExerciseId: 1,
        Weight: { Lb: 100, Kg: 45.36 },
        Reps: 10,
        IsWarmups: false,
        IsNext: false,
        IsFinished: true,
      },
      {
        ExerciseId: 1,
        Weight: { Lb: 110, Kg: 49.9 },
        Reps: 8,
        IsWarmups: false,
        IsNext: true,
        IsFinished: false,
      },
    ],
  }

  const mockHandlers = {
    onExerciseClick: vi.fn(),
    onRetry: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('should render exercise name and info', () => {
      render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

      expect(screen.getByText('Bench Press')).toBeInTheDocument()
      expect(screen.getByText('2 sets')).toBeInTheDocument()
    })

    it('should show bodyweight indicator', () => {
      const bodyweightExercise = {
        ...mockExercise,
        IsBodyweight: true,
      }

      render(<ExerciseCard exercise={bodyweightExercise} {...mockHandlers} />)
      expect(screen.getByText('• Bodyweight')).toBeInTheDocument()
    })

    it('should show next exercise indicator', () => {
      const nextExercise = {
        ...mockExercise,
        IsNextExercise: true,
      }

      render(<ExerciseCard exercise={nextExercise} {...mockHandlers} />)
      expect(screen.getByText('• Next')).toBeInTheDocument()
    })

    it('should show completed indicator', () => {
      const completedExercise = {
        ...mockExercise,
        IsFinished: true,
      }

      render(<ExerciseCard exercise={completedExercise} {...mockHandlers} />)
      expect(screen.getByText('✓')).toBeInTheDocument()
    })
  })

  describe('Loading States', () => {
    it('should show skeleton when loading with no sets', () => {
      const loadingExercise = {
        ...mockExercise,
        isLoadingSets: true,
        sets: [],
      }

      const { container } = render(
        <ExerciseCard exercise={loadingExercise} {...mockHandlers} />
      )

      expect(container.querySelector('.animate-pulse')).toBeInTheDocument()
    })

    it('should show loading text when loading with existing sets', () => {
      const loadingExercise = {
        ...mockExercise,
        isLoadingSets: true,
      }

      render(<ExerciseCard exercise={loadingExercise} {...mockHandlers} />)

      // Card should still render with exercise info
      expect(screen.getByText('Bench Press')).toBeInTheDocument()
    })
  })

  describe('Error States', () => {
    it('should display error message', () => {
      const errorExercise = {
        ...mockExercise,
        setsError: 'Failed to load sets',
      }

      render(<ExerciseCard exercise={errorExercise} {...mockHandlers} />)

      expect(screen.getByText('Failed to load sets')).toBeInTheDocument()
      expect(screen.getByText('Retry')).toBeInTheDocument()
    })

    it('should call onRetry when retry button clicked', () => {
      const errorExercise = {
        ...mockExercise,
        setsError: 'Network error',
      }

      render(<ExerciseCard exercise={errorExercise} {...mockHandlers} />)

      fireEvent.click(screen.getByText('Retry'))
      expect(mockHandlers.onRetry).toHaveBeenCalledWith(1)
    })
  })

  describe('Interactions', () => {
    it('should call onExerciseClick when card clicked', () => {
      render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

      fireEvent.click(
        screen.getByRole('button', { name: /Exercise: Bench Press/i })
      )
      expect(mockHandlers.onExerciseClick).toHaveBeenCalledWith(1)
    })

    it('should toggle set expansion', () => {
      render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

      // Sets should not be visible initially
      expect(screen.queryByText(/100.*×.*10/)).not.toBeInTheDocument()

      // Click expand button
      fireEvent.click(screen.getByLabelText('Expand sets'))

      // Sets should now be visible using more flexible text matching
      const setButtons = screen.getAllByRole('button', { name: /Set \d+:/ })
      expect(setButtons).toHaveLength(2)
      expect(setButtons[0]).toHaveTextContent('100 lbs × 10')
      expect(setButtons[1]).toHaveTextContent('110 lbs × 8')
    })

    it('should auto-expand for current exercise', () => {
      render(
        <ExerciseCard
          exercise={mockExercise}
          {...mockHandlers}
          isCurrentExercise
        />
      )

      // Sets should be visible immediately
      const setButtons = screen.getAllByRole('button', { name: /Set \d+:/ })
      expect(setButtons).toHaveLength(2)
      expect(setButtons[0]).toHaveTextContent('100 lbs × 10')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

      const card = screen.getByRole('button', {
        name: /Exercise: Bench Press/i,
      })
      expect(card).toHaveAttribute('aria-expanded', 'false')

      // Expand sets
      fireEvent.click(screen.getByLabelText('Expand sets'))
      expect(card).toHaveAttribute('aria-expanded', 'true')
    })

    it('should be keyboard accessible', () => {
      render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

      const card = screen.getByRole('button', {
        name: /Exercise: Bench Press/i,
      })
      expect(card).toHaveAttribute('tabIndex', '0')
    })
  })

  describe('Overflow Menu Integration', () => {
    it('should render overflow menu button', () => {
      render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

      const menuButton = screen.getByLabelText('Exercise options')
      expect(menuButton).toBeInTheDocument()
    })

    it('should open overflow menu on click', () => {
      render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

      const menuButton = screen.getByLabelText('Exercise options')
      fireEvent.click(menuButton)

      expect(screen.getByRole('menu')).toBeInTheDocument()
    })

    it('should not propagate menu button clicks to card', () => {
      render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

      const menuButton = screen.getByLabelText('Exercise options')
      fireEvent.click(menuButton)

      expect(mockHandlers.onExerciseClick).not.toHaveBeenCalled()
    })

    it('should handle skip exercise from overflow menu', () => {
      render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

      // Open menu
      fireEvent.click(screen.getByLabelText('Exercise options'))

      // Click skip
      fireEvent.click(screen.getByText('Skip Exercise'))

      // Should trigger appropriate handler
      // This will be implemented when we add the handler to ExerciseCard
      expect(mockHandlers.onExerciseClick).not.toHaveBeenCalled()
    })
  })

  describe('Theme Styling', () => {
    it('should use theme-aware background and text colors', () => {
      const { container } = render(
        <ExerciseCard exercise={mockExercise} {...mockHandlers} />
      )

      const card = container.querySelector('[data-testid="exercise-card"]')
      expect(card).toHaveClass('bg-bg-secondary')

      const heading = screen.getByText('Bench Press')
      expect(heading).toHaveClass('text-text-primary')

      const stats = screen.getByText('2 sets').parentElement
      expect(stats).toHaveClass('text-text-secondary')
    })

    it('should use theme-aware shadow classes', () => {
      const { container } = render(
        <ExerciseCard exercise={mockExercise} {...mockHandlers} />
      )

      const card = container.querySelector('[data-testid="exercise-card"]')
      expect(card).toHaveClass('shadow-theme-md')
    })

    it('should use theme colors for interactive elements', () => {
      render(<ExerciseCard exercise={mockExercise} {...mockHandlers} />)

      const expandButton = screen.getByLabelText('Expand sets')
      expect(expandButton).toHaveClass('text-text-tertiary')
      expect(expandButton).toHaveClass('hover:text-text-secondary')
    })

    it('should use theme colors for error state', () => {
      const errorExercise = {
        ...mockExercise,
        setsError: 'Failed to load sets',
      }

      render(<ExerciseCard exercise={errorExercise} {...mockHandlers} />)

      const errorContainer = screen.getByText('Failed to load sets')
        .parentElement?.parentElement
      expect(errorContainer).toHaveClass('bg-red-50')

      const errorText = screen.getByText('Failed to load sets')
      expect(errorText).toHaveClass('text-red-600')
    })
  })

  describe('Status Indicators', () => {
    it('should show empty circle for not started exercise', () => {
      const notStartedExercise = {
        ...mockExercise,
        IsFinished: false,
        IsInProgress: false,
      }

      render(<ExerciseCard exercise={notStartedExercise} {...mockHandlers} />)

      expect(screen.getByText('○')).toBeInTheDocument()
    })

    it('should show yellow dot for in progress exercise', () => {
      const inProgressExercise = {
        ...mockExercise,
        IsFinished: false,
        IsInProgress: true,
      }

      render(<ExerciseCard exercise={inProgressExercise} {...mockHandlers} />)

      const yellowDot = screen.getByText('●')
      expect(yellowDot).toBeInTheDocument()
      expect(yellowDot).toHaveClass('text-yellow-500')
    })

    it('should show green dot and checkmark when exercise is completed', () => {
      const finishedExercise = {
        ...mockExercise,
        IsFinished: true,
      }

      render(<ExerciseCard exercise={finishedExercise} {...mockHandlers} />)

      // Should show green filled dot
      const dots = screen.getAllByText('●')
      expect(dots[0]).toHaveClass('text-brand-primary')

      // Should also show checkmark
      const checkmark = screen.getByText('✓')
      expect(checkmark).toBeInTheDocument()
      expect(checkmark).toHaveClass('text-brand-primary')
    })

    it('should not show checkmark when exercise is not finished', () => {
      const inProgressExercise = {
        ...mockExercise,
        IsFinished: false,
      }

      render(<ExerciseCard exercise={inProgressExercise} {...mockHandlers} />)

      expect(screen.queryByText('✓')).not.toBeInTheDocument()
    })

    it('should update opacity when exercise is finished', () => {
      const finishedExercise = {
        ...mockExercise,
        IsFinished: true,
      }

      const { container } = render(
        <ExerciseCard exercise={finishedExercise} {...mockHandlers} />
      )

      const card = container.querySelector('[data-testid="exercise-card"]')
      expect(card).toHaveClass('opacity-60')
    })
  })
})
