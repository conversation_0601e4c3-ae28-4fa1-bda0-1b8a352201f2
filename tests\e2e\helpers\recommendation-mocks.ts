import { Page } from '@playwright/test'
import type { RecommendationModel, WorkoutLogSerieModel } from '@/types'

export async function mockRecommendations(
  page: Page,
  exerciseId: number,
  recommendation: Partial<RecommendationModel>
) {
  await page.route(
    `**/api/Exercise/GetRecommendationForExercise*`,
    async (route) => {
      const url = new URL(route.request().url())
      const params = Object.fromEntries(url.searchParams)

      if (params.ExerciceId === exerciseId.toString()) {
        const mockRecommendation: RecommendationModel = {
          FirstWorkSetWeight: recommendation.FirstWorkSetWeight || {
            Lb: 135,
            Kg: 61.2,
          },
          FirstWorkSetReps: recommendation.FirstWorkSetReps || 10,
          FirstWorkSet1RM: recommendation.FirstWorkSet1RM || {
            Lb: 180,
            Kg: 81.6,
          },
          MinReps: recommendation.MinReps || 8,
          MaxReps: recommendation.MaxReps || 12,
          WarmUpWeightSet1: recommendation.WarmUpWeightSet1 || {
            Lb: 95,
            Kg: 43.1,
          },
          WarmUpWeightSet2: recommendation.WarmUpWeightSet2 || {
            Lb: 115,
            Kg: 52.2,
          },
          IsBodyweight: recommendation.IsBodyweight || false,
          Series: recommendation.Series || 3,
          Reps: recommendation.Reps || 10,
          Weight: recommendation.Weight || { Lb: 135, Kg: 61.2 },
          OneRMProgress: recommendation.OneRMProgress || 0,
          RecommendationInKg: recommendation.RecommendationInKg || 61.2,
          OneRMPercentage: recommendation.OneRMPercentage || 75,
          WarmUpReps1: recommendation.WarmUpReps1 || 5,
          WarmUpReps2: recommendation.WarmUpReps2 || 3,
          WarmUpsList: recommendation.WarmUpsList || [],
          WarmupsCount: recommendation.WarmupsCount || 2,
          RpRest: recommendation.RpRest || 0,
          NbPauses: recommendation.NbPauses || 0,
          NbRepsPauses: recommendation.NbRepsPauses || 0,
          IsEasy: recommendation.IsEasy || false,
          IsMedium: recommendation.IsMedium || true,
          Increments: recommendation.Increments || { Lb: 2.5, Kg: 1 },
          Max: recommendation.Max || { Lb: 500, Kg: 227 },
          Min: recommendation.Min || { Lb: 45, Kg: 20 },
          IsNormalSets: recommendation.IsNormalSets || true,
          IsDeload: recommendation.IsDeload || false,
          IsBackOffSet: recommendation.IsBackOffSet || false,
          BackOffSetWeight: recommendation.BackOffSetWeight || {
            Lb: 115,
            Kg: 52.2,
          },
          IsMaxChallenge: recommendation.IsMaxChallenge || false,
          IsLightSession: recommendation.IsLightSession || false,
          IsPyramid: recommendation.IsPyramid || false,
          IsReversePyramid: recommendation.IsReversePyramid || false,
          HistorySet: recommendation.HistorySet || [],
          ReferenceSetHistory:
            recommendation.ReferenceSetHistory || ({} as WorkoutLogSerieModel),
          isPlateAvailable: recommendation.isPlateAvailable || true,
          isDumbbellAvailable: recommendation.isDumbbellAvailable || true,
          isPulleyAvailable: recommendation.isPulleyAvailable || false,
          isBandsAvailable: recommendation.isBandsAvailable || false,
          Speed: recommendation.Speed || 1,
          IsManual: recommendation.IsManual || false,
          ReferenseReps: recommendation.ReferenseReps || 10,
          ReferenseWeight: recommendation.ReferenseWeight || {
            Lb: 135,
            Kg: 61.2,
          },
          IsDropSet: recommendation.IsDropSet || false,
          ...recommendation,
        }

        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockRecommendation),
        })
      } else {
        await route.continue()
      }
    }
  )
}

export const mockRecommendationWithHistory: RecommendationModel = {
  FirstWorkSetWeight: { Lb: 135, Kg: 61.2 },
  FirstWorkSetReps: 10,
  FirstWorkSet1RM: { Lb: 180, Kg: 81.6 },
  MinReps: 8,
  MaxReps: 12,
  WarmUpWeightSet1: { Lb: 95, Kg: 43.1 },
  WarmUpWeightSet2: { Lb: 115, Kg: 52.2 },
  IsBodyweight: false,
  Series: 3,
  Reps: 10,
  Weight: { Lb: 135, Kg: 61.2 },
  OneRMProgress: 0,
  RecommendationInKg: 61.2,
  OneRMPercentage: 75,
  WarmUpReps1: 5,
  WarmUpReps2: 3,
  WarmUpsList: [],
  WarmupsCount: 2,
  RpRest: 0,
  NbPauses: 0,
  NbRepsPauses: 0,
  IsEasy: false,
  IsMedium: true,
  Increments: { Lb: 2.5, Kg: 1 },
  Max: { Lb: 500, Kg: 227 },
  Min: { Lb: 45, Kg: 20 },
  IsNormalSets: true,
  IsDeload: false,
  IsBackOffSet: false,
  BackOffSetWeight: { Lb: 115, Kg: 52.2 },
  IsMaxChallenge: false,
  IsLightSession: false,
  IsPyramid: false,
  IsReversePyramid: false,
  HistorySet: [],
  ReferenceSetHistory: {} as WorkoutLogSerieModel,
  isPlateAvailable: true,
  isDumbbellAvailable: true,
  isPulleyAvailable: false,
  isBandsAvailable: false,
  Speed: 1,
  IsManual: false,
  ReferenseReps: 10,
  ReferenseWeight: { Lb: 135, Kg: 61.2 },
  IsDropSet: false,
}
