import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ExerciseSetsGrid } from '../ExerciseSetsGrid'
import type { ExerciseModel, RecommendationModel } from '@/types'
import type { ExtendedWorkoutLogSerieModel } from '../ExerciseSetsGrid'

describe('ExerciseSetsGrid - Warmup Set Persistence', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
  }

  const mockRecommendation: RecommendationModel = {
    ExerciseId: 1,
    Weight: { Lb: 135, Kg: 61.2 },
    Reps: 10,
    Series: 3,
    WarmupsCount: 2,
    WarmUpsList: [
      {
        WarmUpReps: 5,
        WarmUpWeightSet: { Lb: 95, Kg: 43.1 },
      },
      {
        WarmUpReps: 3,
        WarmUpWeightSet: { Lb: 115, Kg: 52.2 },
      },
    ],
  } as RecommendationModel

  const mockOnSetUpdate = vi.fn()
  const mockOnSaveCurrentSet = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should keep finished warmup sets editable after saving', async () => {
    // This tests the complete flow: active warmup → saved → still editable
    const user = userEvent.setup()

    // Create a finished warmup set
    const finishedWarmupSet: ExtendedWorkoutLogSerieModel = {
      Id: -1001,
      SetNo: '1',
      IsWarmups: true,
      IsFinished: true, // Set is saved/finished
      IsNext: false,
      WarmUpReps: 5,
      WarmUpWeightSet: { Lb: 95, Kg: 43.1 },
      Reps: 0,
      Weight: { Lb: 0, Kg: 0 },
    }

    // Create an active work set
    const activeWorkSet: ExtendedWorkoutLogSerieModel = {
      Id: -2001,
      SetNo: '3',
      IsWarmups: false,
      IsFinished: false,
      IsNext: true, // This is the current active set
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
    }

    const sets = [finishedWarmupSet, activeWorkSet]

    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockRecommendation}
        sets={sets}
        onSetUpdate={mockOnSetUpdate}
        onSaveCurrentSet={mockOnSaveCurrentSet}
        unit="lbs"
      />
    )

    // Find the finished warmup set inputs
    const warmupRepsInput = screen.getByLabelText('Reps for set W')
    const warmupWeightInput = screen.getByLabelText('Weight for set W')

    // Verify that finished warmup sets are NOT disabled
    expect(warmupRepsInput).not.toBeDisabled()
    expect(warmupWeightInput).not.toBeDisabled()

    // Test that we can interact with the finished warmup set
    await user.clear(warmupRepsInput)
    await user.type(warmupRepsInput, '6')

    // Verify the update handler was called
    expect(mockOnSetUpdate).toHaveBeenCalledWith(-1001, { reps: 6 })

    // Test weight input interaction
    await user.clear(warmupWeightInput)
    await user.type(warmupWeightInput, '100')

    expect(mockOnSetUpdate).toHaveBeenCalledWith(-1001, { weight: 100 })
  })

  it('should show visual indication for finished sets while keeping them editable', () => {
    // Test that finished sets have visual styling but remain interactive
    const finishedWarmupSet: ExtendedWorkoutLogSerieModel = {
      Id: -1001,
      SetNo: '1',
      IsWarmups: true,
      IsFinished: true,
      IsNext: false,
      WarmUpReps: 5,
      WarmUpWeightSet: { Lb: 95, Kg: 43.1 },
      Reps: 0,
      Weight: { Lb: 0, Kg: 0 },
    }

    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockRecommendation}
        sets={[finishedWarmupSet]}
        onSetUpdate={mockOnSetUpdate}
        unit="lbs"
      />
    )

    // Should show check icon for finished set
    const checkIcon = screen.getByTestId('check-icon')
    expect(checkIcon).toBeInTheDocument()

    // But inputs should still be enabled
    const repsInput = screen.getByLabelText('Reps for set W')
    const weightInput = screen.getByLabelText('Weight for set W')

    expect(repsInput).not.toBeDisabled()
    expect(weightInput).not.toBeDisabled()
  })

  it('should handle multiple finished warmup sets', async () => {
    // Test with multiple warmup sets, all finished
    const user = userEvent.setup()

    const finishedWarmupSet1: ExtendedWorkoutLogSerieModel = {
      Id: -1001,
      SetNo: '1',
      IsWarmups: true,
      IsFinished: true,
      IsNext: false,
      WarmUpReps: 5,
      WarmUpWeightSet: { Lb: 95, Kg: 43.1 },
      Reps: 0,
      Weight: { Lb: 0, Kg: 0 },
    }

    const finishedWarmupSet2: ExtendedWorkoutLogSerieModel = {
      Id: -1002,
      SetNo: '2',
      IsWarmups: true,
      IsFinished: true,
      IsNext: false,
      WarmUpReps: 3,
      WarmUpWeightSet: { Lb: 115, Kg: 52.2 },
      Reps: 0,
      Weight: { Lb: 0, Kg: 0 },
    }

    const sets = [finishedWarmupSet1, finishedWarmupSet2]

    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockRecommendation}
        sets={sets}
        onSetUpdate={mockOnSetUpdate}
        unit="lbs"
      />
    )

    // Both warmup sets should have W as their set number
    const warmupLabels = screen.getAllByText('W')
    expect(warmupLabels).toHaveLength(2)

    // Find all reps inputs (skip header)
    const allRepsInputs = screen.getAllByRole('spinbutton', {
      name: /Reps for set/i,
    })

    // All should be editable
    allRepsInputs.forEach((input) => {
      expect(input).not.toBeDisabled()
    })

    // Test editing the first warmup set
    await user.clear(allRepsInputs[0])
    await user.type(allRepsInputs[0], '6')

    expect(mockOnSetUpdate).toHaveBeenCalledWith(-1001, { reps: 6 })
  })
})
