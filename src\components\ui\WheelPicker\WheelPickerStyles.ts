// Add scrollbar-hide styles to global CSS
export function injectWheelPickerStyles() {
  if (typeof document === 'undefined') return

  const styleId = 'wheel-picker-styles'

  // Check if styles already exist
  if (document.getElementById(styleId)) return

  const style = `
    .scrollbar-hide {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
    .scrollbar-hide::-webkit-scrollbar {
      display: none;
    }
  `

  const styleElement = document.createElement('style')
  styleElement.id = styleId
  styleElement.textContent = style
  document.head.appendChild(styleElement)
}
