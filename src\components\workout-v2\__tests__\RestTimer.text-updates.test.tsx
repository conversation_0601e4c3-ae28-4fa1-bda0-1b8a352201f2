import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { RestTimer } from '../RestTimer'
import type { RestTimerState } from '@/stores/workoutStore/types'

// Mock the workout store
const mockSetRestTimerState = vi.fn()
let mockRestTimerState: RestTimerState = {
  isActive: false,
  duration: 0,
}

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    restTimerState: mockRestTimerState,
    setRestTimerState: mockSetRestTimerState,
  }),
}))

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, className, ...props }: any) => (
      <div className={className} data-testid="motion-div" {...props}>
        {children}
      </div>
    ),
  },
  AnimatePresence: ({ children }: any) => (
    <div data-testid="animate-presence">{children}</div>
  ),
}))

describe('RestTimer - Text Updates per PR #334', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockRestTimerState = { isActive: false, duration: 0 }
  })

  it('should display "Rest" instead of "Rest Timer"', () => {
    // Given: RestTimer is active
    mockRestTimerState = {
      isActive: true,
      duration: 60,
    }

    // When: RestTimer renders
    render(<RestTimer />)

    // Then: Should display "Rest" as the title
    expect(screen.getByText('Rest')).toBeInTheDocument()

    // Should NOT display "Rest Timer"
    expect(screen.queryByText('Rest Timer')).not.toBeInTheDocument()
  })

  it('should not display motivational "Get ready for..." hint', () => {
    // Given: RestTimer is active with next set info
    mockRestTimerState = {
      isActive: true,
      duration: 90,
      nextSetInfo: {
        reps: 8,
        weight: 135,
        unit: 'lbs',
      },
    }

    // When: RestTimer renders
    render(<RestTimer />)

    // Then: Should NOT display motivational text
    expect(screen.queryByText(/Get ready for/)).not.toBeInTheDocument()
    expect(
      screen.queryByText('Get ready for your next set!')
    ).not.toBeInTheDocument()
    expect(
      screen.queryByText('Get ready for: 8 x 135 lbs')
    ).not.toBeInTheDocument()
  })

  it('should not display motivational text for bodyweight exercises', () => {
    // Given: RestTimer for bodyweight exercise
    mockRestTimerState = {
      isActive: true,
      duration: 60,
      nextSetInfo: {
        reps: 15,
        weight: 0,
        unit: 'lbs',
      },
    }

    // When: RestTimer renders
    render(<RestTimer />)

    // Then: Should NOT display "Get ready for: 15 reps"
    expect(screen.queryByText('Get ready for: 15 reps')).not.toBeInTheDocument()
  })

  it('should not display any motivational text when no next set info', () => {
    // Given: RestTimer without next set info
    mockRestTimerState = {
      isActive: true,
      duration: 45,
      // No nextSetInfo provided
    }

    // When: RestTimer renders
    render(<RestTimer />)

    // Then: Should NOT display generic motivational message
    expect(
      screen.queryByText('Get ready for your next set!')
    ).not.toBeInTheDocument()
  })

  it('should maintain countdown display and skip button functionality', () => {
    // Given: RestTimer is active
    mockRestTimerState = {
      isActive: true,
      duration: 120,
    }

    // When: RestTimer renders
    render(<RestTimer />)

    // Then: Should still display countdown
    expect(screen.getByTestId('countdown-text')).toBeInTheDocument()

    // Should still have skip button (Hide)
    expect(screen.getByText('Hide')).toBeInTheDocument()

    // Should still have progress bar
    expect(screen.getByTestId('rest-timer-progress')).toBeInTheDocument()
  })

  it('should maintain gold gradient styling on countdown text', () => {
    // Given: RestTimer is active
    mockRestTimerState = {
      isActive: true,
      duration: 75,
    }

    // When: RestTimer renders
    render(<RestTimer />)

    // Then: Countdown should have gold gradient classes
    const countdownText = screen.getByTestId('countdown-text')
    expect(countdownText.classList.contains('bg-gradient-to-r')).toBe(true)
    expect(countdownText.classList.contains('from-brand-gold-start')).toBe(true)
    expect(countdownText.classList.contains('to-brand-gold-end')).toBe(true)
    expect(countdownText.classList.contains('bg-clip-text')).toBe(true)
    expect(countdownText.classList.contains('text-transparent')).toBe(true)
  })
})
