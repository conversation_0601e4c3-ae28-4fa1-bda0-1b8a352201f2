import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { SetMetricsDisplay } from '../SetMetricsDisplay'
import type { RecommendationModel } from '@/types'

// Mock useSetMetrics hook
vi.mock('@/hooks/useSetMetrics')

describe('SetMetricsDisplay', () => {
  const mockRecommendation: RecommendationModel = {
    Id: 1,
    WarmupsCount: 2,
    Series: 3,
  } as RecommendationModel

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should display last time and 1RM progress when available', async () => {
    const { useSetMetrics } = await import('@/hooks/useSetMetrics')
    vi.mocked(useSetMetrics).mockReturnValue({
      lastTimeReps: 12,
      lastTimeWeight: 60,
      oneRMProgress: 15.75,
    })

    render(
      <SetMetricsDisplay
        recommendation={mockRecommendation}
        currentSetIndex={2}
        isWarmup={false}
        isFirstWorkSet
        unit="kg"
        currentReps={12}
        currentWeight={65}
      />
    )

    expect(screen.getByTestId('set-metrics')).toBeInTheDocument()
    expect(
      screen.getByText(/Last workout best: 12 × 60 kg/)
    ).toBeInTheDocument()
    expect(screen.getByText(/Today: \+15\.75%/)).toBeInTheDocument()
  })

  it('should display "Today" section before "Last workout best" section', async () => {
    const { useSetMetrics } = await import('@/hooks/useSetMetrics')
    vi.mocked(useSetMetrics).mockReturnValue({
      lastTimeReps: 10,
      lastTimeWeight: 50,
      oneRMProgress: 12.5,
    })

    render(
      <SetMetricsDisplay
        recommendation={mockRecommendation}
        currentSetIndex={2}
        isWarmup={false}
        isFirstWorkSet
        unit="kg"
        currentReps={10}
        currentWeight={55}
      />
    )

    const setMetrics = screen.getByTestId('set-metrics')
    const todayText = screen.getByText(/Today: \+12\.50%/)
    const lastWorkoutText = screen.getByText(/Last workout best: 10 × 50 kg/)

    // Today should appear before Last workout best in the DOM
    expect(setMetrics).toContainElement(todayText)
    expect(setMetrics).toContainElement(lastWorkoutText)

    // Verify order by checking that Today appears first in the document flow
    const allText = setMetrics.textContent
    const todayIndex = allText?.indexOf('Today') ?? -1
    const lastWorkoutIndex = allText?.indexOf('Last workout best') ?? -1

    expect(todayIndex).toBeGreaterThan(-1)
    expect(lastWorkoutIndex).toBeGreaterThan(-1)
    expect(todayIndex).toBeLessThan(lastWorkoutIndex)
  })

  it('should display only last time when 1RM progress not available', async () => {
    const { useSetMetrics } = await import('@/hooks/useSetMetrics')
    vi.mocked(useSetMetrics).mockReturnValue({
      lastTimeReps: 10,
      lastTimeWeight: 132,
      oneRMProgress: null,
    })

    render(
      <SetMetricsDisplay
        recommendation={mockRecommendation}
        currentSetIndex={1}
        isWarmup
        isFirstWorkSet={false}
        unit="lbs"
        currentReps={10}
        currentWeight={130}
      />
    )

    expect(screen.getByTestId('set-metrics')).toBeInTheDocument()
    expect(
      screen.getByText(/Last workout best: 10 × 132 lbs/)
    ).toBeInTheDocument()
    expect(screen.queryByText(/Today:/)).not.toBeInTheDocument()
  })

  it('should not render when no metrics are available', async () => {
    const { useSetMetrics } = await import('@/hooks/useSetMetrics')
    vi.mocked(useSetMetrics).mockReturnValue({
      lastTimeReps: null,
      lastTimeWeight: null,
      oneRMProgress: null,
    })

    render(
      <SetMetricsDisplay
        recommendation={mockRecommendation}
        currentSetIndex={0}
        isWarmup={false}
        isFirstWorkSet={false}
        unit="kg"
        currentReps={8}
        currentWeight={50}
      />
    )

    expect(screen.queryByTestId('set-metrics')).not.toBeInTheDocument()
  })

  it('should handle null recommendation', async () => {
    const { useSetMetrics } = await import('@/hooks/useSetMetrics')
    vi.mocked(useSetMetrics).mockReturnValue({
      lastTimeReps: null,
      lastTimeWeight: null,
      oneRMProgress: null,
    })

    render(
      <SetMetricsDisplay
        recommendation={null}
        currentSetIndex={0}
        isWarmup={false}
        isFirstWorkSet={false}
        unit="kg"
        currentReps={8}
        currentWeight={50}
      />
    )

    expect(screen.queryByTestId('set-metrics')).not.toBeInTheDocument()
  })

  it('should pass correct parameters to useSetMetrics hook', async () => {
    const { useSetMetrics } = await import('@/hooks/useSetMetrics')
    const mockUseSetMetrics = vi.mocked(useSetMetrics)
    mockUseSetMetrics.mockReturnValue({
      lastTimeReps: null,
      lastTimeWeight: null,
      oneRMProgress: null,
    })

    render(
      <SetMetricsDisplay
        recommendation={mockRecommendation}
        currentSetIndex={2}
        isWarmup={false}
        isFirstWorkSet
        unit="kg"
        currentReps={12}
        currentWeight={65}
      />
    )

    expect(mockUseSetMetrics).toHaveBeenCalledWith({
      recommendation: mockRecommendation,
      currentSetIndex: 2,
      isWarmup: false,
      unit: 'kg',
      isFirstWorkSet: true,
      currentReps: 12,
      currentWeight: 65,
    })
  })
})
