import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, act } from '@testing-library/react'
import { RestTimer } from '../RestTimer'

// Mock the workout store
const mockSetRestTimerState = vi.fn()
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    restTimerState: { isActive: true, duration: 90 },
    setRestTimerState: mockSetRestTimerState,
  }),
}))

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion - pass through className and data-testid properly
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, className, ...props }: any) => (
      <div className={className} {...props}>
        {children}
      </div>
    ),
  },
  AnimatePresence: ({ children }: any) => (
    <div data-testid="animate-presence">{children}</div>
  ),
}))

describe('RestTimer - Position Changes', () => {
  it('should be positioned at bottom of screen, not top', () => {
    // Given: RestTimer is active
    render(<RestTimer />)

    // When: Timer overlay displays
    const timerContainer = screen.getByTestId('rest-timer-container')

    // Then: Should be positioned at bottom with fixed positioning
    expect(timerContainer).toHaveClass('bottom-0')
    expect(timerContainer).toHaveClass('fixed')
    expect(timerContainer).not.toHaveClass('top-0')
  })

  it('should use fixed positioning without excessive z-index', () => {
    // Given: RestTimer is rendered
    render(<RestTimer />)

    // When: Timer displays
    const timerContainer = screen.getByTestId('rest-timer-container')

    // Then: Should use fixed positioning but not excessive z-index
    expect(timerContainer).toHaveClass('fixed')
    expect(timerContainer).not.toHaveClass('z-50')
  })

  it('should maintain proper mobile viewport positioning', () => {
    // Given: RestTimer in mobile context
    render(<RestTimer />)

    // When: Timer displays
    const timerContainer = screen.getByTestId('rest-timer-container')

    // Then: Should be fixed at bottom for mobile viewport
    expect(timerContainer).toHaveClass('bottom-0')
    expect(timerContainer).toHaveClass('fixed')
    expect(timerContainer).toHaveClass('left-0')
    expect(timerContainer).toHaveClass('right-0')
  })

  it('should remove "Catch your breath and hydrate" text', () => {
    // Given: RestTimer is active with > 30s remaining
    render(<RestTimer />)

    // When: Timer displays motivational text
    // Then: Should not show the unwanted hydration message
    expect(
      screen.queryByText(/catch your breath and hydrate/i)
    ).not.toBeInTheDocument()

    // But should show alternative motivational text
    expect(
      screen.getByText(/get ready for your next set!/i)
    ).toBeInTheDocument()
  })
})

describe('RestTimer - Notification Error Handling', () => {
  const originalNotification = global.Notification

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
    // Reset Notification mock before each test
    global.Notification = originalNotification
  })

  afterEach(() => {
    vi.useRealTimers()
    // Restore original Notification after each test
    global.Notification = originalNotification
  })

  it('should handle Notification constructor throwing illegal constructor error', async () => {
    // Given: Notification constructor throws illegal constructor error
    const mockNotification = vi.fn(() => {
      throw new DOMException('Illegal constructor', 'TypeError')
    })
    global.Notification = mockNotification as any
    global.Notification.permission = 'granted'

    // Mock console.warn to verify error logging
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

    // When: Timer countdown reaches completion (simulate timer completion)
    render(<RestTimer />)

    // Wait for initial render and timer setup
    await vi.waitFor(() => {
      expect(screen.getByText(/Rest Timer/)).toBeInTheDocument()
    })

    // Simulate timer completion by advancing timers
    act(() => {
      vi.advanceTimersByTime(90000) // 90 seconds
    })

    // Then: Error should be caught and logged, but app should not crash
    expect(consoleSpy).toHaveBeenCalledWith(
      'Failed to show rest timer notification:',
      expect.any(Error)
    )

    // Timer should still complete normally
    expect(mockSetRestTimerState).toHaveBeenCalledWith({
      isActive: false,
      duration: 0,
    })

    consoleSpy.mockRestore()
  })

  it('should successfully create notification when constructor works normally', async () => {
    // Given: Working Notification constructor
    const mockNotificationInstance = {
      close: vi.fn(),
    }
    const mockNotification = vi.fn(() => mockNotificationInstance)
    global.Notification = mockNotification as any
    global.Notification.permission = 'granted'

    // When: Timer completes
    render(<RestTimer />)

    // Simulate timer completion
    act(() => {
      vi.advanceTimersByTime(90000)
    })

    // Then: Notification should be created successfully
    expect(mockNotification).toHaveBeenCalledWith('Rest Complete!', {
      body: 'Time for your next set',
      icon: '/icon-192x192.png',
      tag: 'rest-timer',
    })

    // And notification should be closed after 5 seconds
    act(() => {
      vi.advanceTimersByTime(5000)
    })
    expect(mockNotificationInstance.close).toHaveBeenCalled()
  })

  it('should not attempt notification when permission is not granted', async () => {
    // Given: Notification permission is denied
    const mockNotification = vi.fn()
    global.Notification = mockNotification as any
    global.Notification.permission = 'denied'

    // When: Timer completes
    render(<RestTimer />)
    act(() => {
      vi.advanceTimersByTime(90000)
    })

    // Then: Notification constructor should not be called
    expect(mockNotification).not.toHaveBeenCalled()

    // But timer should still complete
    expect(mockSetRestTimerState).toHaveBeenCalledWith({
      isActive: false,
      duration: 0,
    })
  })

  it('should not attempt notification when Notification API is not available', async () => {
    // Given: Notification API is not available
    delete (global as any).Notification
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

    // When: Timer completes
    render(<RestTimer />)
    act(() => {
      vi.advanceTimersByTime(90000)
    })

    // Then: No notification should be attempted and no errors should occur
    expect(consoleSpy).not.toHaveBeenCalled()
    expect(mockSetRestTimerState).toHaveBeenCalledWith({
      isActive: false,
      duration: 0,
    })

    consoleSpy.mockRestore()
  })

  it('should handle SecurityError when creating notification', async () => {
    // Given: Notification constructor throws SecurityError
    const mockNotification = vi.fn(() => {
      throw new DOMException('Security error', 'SecurityError')
    })
    global.Notification = mockNotification as any
    global.Notification.permission = 'granted'

    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

    // When: Timer completes
    render(<RestTimer />)
    act(() => {
      vi.advanceTimersByTime(90000)
    })

    // Then: Error should be caught and logged
    expect(consoleSpy).toHaveBeenCalledWith(
      'Failed to show rest timer notification:',
      expect.any(Error)
    )

    // Timer should still complete normally
    expect(mockSetRestTimerState).toHaveBeenCalledWith({
      isActive: false,
      duration: 0,
    })

    consoleSpy.mockRestore()
  })
})
