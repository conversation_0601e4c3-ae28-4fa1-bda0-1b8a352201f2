import { redirect } from 'next/navigation'
import { ExercisePageV2Client } from './ExercisePageV2Client'
import { isValidExerciseId } from '@/utils/exerciseValidation'

export const dynamic = 'force-dynamic'

interface ExercisePageV2Props {
  params: Promise<{
    id: string
  }>
}

export default async function ExercisePageV2({ params }: ExercisePageV2Props) {
  const { id } = await params
  const exerciseId = parseInt(id)

  if (!isValidExerciseId(exerciseId)) {
    redirect('/workout')
  }

  return <ExercisePageV2Client exerciseId={exerciseId} />
}
