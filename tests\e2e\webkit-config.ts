import { devices } from '@playwright/test'

/**
 * WebKit-specific configuration for CI stability
 * Enhanced with memory management and error recovery
 */
export const webkitLaunchOptions = {
  timeout: 120000, // 2 minutes timeout for faster feedback
  slowMo: 250, // Reduced delay for faster execution
  headless: true,
  args: [
    // WebKit doesn't support Chrome/Chromium specific flags
    // Keep args array empty for WebKit compatibility
  ],
  // Environment variables for WebKit stability
  env: {
    ...process.env,
    WEBKIT_DISABLE_COMPOSITING: '1',
    WEBKIT_FORCE_COMPOSITING_MODE: '0',
    // Memory management
    NODE_OPTIONS: '--max_old_space_size=8192 --max-semi-space-size=512',
  },
  // Additional stability options
  chromiumSandbox: false,
  // Handle crashes gracefully
  handleSIGINT: false,
  handleSIGTERM: false,
}

export const webkitContextOptions = {
  viewport: { width: 390, height: 844 },
  reducedMotion: 'reduce' as const,
  // Disable animations and features for stability
  forcedColors: 'none' as const,
  colorScheme: 'light' as const,
  // Disable service workers which can cause issues
  serviceWorkers: 'block' as const,
  // Set explicit locale
  locale: 'en-US',
  // Disable permissions that might cause prompts
  permissions: [] as string[],
  // Additional stability options
  bypassCSP: true,
  ignoreHTTPSErrors: true,
  // Disable unnecessary features that can cause crashes
  javaScriptEnabled: true,
  // Reduce memory usage
  strictSelectors: false,
  // Handle timeouts gracefully
  timeout: 180000, // 3 minutes for context operations
}

export const webkitTestOptions = {
  // Increase timeouts for WebKit
  actionTimeout: 45000, // 45 seconds for actions
  navigationTimeout: 90000, // 90 seconds for navigation
  // Add stability options
  screenshot: 'only-on-failure' as const,
  video: 'retain-on-failure' as const,
  trace: 'retain-on-failure' as const,
  // Additional headers for debugging
  extraHTTPHeaders: {
    'X-WebKit-Test': 'true',
    'Cache-Control': 'no-cache',
    Pragma: 'no-cache',
  },
}

export const webkitProjectConfig = {
  name: 'Mobile Safari',
  use: {
    ...devices['iPhone 13'],
    viewport: { width: 390, height: 844 },
    hasTouch: true,
    isMobile: true,
    launchOptions: webkitLaunchOptions,
    contextOptions: webkitContextOptions,
    ...webkitTestOptions,
  },
  retries: 5, // Increased retries for WebKit instability
  workers: 1, // Single worker for WebKit to prevent resource exhaustion
  // Test timeout configuration
  timeout: 180000, // 3 minutes per test
  expect: {
    timeout: 45000, // 45 seconds for assertions
  },
}
