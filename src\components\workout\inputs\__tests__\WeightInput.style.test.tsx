import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { WeightInput } from '../WeightInput'

const defaultProps = {
  weight: 100,
  unit: 'lbs' as const,
  onChange: vi.fn(),
  onIncrement: vi.fn(),
  onDecrement: vi.fn(),
}

describe('WeightInput - Updated styling', () => {
  it('should display uppercase unit label without italic styling', () => {
    render(<WeightInput {...defaultProps} />)

    const label = screen.getByText('LBS')
    expect(label).toBeInTheDocument()
    expect(label).not.toHaveClass('italic')
    expect(label).toHaveClass('text-text-secondary')
  })

  it('should display uppercase "KG" when unit is kg', () => {
    render(<WeightInput {...defaultProps} unit="kg" />)

    const label = screen.getByText('KG')
    expect(label).toBeInTheDocument()
    expect(label).not.toHaveClass('italic')
    expect(label).toHaveClass('text-text-secondary')
  })

  it('should accept uppercase unit and display as uppercase', () => {
    // @ts-expect-error Testing uppercase unit input
    render(<WeightInput {...defaultProps} unit="LBS" />)

    const label = screen.getByText('LBS')
    expect(label).toBeInTheDocument()
    expect(screen.queryByText('lbs')).not.toBeInTheDocument()
  })

  it('should accept lowercase unit and display as uppercase', () => {
    render(<WeightInput {...defaultProps} unit="lbs" />)

    const label = screen.getByText('LBS')
    expect(label).toBeInTheDocument()
    expect(screen.queryByText('lbs')).not.toBeInTheDocument()
  })

  it('should have label with proper styling', () => {
    render(<WeightInput {...defaultProps} />)

    const label = screen.getByText('LBS')
    // Should have small size and secondary color but no italic
    expect(label).toHaveClass('text-sm')
    expect(label).toHaveClass('text-text-secondary')
    expect(label).not.toHaveClass('italic')
  })
})
