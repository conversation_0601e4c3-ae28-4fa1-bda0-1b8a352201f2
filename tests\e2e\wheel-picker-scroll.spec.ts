import { test, expect } from '@playwright/test'

test.describe('WheelPicker Scroll Conflicts', () => {
  test.beforeEach(async ({ page }) => {
    // Mock login state
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for navigation to workout page
    await page.waitForURL('**/workout')

    // Navigate to v2 exercise page
    await page.click('[data-testid="try-new-ui-button"]')
    await page.waitForLoadState('networkidle')
  })

  test('should respond to horizontal swipe on reps WheelPicker without triggering page scroll', async ({
    page,
  }) => {
    // Given: User is on exercise v2 page with WheelPicker visible
    await expect(
      page.locator('[data-testid="reps-wheel-picker"]')
    ).toBeVisible()

    // Get initial reps value
    const initialRepsButton = page
      .locator('[data-testid^="wheel-value-"]')
      .first()
    const initialValue = await initialRepsButton.textContent()

    // Get initial page scroll position
    const initialScrollY = await page.evaluate(() => window.scrollY)

    // When: User swipes horizontally on WheelPicker for reps
    const repsContainer = page.locator(
      '[data-testid="reps-wheel-picker"] [data-testid="wheel-picker-container"]'
    )
    await repsContainer.hover()

    // Simulate horizontal swipe gesture (this test will fail until WheelPicker is fixed)
    await page.mouse.down()
    await page.mouse.move(100, 0) // Horizontal movement only
    await page.mouse.up()

    // Then: Reps value should change and page should not scroll vertically
    await page.waitForTimeout(100) // Allow for animation

    const finalScrollY = await page.evaluate(() => window.scrollY)
    const finalRepsButton = page
      .locator('[data-testid^="wheel-value-"]')
      .first()
    const finalValue = await finalRepsButton.textContent()

    // Value should change (WheelPicker working)
    expect(finalValue).not.toBe(initialValue)

    // Page should not scroll vertically (no scroll conflict)
    expect(finalScrollY).toBe(initialScrollY)
  })

  test('should respond to horizontal swipe on weight WheelPicker without triggering page scroll', async ({
    page,
  }) => {
    // Given: User is on exercise v2 page with WheelPicker visible
    await expect(
      page.locator('[data-testid="weight-wheel-picker"]')
    ).toBeVisible()

    // Get initial weight value
    const initialWeightButton = page
      .locator(
        '[data-testid="weight-wheel-picker"] [data-testid^="wheel-value-"]'
      )
      .first()
    const initialValue = await initialWeightButton.textContent()

    // Get initial page scroll position
    const initialScrollY = await page.evaluate(() => window.scrollY)

    // When: User swipes horizontally on WheelPicker for weight
    const weightContainer = page.locator(
      '[data-testid="weight-wheel-picker"] [data-testid="wheel-picker-container"]'
    )
    await weightContainer.hover()

    // Simulate horizontal swipe gesture
    await page.mouse.down()
    await page.mouse.move(100, 0) // Horizontal movement only
    await page.mouse.up()

    // Then: Weight value should change and page should not scroll vertically
    await page.waitForTimeout(100) // Allow for animation

    const finalScrollY = await page.evaluate(() => window.scrollY)
    const finalWeightButton = page
      .locator(
        '[data-testid="weight-wheel-picker"] [data-testid^="wheel-value-"]'
      )
      .first()
    const finalValue = await finalWeightButton.textContent()

    // Value should change (WheelPicker working)
    expect(finalValue).not.toBe(initialValue)

    // Page should not scroll vertically (no scroll conflict)
    expect(finalScrollY).toBe(initialScrollY)
  })

  test('should handle rapid touch gestures without flakiness', async ({
    page,
  }) => {
    // Given: User interacts with WheelPicker on mobile viewport
    await page.setViewportSize({ width: 375, height: 667 }) // iPhone viewport
    await expect(
      page.locator('[data-testid="reps-wheel-picker"]')
    ).toBeVisible()

    const repsContainer = page.locator(
      '[data-testid="reps-wheel-picker"] [data-testid="wheel-picker-container"]'
    )

    // When: Multiple rapid touch gestures are performed
    await repsContainer.hover()
    await page.mouse.down()
    await page.mouse.move(50, 0)
    await page.mouse.up()
    await page.waitForTimeout(50)

    await repsContainer.hover()
    await page.mouse.down()
    await page.mouse.move(-50, 0)
    await page.mouse.up()
    await page.waitForTimeout(50)

    await repsContainer.hover()
    await page.mouse.down()
    await page.mouse.move(50, 0)
    await page.mouse.up()

    // Then: All gestures should register correctly without flakiness
    // WheelPicker should still be responsive
    await expect(repsContainer).toBeVisible()

    // Should be able to interact normally after rapid gestures
    await repsContainer.click()
    await expect(
      page.locator('[data-testid^="wheel-value-"]').first()
    ).toBeVisible()
  })
})
