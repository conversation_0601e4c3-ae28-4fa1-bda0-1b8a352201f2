import { test, expect } from '@playwright/test'
import { login, startWorkout } from './helpers/auth'

test.describe('Exercise Overflow Menu', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
    await page.goto('/workout')
    await startWorkout(page)
  })

  test('should open overflow menu on tap without preventDefault warnings', async ({
    page,
  }) => {
    // Set up console listener for preventDefault warnings
    const consoleWarnings: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'warning' && msg.text().includes('preventDefault')) {
        consoleWarnings.push(msg.text())
      }
    })

    // Navigate to first exercise
    await page.click('[data-testid="exercise-card"]:first-child')
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Find and tap the overflow menu button
    const menuButton = page.getByLabel('Exercise options')
    await expect(menuButton).toBeVisible()

    // Tap the menu button (simulates touch event on mobile)
    await menuButton.tap()

    // Verify menu opens
    const menu = page.getByRole('menu')
    await expect(menu).toBeVisible()

    // Verify menu items are present
    await expect(page.getByText('Skip Exercise')).toBeVisible()
    await expect(page.getByText('View History')).toBeVisible()
    await expect(page.getByText('Add Note')).toBeVisible()

    // Verify no preventDefault warnings
    expect(consoleWarnings).toHaveLength(0)
  })

  test('should close menu when tapping outside', async ({ page }) => {
    // Navigate to first exercise
    await page.click('[data-testid="exercise-card"]:first-child')
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Open menu
    const menuButton = page.getByLabel('Exercise options')
    await menuButton.tap()

    const menu = page.getByRole('menu')
    await expect(menu).toBeVisible()

    // Tap outside
    await page.locator('body').tap({ position: { x: 10, y: 10 } })

    // Menu should close
    await expect(menu).not.toBeVisible()
  })

  test('should handle menu actions correctly', async ({ page }) => {
    // Navigate to first exercise
    await page.click('[data-testid="exercise-card"]:first-child')
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Open menu
    const menuButton = page.getByLabel('Exercise options')
    await menuButton.tap()

    // Test Skip Exercise (just verify it can be clicked)
    await page.getByText('Skip Exercise').tap()

    // Menu should close after action
    const menu = page.getByRole('menu')
    await expect(menu).not.toBeVisible()
  })

  test('should position menu correctly near bottom of viewport', async ({
    page,
  }) => {
    // Navigate to first exercise
    await page.click('[data-testid="exercise-card"]:first-child')
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Scroll so menu button is near bottom
    await page.evaluate(() => {
      const button = document.querySelector('[aria-label="Exercise options"]')
      if (button) {
        button.scrollIntoView({ block: 'end' })
      }
    })

    // Open menu
    const menuButton = page.getByLabel('Exercise options')
    await menuButton.tap()

    // Check menu is visible and positioned correctly
    const menu = page.getByRole('menu')
    await expect(menu).toBeVisible()

    // Get menu position
    const menuBox = await menu.boundingBox()
    const viewportSize = page.viewportSize()

    if (menuBox && viewportSize) {
      // Menu should not extend beyond viewport
      expect(menuBox.y + menuBox.height).toBeLessThanOrEqual(
        viewportSize.height
      )
    }
  })

  test('should work with keyboard navigation', async ({ page }) => {
    // Navigate to first exercise
    await page.click('[data-testid="exercise-card"]:first-child')
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Focus menu button and open with Enter
    const menuButton = page.getByLabel('Exercise options')
    await menuButton.focus()
    await page.keyboard.press('Enter')

    // Menu should open
    const menu = page.getByRole('menu')
    await expect(menu).toBeVisible()

    // First menu item should be focused
    const firstMenuItem = page.getByRole('menuitem').first()
    await expect(firstMenuItem).toBeFocused()

    // Close with Escape
    await page.keyboard.press('Escape')
    await expect(menu).not.toBeVisible()

    // Focus should return to button
    await expect(menuButton).toBeFocused()
  })
})
