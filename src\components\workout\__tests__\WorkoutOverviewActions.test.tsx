import { renderHook, act } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useWorkoutActions } from '../WorkoutOverviewActions'
import type { WorkoutTemplateGroupModel } from '@/types'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

// Mock debugLog
vi.mock('@/utils/debugLog', () => ({
  debugLog: {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}))

// Mock exercise validation to allow test IDs
vi.mock('@/utils/exerciseValidation', () => ({
  isValidExerciseId: vi.fn((id: number) => id > 0 && Number.isInteger(id)),
}))

describe('useWorkoutActions', () => {
  const mockPush = vi.fn()
  const mockRouter = { push: mockPush }
  const mockStartWorkout = vi.fn()
  const mockLoadExerciseRecommendation = vi.fn()
  const mockUpdateExerciseWorkSets = vi.fn()
  const mockFinishWorkout = vi.fn()

  const mockTodaysWorkout: WorkoutTemplateGroupModel[] = [
    {
      Id: 1,
      Name: 'Test Workout',
      CreatedOn: '2024-01-01',
      WorkoutTemplates: [
        {
          Id: 1,
          Label: 'Test Workout',
          Exercises: [
            {
              Id: 123,
              Label: 'Bench Press',
              BodyPartId: 1,
              IsBodyweight: false,
            },
            {
              Id: 456,
              Label: 'Squats',
              BodyPartId: 2,
              IsBodyweight: false,
            },
          ],
        },
      ],
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue(mockRouter)
  })

  describe('handleStartWorkout', () => {
    it('should navigate to first exercise after workout starts with updated exercises', async () => {
      // Initial exercises array is empty
      const initialExercises: any[] = []

      // Mock startWorkout to return success with first exercise ID
      mockStartWorkout.mockImplementation(async () => {
        // Simulate the workout store being updated and returning first exercise ID
        return { success: true, firstExerciseId: 123 }
      })

      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockTodaysWorkout,
          startWorkout: mockStartWorkout,
          exercises: initialExercises,
          workoutSession: null,
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      // Start the workout
      await act(async () => {
        await result.current.handleStartWorkout()
      })

      // Verify startWorkout was called
      expect(mockStartWorkout).toHaveBeenCalledWith(mockTodaysWorkout)

      // Now navigation should happen with the first exercise ID from startWorkout (V2)
      expect(mockPush).toHaveBeenCalledWith('/workout/exercise/123')
    })

    it('should not navigate if startWorkout fails', async () => {
      mockStartWorkout.mockResolvedValue({ success: false })

      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockTodaysWorkout,
          startWorkout: mockStartWorkout,
          exercises: [],
          workoutSession: null,
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      await act(async () => {
        await result.current.handleStartWorkout()
      })

      expect(mockStartWorkout).toHaveBeenCalledWith(mockTodaysWorkout)
      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should handle errors gracefully', async () => {
      mockStartWorkout.mockRejectedValue(new Error('Network error'))

      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockTodaysWorkout,
          startWorkout: mockStartWorkout,
          exercises: [],
          workoutSession: null,
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      await act(async () => {
        await result.current.handleStartWorkout()
      })

      expect(mockPush).not.toHaveBeenCalled()
      expect(result.current.isStartingWorkout).toBe(false)
    })
  })

  describe('handleExerciseClick', () => {
    it('should start workout if not already started before navigating', async () => {
      mockStartWorkout.mockResolvedValue({
        success: true,
        firstExerciseId: 123,
      })

      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockTodaysWorkout,
          startWorkout: mockStartWorkout,
          exercises: [{ Id: 123, Label: 'Bench Press', sets: [] }],
          workoutSession: null, // No active session
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      await act(async () => {
        await result.current.handleExerciseClick(123)
      })

      expect(mockStartWorkout).toHaveBeenCalledWith(mockTodaysWorkout)
      expect(mockPush).toHaveBeenCalledWith('/workout/exercise/123')
    })

    it('should pre-load exercise recommendation before navigation', async () => {
      mockLoadExerciseRecommendation.mockResolvedValue({
        Id: 1,
        Reps: 10,
        Weight: 100,
      })

      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockTodaysWorkout,
          startWorkout: mockStartWorkout,
          exercises: [{ Id: 123, Label: 'Bench Press', sets: [] }],
          workoutSession: { id: 'test-session' },
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      await act(async () => {
        await result.current.handleExerciseClick(123)
      })

      expect(mockUpdateExerciseWorkSets).toHaveBeenCalledWith(123, [])
      expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(123)
      expect(mockPush).toHaveBeenCalledWith('/workout/exercise/123')
    })

    it('should redirect to workout page when invalid exercise ID is clicked', async () => {
      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockTodaysWorkout,
          startWorkout: mockStartWorkout,
          exercises: [{ Id: 123, Label: 'Bench Press', sets: [] }],
          workoutSession: { id: 'test-session' },
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      // Test with exercise ID 0
      await act(async () => {
        await result.current.handleExerciseClick(0)
      })

      expect(mockPush).toHaveBeenCalledWith('/workout')
      expect(mockUpdateExerciseWorkSets).not.toHaveBeenCalled()
      expect(mockLoadExerciseRecommendation).not.toHaveBeenCalled()
    })
  })

  describe('handleFinishWorkout', () => {
    it('should finish workout and navigate to complete page', async () => {
      mockFinishWorkout.mockResolvedValue(true)

      const { result } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockTodaysWorkout,
          startWorkout: mockStartWorkout,
          exercises: [],
          workoutSession: { id: 'test-session' },
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      await act(async () => {
        await result.current.handleFinishWorkout()
      })

      expect(mockFinishWorkout).toHaveBeenCalled()
      expect(mockPush).toHaveBeenCalledWith('/workout/complete')
    })
  })

  describe('button labels', () => {
    it('should return correct button labels based on workout state', () => {
      // No workout session
      const { result: result1 } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockTodaysWorkout,
          startWorkout: mockStartWorkout,
          exercises: [],
          workoutSession: null,
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      expect(result1.current.getButtonLabel()).toBe('Start Workout')
      expect(result1.current.getButtonAriaLabel()).toBe(
        'Start a new workout session'
      )

      // Active workout session without completed sets
      const { result: result2 } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockTodaysWorkout,
          startWorkout: mockStartWorkout,
          exercises: [],
          workoutSession: { exercises: [] },
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      expect(result2.current.getButtonLabel()).toBe('Continue Workout')
      expect(result2.current.getButtonAriaLabel()).toBe(
        'Continue your current workout'
      )

      // Active workout session with completed sets
      const { result: result3 } = renderHook(() =>
        useWorkoutActions({
          todaysWorkout: mockTodaysWorkout,
          startWorkout: mockStartWorkout,
          exercises: [],
          workoutSession: {
            exercises: [{ sets: [{ reps: 10, weight: 100 }] }],
          },
          loadExerciseRecommendation: mockLoadExerciseRecommendation,
          updateExerciseWorkSets: mockUpdateExerciseWorkSets,
          finishWorkout: mockFinishWorkout,
        })
      )

      expect(result3.current.getButtonLabel()).toBe('Finish and save workout')
      expect(result3.current.getButtonAriaLabel()).toBe(
        'Finish and save workout'
      )
    })
  })
})
