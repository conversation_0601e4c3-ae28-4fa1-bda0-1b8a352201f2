import { render } from '@testing-library/react'
import { vi } from 'vitest'
import ProgramPage from '../page'

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    prefetch: vi.fn(),
  }),
}))

// Mock all dependencies
vi.mock('@/hooks/useProgramWithCalculationsAndCache', () => ({
  useProgramWithCalculationsAndCache: () => ({
    program: { id: 1, name: 'Test Program', description: 'Test' },
    progress: { percentage: 50 },
    isLoading: false,
    isRefreshing: false,
    error: null,
    refetch: vi.fn(),
    hasPartialDataError: false,
  }),
}))

vi.mock('@/hooks/useUserStats', () => ({
  useUserStats: () => ({
    stats: {
      weekStreak: 5,
      workoutsCompleted: 42,
      lbsLifted: 12500,
    },
    isLoading: false,
    error: null,
    refetch: vi.fn(),
  }),
}))

vi.mock('@/hooks/useUserInfo', () => ({
  useUserInfo: () => ({
    refetch: vi.fn(),
  }),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    exercises: [],
    preloadRecommendations: vi.fn(),
    loadingStates: new Map(),
  }),
}))

vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    isRefreshing: false,
    pullDistance: 0,
    isPulling: false,
  }),
}))

vi.mock('@/hooks/useHaptic', () => ({
  useHaptic: () => ({
    trigger: vi.fn(),
    withHandler: (fn: () => void) => fn,
  }),
}))

vi.mock('@/components/AuthGuard', () => ({
  AuthGuard: ({ children }: { children: React.ReactNode }) => children,
}))

vi.mock('@/components/program/ProgramErrorBoundary', () => ({
  ProgramErrorBoundary: ({ children }: { children: React.ReactNode }) =>
    children,
}))

vi.mock('@/components/program/ProgramStats', () => ({
  ProgramStats: () => <div data-testid="program-stats">Stats</div>,
}))

vi.mock('@/components/ui/FloatingCTAButton', () => ({
  FloatingCTAButton: ({ onClick }: { onClick: () => void }) => (
    <button data-testid="floating-cta-button" onClick={onClick}>
      Start Workout
    </button>
  ),
}))

vi.mock('@/components/ProgramDescription', () => ({
  ProgramDescription: () => <div>Program Description</div>,
}))

vi.mock('@/components/PullToRefreshIndicator', () => ({
  PullToRefreshIndicator: () => null,
}))

vi.mock('@/components/SuccessAnimation', () => ({
  SuccessAnimation: () => null,
}))

vi.mock('@/components/ui/ScreenReaderAnnouncer', () => ({
  ScreenReaderAnnouncer: () => null,
  useScreenReaderAnnouncer: () => ({
    announcement: '',
    announce: vi.fn(),
  }),
}))

describe('ProgramPage V2 Styling', () => {
  it('applies V2 background styling with bg-surface-primary', () => {
    const { container } = render(<ProgramPage />)

    const pageContainer = container.querySelector(
      '[data-testid="program-overview-page"]'
    )
    expect(pageContainer).toHaveClass('bg-surface-primary')

    // Should not have the old bg-bg-primary class
    expect(pageContainer).not.toHaveClass('bg-bg-primary')
  })

  it('applies V2 container styling with minimal padding', () => {
    const { container } = render(<ProgramPage />)

    const scrollContainer = container.querySelector(
      '[data-testid="scroll-container"]'
    )
    const contentWrapper = scrollContainer?.querySelector('div > div')

    // Should have minimal padding like Exercise V2
    expect(contentWrapper).toHaveClass('px-4')
    expect(contentWrapper).toHaveClass('pt-2')

    // Should not have the old generous padding
    expect(contentWrapper).not.toHaveClass('p-4')
  })

  it('removes max-width constraint for full mobile width usage', () => {
    const { container } = render(<ProgramPage />)

    // Should not find any element with max-w-lg class
    const maxWidthElement = container.querySelector('.max-w-lg')
    expect(maxWidthElement).not.toBeInTheDocument()
  })

  it('applies compact spacing between sections', () => {
    const { container } = render(<ProgramPage />)

    const sectionsContainer = container.querySelector('.space-y-6')

    // Should have more compact spacing
    expect(sectionsContainer).not.toBeInTheDocument()

    // Should find space-y-4 instead
    const compactContainer = container.querySelector('.space-y-4')
    expect(compactContainer).toBeInTheDocument()
  })

  it('removes loading message section to rely on SwipeableStatCard loading', () => {
    const { container } = render(<ProgramPage />)

    // Should not find any element with the loading message text
    const loadingText = container.textContent || ''
    expect(loadingText).not.toContain('Loading your workout...')
  })
})
