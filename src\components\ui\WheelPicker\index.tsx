'use client'

import { useEffect, useMemo } from 'react'
import { vibrate } from '@/utils/haptics'
import { useWheelPickerDrag } from './useWheelPickerDrag'
import { injectWheelPickerStyles } from './WheelPickerStyles'

interface WheelPickerProps {
  value: number
  onChange: (value: number) => void
  min: number
  max: number
  increment: number
  label?: string
  disabled?: boolean
  testId?: string
}

function getValueClassName(isCenter: boolean, distance: number): string {
  if (isCenter) {
    return 'text-6xl font-bold text-text-primary scale-100'
  }

  switch (distance) {
    case 1:
      return 'text-3xl text-text-secondary scale-90'
    case 2:
      return 'text-2xl text-text-tertiary scale-80'
    default:
      return 'text-xl text-text-tertiary scale-70'
  }
}

export function WheelPicker({
  value,
  onChange,
  min,
  max,
  increment,
  label,
  disabled = false,
  testId,
}: WheelPickerProps) {
  // Generate visible values (3 on each side of current value)
  const visibleValues = useMemo(() => {
    const values: number[] = []
    const sideCount = 3

    // Left side values
    for (let i = sideCount; i > 0; i--) {
      const val = value - i * increment
      if (val >= min) {
        values.push(val)
      }
    }

    // Current value
    values.push(value)

    // Right side values
    for (let i = 1; i <= sideCount; i++) {
      const val = value + i * increment
      if (val <= max) {
        values.push(val)
      }
    }

    return values
  }, [value, increment, min, max])

  // Use drag hook
  const {
    containerRef,
    isDragging,
    handleMouseDown,
    handleTouchStart,
    handleMouseMove,
    handleTouchMove,
    handleEnd,
    handleScroll,
  } = useWheelPickerDrag(value, onChange, visibleValues, disabled)

  // Handle value selection
  const handleValueClick = (newValue: number) => {
    if (!disabled && newValue !== value) {
      vibrate('light')
      onChange(newValue)
    }
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return

    if (e.key === 'ArrowRight') {
      e.preventDefault()
      const newValue = Math.min(value + increment, max)
      if (newValue !== value) {
        vibrate('light')
        onChange(newValue)
      }
    } else if (e.key === 'ArrowLeft') {
      e.preventDefault()
      const newValue = Math.max(value - increment, min)
      if (newValue !== value) {
        vibrate('light')
        onChange(newValue)
      }
    }
  }

  // Center the current value on mount and value change
  useEffect(() => {
    if (containerRef.current) {
      const container = containerRef.current
      const centerIndex = visibleValues.indexOf(value)
      if (centerIndex !== -1) {
        const itemWidth = container.scrollWidth / visibleValues.length
        const scrollPosition =
          centerIndex * itemWidth - container.clientWidth / 2 + itemWidth / 2
        container.scrollLeft = Math.max(0, scrollPosition)
      }
    }
  }, [value, visibleValues, containerRef])

  // Inject styles on mount
  useEffect(() => {
    injectWheelPickerStyles()
  }, [])

  return (
    <div
      className="relative w-full"
      data-testid={testId}
      role="spinbutton"
      aria-label={label}
      aria-valuenow={value}
      aria-valuemin={min}
      aria-valuemax={max}
      tabIndex={0}
      onKeyDown={handleKeyDown}
    >
      <div
        ref={containerRef}
        className={`
          flex items-center gap-4 overflow-x-auto scrollbar-hide
          ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}
          ${disabled ? 'opacity-50' : ''}
        `}
        style={{ touchAction: 'pan-x' }}
        data-testid="wheel-picker-container"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleEnd}
        onMouseLeave={handleEnd}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleEnd}
        onScroll={handleScroll}
      >
        {visibleValues.map((val, index) => {
          const isCenter = val === value
          const distance = Math.abs(visibleValues.indexOf(value) - index)

          return (
            <button
              key={val}
              onClick={() => handleValueClick(val)}
              disabled={disabled}
              data-testid={`wheel-value-${val}`}
              className={`
                flex-shrink-0 px-4 py-3 rounded-lg transition-all min-h-[44px]
                ${getValueClassName(isCenter, distance)}
                ${!disabled && !isCenter ? 'hover:text-text-secondary' : ''}
              `}
            >
              {val % 1 === 0 ? val : val.toFixed(1)}
            </button>
          )
        })}
      </div>

      {/* Center indicator line (visual guide) */}
      <div
        className="absolute top-0 left-1/2 transform -translate-x-1/2 w-px h-full bg-text-tertiary opacity-20 pointer-events-none"
        data-testid="wheel-picker-center"
      >
        <span className="sr-only">{value}</span>
      </div>
    </div>
  )
}
