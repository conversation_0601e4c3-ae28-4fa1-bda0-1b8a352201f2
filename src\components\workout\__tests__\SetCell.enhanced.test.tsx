import { render, screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import { SetCell } from '../SetCell'
import type { ExerciseModel } from '@/types'

describe('SetCell - Enhanced Features with Arrows', () => {
  const mockExercise: ExerciseModel = {
    Id: 123,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsSystemExercise: true,
    BodyPartId: 1,
  }

  const mockOnRepsChange = vi.fn()
  const mockOnWeightChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Arrow Display for Active Set', () => {
    it('should display up/down arrows when set is active (isNext=true)', () => {
      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={20}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
        />
      )

      // Check for up arrows above values
      const upArrows = screen.getAllByLabelText(/Increase/)
      expect(upArrows).toHaveLength(2) // One for reps, one for weight

      // Check for down arrows below values
      const downArrows = screen.getAllByLabelText(/Decrease/)
      expect(downArrows).toHaveLength(2) // One for reps, one for weight
    })

    it('should NOT display arrows when set is inactive', () => {
      render(
        <SetCell
          setNo={2}
          reps={10}
          weight={135}
          isNext={false}
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
        />
      )

      // Should not find any arrow buttons
      expect(screen.queryByLabelText(/Increase/)).not.toBeInTheDocument()
      expect(screen.queryByLabelText(/Decrease/)).not.toBeInTheDocument()
    })
  })

  describe('Arrow Tap Functionality', () => {
    it('should increment reps by 1 when up arrow is clicked', () => {
      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={20}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
        />
      )

      const repsUpArrow = screen.getByLabelText('Increase reps')
      fireEvent.click(repsUpArrow)

      expect(mockOnRepsChange).toHaveBeenCalledWith(13)
    })

    it('should decrement reps by 1 when down arrow is clicked', () => {
      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={20}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
        />
      )

      const repsDownArrow = screen.getByLabelText('Decrease reps')
      fireEvent.click(repsDownArrow)

      expect(mockOnRepsChange).toHaveBeenCalledWith(11)
    })

    it('should increment weight by 2.5 lbs when up arrow is clicked', () => {
      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={20}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
        />
      )

      const weightUpArrow = screen.getByLabelText('Increase weight')
      fireEvent.click(weightUpArrow)

      expect(mockOnWeightChange).toHaveBeenCalledWith(22.5)
    })

    it('should increment weight by 1 kg when up arrow is clicked (metric)', () => {
      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={20}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="kg"
          exercise={mockExercise}
        />
      )

      const weightUpArrow = screen.getByLabelText('Increase weight')
      fireEvent.click(weightUpArrow)

      expect(mockOnWeightChange).toHaveBeenCalledWith(21)
    })

    it('should not allow reps to go below 1', () => {
      render(
        <SetCell
          setNo={1}
          reps={1}
          weight={20}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
        />
      )

      const repsDownArrow = screen.getByLabelText('Decrease reps')
      fireEvent.click(repsDownArrow)

      // Should not call with 0 or negative
      expect(mockOnRepsChange).not.toHaveBeenCalled()
    })

    it('should allow weight to go to 0', () => {
      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={2.5}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
        />
      )

      const weightDownArrow = screen.getByLabelText('Decrease weight')
      fireEvent.click(weightDownArrow)

      // Should allow weight to go to 0
      expect(mockOnWeightChange).toHaveBeenCalledWith(0)
    })

    it('should not allow weight to go below 0', () => {
      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={2}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
        />
      )

      const weightDownArrow = screen.getByLabelText('Decrease weight')
      fireEvent.click(weightDownArrow)

      // Should call with 0 when weight would go negative (2 - 2.5 = -0.5)
      expect(mockOnWeightChange).toHaveBeenCalledWith(0)
    })

    it('should use custom increment from recommendation when available', () => {
      const mockRecommendation = {
        ExerciseId: 1,
        Weight: { Lb: 100, Kg: 45 },
        Reps: 10,
        Increments: { Lb: 10, Kg: 5 },
      }

      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={100}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
          recommendation={mockRecommendation}
        />
      )

      const weightUpArrow = screen.getByLabelText('Increase weight')
      fireEvent.click(weightUpArrow)

      expect(mockOnWeightChange).toHaveBeenCalledWith(110)
    })

    it('should use custom increment from recommendation for kg', () => {
      const mockRecommendation = {
        ExerciseId: 1,
        Weight: { Lb: 100, Kg: 45 },
        Reps: 10,
        Increments: { Lb: 10, Kg: 5 },
      }

      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={45}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="kg"
          exercise={mockExercise}
          recommendation={mockRecommendation}
        />
      )

      const weightUpArrow = screen.getByLabelText('Increase weight')
      fireEvent.click(weightUpArrow)

      expect(mockOnWeightChange).toHaveBeenCalledWith(50)
    })
  })

  describe('Visual Indicators', () => {
    it('should show chevron up icon for up arrows', () => {
      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={20}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
        />
      )

      // Check for chevron up icons (could be SVG or text)
      const upArrows = screen.getAllByLabelText(/Increase/)
      upArrows.forEach((arrow) => {
        expect(arrow).toBeInTheDocument()
        // Could check for specific SVG path or class
        expect(arrow).toHaveAttribute('aria-label')
      })
    })

    it('should show chevron down icon for down arrows', () => {
      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={20}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
        />
      )

      // Check for chevron down icons
      const downArrows = screen.getAllByLabelText(/Decrease/)
      downArrows.forEach((arrow) => {
        expect(arrow).toBeInTheDocument()
        expect(arrow).toHaveAttribute('aria-label')
      })
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels for all arrow buttons', () => {
      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={20}
          isNext
          isFinished={false}
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
        />
      )

      expect(screen.getByLabelText('Increase reps')).toBeInTheDocument()
      expect(screen.getByLabelText('Decrease reps')).toBeInTheDocument()
      expect(screen.getByLabelText('Increase weight')).toBeInTheDocument()
      expect(screen.getByLabelText('Decrease weight')).toBeInTheDocument()
    })

    it('should disable arrow buttons when inputs are disabled', () => {
      render(
        <SetCell
          setNo={1}
          reps={12}
          weight={20}
          isNext
          isFinished // Finished sets have disabled inputs
          isBodyweight={false}
          onRepsChange={mockOnRepsChange}
          onWeightChange={mockOnWeightChange}
          unit="lbs"
          exercise={mockExercise}
        />
      )

      // Arrows should not be shown for finished sets
      expect(screen.queryByLabelText(/Increase/)).not.toBeInTheDocument()
      expect(screen.queryByLabelText(/Decrease/)).not.toBeInTheDocument()
    })
  })
})
