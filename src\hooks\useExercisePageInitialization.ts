/**
 * Custom hook for exercise page initialization logic
 * Extracted from ExercisePageClient to reduce file size and improve maintainability
 */

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { debugLog } from '@/utils/debugLog'

export interface ExercisePageInitializationState {
  isInitializing: boolean
  loadingError: Error | null
  retryInitialization: () => Promise<void>
}

export function useExercisePageInitialization(
  exerciseId: number
): ExercisePageInitializationState {
  const router = useRouter()
  const [isInitializing, setIsInitializing] = useState(true)
  const [loadingError, setLoadingError] = useState<Error | null>(null)

  const {
    todaysWorkout,
    isLoadingWorkout,
    startWorkout,
    exercises,
    workoutSession,
    loadRecommendation,
    updateExerciseWorkSets,
  } = useWorkout()

  const {
    setCurrentExerciseById,
    loadingStates,
    getCachedExerciseRecommendation,
  } = useWorkoutStore()

  // Retry function for manual initialization
  const retryInitialization = async () => {
    try {
      setIsInitializing(true)
      setLoadingError(null)

      // Start workout if needed
      if (!workoutSession && todaysWorkout && !isLoadingWorkout) {
        const workoutGroup = todaysWorkout[0]
        const workout = workoutGroup?.WorkoutTemplates?.[0]

        if (workout) {
          const result = await startWorkout(todaysWorkout)
          if (!result.success) {
            debugLog.error('Failed to start workout')
            router.replace('/workout')
            return
          }
        } else {
          router.replace('/workout')
          return
        }
      }

      // Set current exercise and load recommendations
      if (exerciseId) {
        // Skip validation if exercises haven't been loaded yet or if still loading workout
        // Also skip if we don't have a workout session yet (still initializing)
        // IMPORTANT: Keep initializing state until we have confirmed workout data
        if (
          !exercises ||
          exercises.length === 0 ||
          isLoadingWorkout ||
          !workoutSession
        ) {
          debugLog.log(
            '[useExercisePageInitialization] Exercises not loaded yet, workout still loading, or no session, skipping validation',
            {
              hasExercises: !!exercises,
              exercisesLength: exercises?.length || 0,
              isLoadingWorkout,
              hasWorkoutSession: !!workoutSession,
            }
          )
          // Don't mark as complete yet - we're still waiting for data
          return
        }

        // First validate that the exercise exists in the workout
        debugLog.log('[useExercisePageInitialization] Validating exercise:', {
          exerciseId,
          exercisesLength: exercises?.length,
          exerciseIds: exercises?.map((ex) => ex.Id),
          exerciseIdType: typeof exerciseId,
        })

        const exercise = exercises?.find(
          (ex) => Number(ex.Id) === Number(exerciseId)
        )

        if (!exercise) {
          // Exercise not found in workout
          debugLog.error(
            `Exercise ${exerciseId} not found in workout exercises`,
            {
              exerciseId,
              exerciseIdType: typeof exerciseId,
              availableExercises: exercises?.map((ex) => ({
                id: ex.Id,
                label: ex.Label,
                idType: typeof ex.Id,
              })),
            }
          )
          throw new Error(`Exercise ${exerciseId} not found in workout`)
        }

        setCurrentExerciseById(exerciseId)

        // Check if recommendation is loaded for this exercise
        const hasRecommendation = getCachedExerciseRecommendation(exerciseId)
        const isLoadingRecommendation = loadingStates.get(exerciseId)

        // If no recommendation and not loading, trigger loading
        if (!hasRecommendation && !isLoadingRecommendation) {
          loadRecommendation(exerciseId, exercise.Label || 'Exercise')
        }

        // Pre-load recommendation if not already loaded using alternative method
        if (!exercise.sets || exercise.sets.length === 0) {
          updateExerciseWorkSets(exerciseId, [])
        }
      }
    } catch (error) {
      debugLog.error('Failed to retry initialization:', error)
      setLoadingError(
        error instanceof Error
          ? error
          : new Error('Failed to retry initialization')
      )
    } finally {
      setIsInitializing(false)
    }
  }

  // Effect 1: Start workout if needed
  useEffect(() => {
    async function startWorkoutIfNeeded() {
      try {
        setLoadingError(null)

        // Guard: Skip workout start if session already exists
        if (workoutSession) {
          debugLog(
            '✅ [ExercisePageClient] Workout session already exists, skipping workout start',
            {
              sessionId: workoutSession.id,
            }
          )
          // Don't set isInitializing to false here - let the exercise setup effect handle it
          return
        }

        if (todaysWorkout && !isLoadingWorkout) {
          debugLog('🚀 [ExercisePageClient] Starting workout...', {
            todaysWorkout,
          })

          const workoutGroup = todaysWorkout[0]
          const workout = workoutGroup?.WorkoutTemplates?.[0]

          if (workout) {
            const result = await startWorkout(todaysWorkout)
            if (result.success) {
              debugLog('✅ [ExercisePageClient] Workout started successfully')
            } else {
              debugLog.error('Failed to start workout in initialization')
            }
          } else {
            debugLog.error(
              '❌ [ExercisePageClient] No workout template found, redirecting...'
            )
            router.replace('/workout')
          }
        }
      } catch (error) {
        debugLog.error('Failed to start workout:', error)
        setLoadingError(
          error instanceof Error ? error : new Error('Failed to start workout')
        )
      }
    }

    startWorkoutIfNeeded()
  }, [workoutSession, todaysWorkout, isLoadingWorkout, startWorkout, router])

  // Effect 2: Set current exercise and load recommendations
  useEffect(() => {
    async function setupExercise() {
      try {
        if (!exerciseId) return

        debugLog('🎯 [ExercisePageClient] Setting up exercise', {
          exerciseId,
          hasExercises: !!exercises,
          exercisesCount: exercises?.length || 0,
          hasWorkoutSession: !!workoutSession,
          isInitializing,
        })

        // Skip validation if exercises haven't been loaded yet or if still loading workout
        // Also skip if we're still in the process of starting a workout (no session yet)
        // CRITICAL: Don't mark initialization complete until we have valid data
        if (
          !exercises ||
          exercises.length === 0 ||
          isLoadingWorkout ||
          (!workoutSession && todaysWorkout)
        ) {
          debugLog.log(
            '⏳ [ExercisePageClient] Exercises not loaded yet, workout still loading, or session being created, waiting...',
            {
              hasExercises: !!exercises,
              exercisesLength: exercises?.length || 0,
              isLoadingWorkout,
              hasWorkoutSession: !!workoutSession,
              hasTodaysWorkout: !!todaysWorkout,
            }
          )
          // Keep isInitializing true - don't mark complete yet
          return
        }

        // First validate that the exercise exists in the workout
        const exercise = exercises?.find(
          (ex) => Number(ex.Id) === Number(exerciseId)
        )

        if (!exercise) {
          debugLog.error(
            '❌ [ExercisePageClient] Exercise not found in exercises list',
            {
              exerciseId,
              availableIds: exercises?.map((ex) => ex.Id),
            }
          )
          throw new Error(`Exercise ${exerciseId} not found in workout`)
        }

        // Set current exercise by ID
        setCurrentExerciseById(exerciseId)

        // Check if recommendation is loaded for this exercise
        const hasRecommendation = getCachedExerciseRecommendation(exerciseId)
        const isLoadingRecommendation = loadingStates.get(exerciseId)

        debugLog('💭 [ExercisePageClient] Recommendation status', {
          exerciseId,
          hasRecommendation: !!hasRecommendation,
          isLoadingRecommendation,
        })

        // If no recommendation and not loading, trigger loading
        if (!hasRecommendation && !isLoadingRecommendation) {
          debugLog('📡 [ExercisePageClient] Loading recommendation...', {
            exerciseId,
            exerciseLabel: exercise.Label,
          })
          loadRecommendation(exerciseId, exercise.Label || 'Exercise')
        }

        // Pre-load recommendation if not already loaded using alternative method
        if (!exercise.sets || exercise.sets.length === 0) {
          debugLog(
            '🔧 [ExercisePageClient] Updating exercise work sets to empty array'
          )
          updateExerciseWorkSets(exerciseId, [])
        }
      } catch (error) {
        debugLog.error('Failed to setup exercise:', error)
        setLoadingError(
          error instanceof Error ? error : new Error('Failed to setup exercise')
        )
        // Mark as complete on error
        setIsInitializing(false)
      } finally {
        // Only mark initialization as complete if we have validated the exercise
        if (exercises && exercises.length > 0 && workoutSession) {
          setIsInitializing(false)
        }
      }
    }

    setupExercise()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    exerciseId,
    exercises?.length, // Only depend on length to avoid array reference changes
    workoutSession, // Add workoutSession to ensure setup runs after session is ready
    isLoadingWorkout, // Add loading state to ensure we re-check when loading completes
    todaysWorkout, // Add todaysWorkout to re-run when workout data is available
    // Removed function dependencies that are recreated on every render
    // setCurrentExerciseById, loadingStates, loadRecommendation, updateExerciseWorkSets
    // These are stable functions from the store and don't need to be in dependencies
  ])

  return {
    isInitializing,
    loadingError,
    retryInitialization,
  }
}
