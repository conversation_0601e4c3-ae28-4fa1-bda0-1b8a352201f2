import { useCallback, useMemo } from 'react'
import { useWorkoutStore } from '@/stores/workoutStore/index'
// import { recommendationCache } from '@/services/cache/recommendationCache'
import { logger } from '@/utils/logger'
import {
  getExerciseRecommendation,
  // generateRecommendationCacheKey,
} from '@/services/api/workout'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import { getUserSettings, shouldUseRestPause } from '@/services/userSettings'
import type { RecommendationModel } from '@/types'

// Track pending recommendation requests to avoid duplicates
const pendingRecommendationRequests = new Map<
  number,
  Promise<RecommendationModel | null>
>()

// Clear pending requests for testing
export const clearPendingRequests = () => {
  pendingRecommendationRequests.clear()
}

export function useWorkoutRecommendations() {
  const {
    getCachedExerciseRecommendation,
    setCachedExerciseRecommendation,
    loadAllExerciseRecommendations,
    currentWorkout,
    getSwapForExercise,
  } = useWorkoutStore()

  const loadRecommendation = useCallback(
    async (
      exerciseId: number,
      exerciseName: string
    ): Promise<RecommendationModel | null> => {
      logger.log('🎯 [useWorkoutRecommendations] loadRecommendation called', {
        exerciseId,
        exerciseName,
        currentWorkoutId: currentWorkout?.Id,
        hasCurrentWorkout: !!currentWorkout,
      })

      logger.log('[loadRecommendation] Starting for exercise', {
        exerciseId,
        exerciseName,
      })

      const userEmail = getCurrentUserEmail()
      if (!userEmail) {
        logger.error('❌ [useWorkoutRecommendations] No user email found')
        logger.warn('[loadRecommendation] No user email found')
        return null
      }

      // TODO: Fix generateRecommendationCacheKey to accept 2 args
      // const cacheKey = `${userEmail}:${exerciseId}`

      // Check cache first
      const cached = getCachedExerciseRecommendation(exerciseId)
      if (cached) {
        logger.log(
          '💾 [useWorkoutRecommendations] Using cached recommendation',
          {
            exerciseId,
            cached,
          }
        )
        // Using cached recommendation
        return cached
      }

      // Check if a request is already pending
      const pending = pendingRecommendationRequests.get(exerciseId)
      if (pending) {
        logger.log('⏳ [useWorkoutRecommendations] Request already pending', {
          exerciseId,
        })
        // Request already pending
        return pending
      }
      // Create the request promise
      const requestPromise: Promise<RecommendationModel | null> = (async () => {
        try {
          // Only log initial fetch in development
          logger.log('🔄 [useWorkoutRecommendations] Fetching from API', {
            exerciseId,
            workoutId: currentWorkout?.Id,
          })
          // Get workout ID from current workout
          const workoutId = currentWorkout?.Id
          if (!workoutId) {
            logger.error(
              '❌ [useWorkoutRecommendations] No current workout ID available'
            )
            logger.warn('[loadRecommendation] No current workout ID available')
            return null
          }

          // Find the exercise to get its SetStyle and IsFlexibility
          const exercise = currentWorkout?.Exercises?.find(
            (ex) => ex.Id === exerciseId
          )
          if (!exercise) {
            // Only log in development once
            if (process.env.NODE_ENV === 'development') {
              logger.warn(`Exercise ${exerciseId} not found in current workout`)
            }
            return null
          }

          // Get user settings for the recommendation request
          const userSettings = await getUserSettings()

          // Check if this exercise was swapped to get the original exercise ID
          const swapContext = getSwapForExercise(workoutId, exerciseId)
          const originalExerciseId = swapContext?.sourceExerciseId

          // Determine SetStyle based on user preferences and exercise type
          let setStyle = exercise.SetStyle || 'Normal' // Start with exercise default

          // Override with user preference if rest-pause should be used
          const useRestPause = await shouldUseRestPause(exercise)
          if (useRestPause) {
            setStyle = 'RestPause'
          }

          // Create the request object for getExerciseRecommendation following mobile app pattern
          const request = {
            Username: userEmail,
            ExerciseId: exerciseId,
            WorkoutId: workoutId,
            SetStyle: setStyle,
            IsFlexibility: exercise.IsFlexibility || false, // Default to false if not specified
            IsQuickMode: userSettings.isQuickMode,
            LightSessionDays: userSettings.lightSessionDays,
            SwapedExId: originalExerciseId, // Pass original exercise ID if this was swapped
            IsStrengthPhashe: userSettings.isStrengthPhase, // Note: API has typo
            IsFreePlan: userSettings.isFreePlan,
            IsFirstWorkoutOfStrengthPhase:
              userSettings.isFirstWorkoutOfStrengthPhase,
            VersionNo: 1, // API version number
          }

          // Add timeout to prevent hanging requests
          const timeoutPromise = new Promise<RecommendationModel | null>(
            (resolve) => {
              setTimeout(
                () => resolve(null), // Return null on timeout instead of rejecting
                30000
              ) // 30 second timeout
            }
          )

          const recommendation = await Promise.race([
            getExerciseRecommendation(request),
            timeoutPromise,
          ])

          // Only log successful responses in development
          if (recommendation) {
            logger.log(
              '📥 [useWorkoutRecommendations] Received recommendation for exercise',
              exerciseId
            )
          }

          // Always update the cache, even for null results
          // This prevents repeated API calls for exercises without recommendations
          setCachedExerciseRecommendation(exerciseId, recommendation)

          if (recommendation) {
            // Update recommendationCache
            // TODO: Fix recommendationCache.set signature
            // recommendationCache.set(cacheKey, recommendation)
          }

          return recommendation
        } catch (error) {
          // Handle different types of errors with appropriate logging
          const isNetworkError =
            error instanceof Error &&
            (error.message.includes('Network Error') ||
              error.message.includes('Failed to fetch') ||
              error.message.includes('ERR_CONNECTION_REFUSED'))

          const is404Error =
            error instanceof Error && error.message.includes('404')

          if (is404Error) {
            // 404 errors are expected for exercises without history - log as info only
            logger.info(
              '[loadRecommendation] No recommendation available (404)',
              {
                exerciseId,
              }
            )
            // Cache null to prevent repeated 404 requests
            setCachedExerciseRecommendation(exerciseId, null)
          } else if (isNetworkError) {
            // Network errors should be logged as warnings
            logger.warn(
              '🌐 [useWorkoutRecommendations] Network error fetching recommendation',
              { exerciseId, error: error.message }
            )
            // Network error - likely offline
          } else if (
            process.env.NODE_ENV === 'development' &&
            error instanceof Error &&
            !error.message.includes('401')
          ) {
            // Log other errors only once in development
            logger.error('Failed to fetch recommendation', {
              exerciseId,
              error: error.message,
            })
          }
          return null
        } finally {
          // Remove from pending requests
          pendingRecommendationRequests.delete(exerciseId)
        }
      })()

      // Track the pending request
      pendingRecommendationRequests.set(exerciseId, requestPromise)

      return requestPromise
    },
    [
      getCachedExerciseRecommendation,
      setCachedExerciseRecommendation,
      currentWorkout,
      getSwapForExercise,
      // Using refs for auth state and workoutId to prevent dependency issues
      // eslint-disable-next-line react-hooks/exhaustive-deps
    ]
  )

  const preloadRecommendations = useCallback(
    async (exercises: Array<{ Id: number; Label: string }>) => {
      // Starting preload for exercises

      const promises = exercises.map((exercise) =>
        loadRecommendation(exercise.Id, exercise.Label)
      )

      const results = await Promise.allSettled(promises)

      const successful = results.filter(
        (r) => r.status === 'fulfilled' && r.value !== null
      ).length

      // Preload completed

      return successful
    },
    [loadRecommendation]
  )

  const invalidateRecommendation = useCallback(
    (exerciseId: number) => {
      const userEmail = getCurrentUserEmail()
      if (!userEmail) return

      // TODO: Fix generateRecommendationCacheKey to accept 2 args
      // const cacheKey = `${userEmail}:${exerciseId}`
      // TODO: Fix delete method
      // recommendationCache.delete(cacheKey)

      // Also clear from store
      setCachedExerciseRecommendation(exerciseId, null)
    },
    [setCachedExerciseRecommendation]
  )

  return useMemo(
    () => ({
      loadRecommendation,
      preloadRecommendations,
      invalidateRecommendation,
      loadAllExerciseRecommendations,
    }),
    [
      loadRecommendation,
      preloadRecommendations,
      invalidateRecommendation,
      loadAllExerciseRecommendations,
    ]
  )
}
