import { render, screen, fireEvent, act } from '@testing-library/react'
import { beforeEach, describe, expect, it, vi, afterEach } from 'vitest'
import { CurrentSetCard } from '../CurrentSetCard'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Mock haptics utility
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

describe('CurrentSetCard - Fade Animation', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Name: 'Test Exercise',
    Equipment: 'Dumbbell',
    DefaultTargetReps: 10,
    DefaultTargetWeight: { Kg: 20, Lb: 44 },
  } as ExerciseModel

  const mockCurrentSet: WorkoutLogSerieModel = {
    Id: -1,
    Reps: 10,
    Weight: { Kg: 20, Lb: 44 },
    IsWarmups: false,
    SetTitle: 'Work Set',
  } as WorkoutLogSerieModel

  const mockSetData = { reps: 10, weight: 44, duration: 0 }
  const mockOnSetDataChange = vi.fn()
  const mockOnComplete = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should apply fade-out animation class when save button is clicked', async () => {
    render(
      <CurrentSetCard
        exercise={mockExercise}
        currentSet={mockCurrentSet}
        setData={mockSetData}
        onSetDataChange={mockOnSetDataChange}
        onComplete={mockOnComplete}
        isSaving={false}
        completedSets={0}
        unit="lbs"
      />
    )

    const saveButton = screen.getByText('Save set')
    const inputContainer = screen.getByTestId('input-controls-container')

    // Initially no fade animation classes
    expect(inputContainer).not.toHaveClass('fade-out')
    expect(inputContainer).not.toHaveClass('fade-in')

    // Click save button
    fireEvent.click(saveButton)

    // Should immediately apply fade-out class
    expect(inputContainer).toHaveClass('fade-out')
  })

  it('should transition to fade-in animation after 200ms', async () => {
    render(
      <CurrentSetCard
        exercise={mockExercise}
        currentSet={mockCurrentSet}
        setData={mockSetData}
        onSetDataChange={mockOnSetDataChange}
        onComplete={mockOnComplete}
        isSaving={false}
        completedSets={0}
        unit="lbs"
      />
    )

    const saveButton = screen.getByText('Save set')
    const inputContainer = screen.getByTestId('input-controls-container')

    fireEvent.click(saveButton)

    // Fast forward 200ms to transition to fade-in
    await act(async () => {
      vi.advanceTimersByTime(200)
    })

    expect(inputContainer).toHaveClass('fade-in')
    expect(inputContainer).not.toHaveClass('fade-out')
  })

  it('should call onComplete after fade animation completes', async () => {
    render(
      <CurrentSetCard
        exercise={mockExercise}
        currentSet={mockCurrentSet}
        setData={mockSetData}
        onSetDataChange={mockOnSetDataChange}
        onComplete={mockOnComplete}
        isSaving={false}
        completedSets={0}
        unit="lbs"
      />
    )

    const saveButton = screen.getByText('Save set')

    fireEvent.click(saveButton)

    // Fast forward through fade-out (200ms) + fade-in (200ms)
    await act(async () => {
      vi.advanceTimersByTime(400)
    })

    expect(mockOnComplete).toHaveBeenCalledTimes(1)
  })

  it('should prevent multiple animations when button is clicked rapidly', async () => {
    render(
      <CurrentSetCard
        exercise={mockExercise}
        currentSet={mockCurrentSet}
        setData={mockSetData}
        onSetDataChange={mockOnSetDataChange}
        onComplete={mockOnComplete}
        isSaving={false}
        completedSets={0}
        unit="lbs"
      />
    )

    const saveButton = screen.getByText('Save set')

    // Click button multiple times rapidly
    fireEvent.click(saveButton)
    fireEvent.click(saveButton)
    fireEvent.click(saveButton)

    // Fast forward through full animation
    await act(async () => {
      vi.advanceTimersByTime(400)
    })

    // Should only complete once
    expect(mockOnComplete).toHaveBeenCalledTimes(1)
  })

  it('should not trigger animation when button is disabled', () => {
    render(
      <CurrentSetCard
        exercise={mockExercise}
        currentSet={mockCurrentSet}
        setData={mockSetData}
        onSetDataChange={mockOnSetDataChange}
        onComplete={mockOnComplete}
        isSaving // Button should be disabled
        completedSets={0}
        unit="lbs"
      />
    )

    const saveButton = screen.getByText('Save set')
    const inputContainer = screen.getByTestId('input-controls-container')

    fireEvent.click(saveButton)

    // Should not apply animation classes when disabled
    expect(inputContainer).not.toHaveClass('fade-out')
    expect(inputContainer).not.toHaveClass('fade-in')
  })

  it('should complete animation even if component unmounts', async () => {
    const { unmount } = render(
      <CurrentSetCard
        exercise={mockExercise}
        currentSet={mockCurrentSet}
        setData={mockSetData}
        onSetDataChange={mockOnSetDataChange}
        onComplete={mockOnComplete}
        isSaving={false}
        completedSets={0}
        unit="lbs"
      />
    )

    const saveButton = screen.getByText('Save set')
    fireEvent.click(saveButton)

    // Unmount component during animation
    unmount()

    // Fast forward through animation
    vi.advanceTimersByTime(400)

    // Should not throw errors or cause memory leaks
    expect(() => {
      vi.runAllTimers()
    }).not.toThrow()
  })
})
