/**
 * Cache setter actions for the workout store
 */

import type { WorkoutState } from './types'
import type {
  WorkoutTemplateModel,
  WorkoutTemplateGroupModel,
  RecommendationModel,
} from '@/types'
import type { GetUserWorkoutProgramTimeZoneInfoResponse } from '@/services/api/workout-types'
import {
  CACHE_VERSION,
  MAX_RECOMMENDATIONS,
  initialCachedData,
} from './constants'
import {
  isValidUserProgramInfo,
  isValidWorkoutTemplateArray,
  isValidRecommendation,
} from './validators'
import { debugLog } from '@/utils/debugLog'

export const createSetCacheActions = (
  set: (
    partial:
      | Partial<WorkoutState>
      | ((state: WorkoutState) => Partial<WorkoutState>)
  ) => void,
  get: () => WorkoutState
) => ({
  setCachedUserProgramInfo: (
    data: GetUserWorkoutProgramTimeZoneInfoResponse | null
  ) => {
    if (data !== null && !isValidUserProgramInfo(data)) {
      console.warn('Invalid user program info data, not caching')
      return
    }

    set((state) => ({
      cachedData: {
        ...state.cachedData,
        userProgramInfo: data,
        lastUpdated: {
          ...state.cachedData.lastUpdated,
          userProgramInfo: Date.now(),
        },
      },
    }))
  },

  setCachedUserWorkouts: (data: WorkoutTemplateModel[] | null) => {
    if (data !== null && !isValidWorkoutTemplateArray(data)) {
      // Only log detailed error in development
      const dataArray = Array.isArray(data) ? data : []
      debugLog.warn('Invalid workout templates data, not caching:', {
        dataType: typeof data,
        isArray: Array.isArray(data),
        length: dataArray.length || 'N/A',
        firstItem: dataArray.length > 0 ? dataArray[0] : 'N/A',
      })
      return
    }

    set((state) => ({
      cachedData: {
        ...state.cachedData,
        userWorkouts: data,
        lastUpdated: {
          ...state.cachedData.lastUpdated,
          userWorkouts: Date.now(),
        },
      },
    }))
  },

  setCachedTodaysWorkout: (data: WorkoutTemplateGroupModel[] | null) => {
    set((state) => ({
      cachedData: {
        ...state.cachedData,
        todaysWorkout: data,
        lastUpdated: {
          ...state.cachedData.lastUpdated,
          todaysWorkout: Date.now(),
        },
      },
    }))
  },

  setCachedExerciseRecommendation: (
    exerciseId: number,
    data: RecommendationModel | null
  ) => {
    // Allow null values to cache 404 responses and prevent repeated requests
    if (data !== null && !isValidRecommendation(data)) {
      console.warn(
        `Invalid recommendation data for exercise ${exerciseId}, not caching`
      )
      return
    }

    set((state) => {
      const recommendations = {
        ...state.cachedData.exerciseRecommendations,
      }
      const timestamps = {
        ...state.cachedData.lastUpdated.exerciseRecommendations,
      }

      // Enforce max recommendations limit
      const recommendationCount = Object.keys(recommendations).length
      if (
        recommendationCount >= MAX_RECOMMENDATIONS &&
        !recommendations[exerciseId]
      ) {
        // Remove oldest recommendation
        const sortedEntries = Object.entries(timestamps).sort(
          ([, a], [, b]) => a - b
        )
        if (sortedEntries.length > 0 && sortedEntries[0]) {
          const [oldestId] = sortedEntries[0]
          delete recommendations[Number(oldestId)]
          delete timestamps[Number(oldestId)]
        }
      }

      recommendations[exerciseId] = data
      timestamps[exerciseId] = Date.now()

      return {
        cachedData: {
          ...state.cachedData,
          exerciseRecommendations: recommendations,
          lastUpdated: {
            ...state.cachedData.lastUpdated,
            exerciseRecommendations: timestamps,
          },
        },
      }
    })
  },

  setHasHydrated: (hydrated: boolean) => {
    if (hydrated && !get().hasHydrated) {
      // Track hydration time
      const hydrationTime = performance.now()
      set((state) => ({
        hasHydrated: hydrated,
        cacheStats: {
          ...state.cacheStats,
          hydrationTime,
        },
      }))
    } else {
      set({ hasHydrated: hydrated })
    }
  },

  handleCacheVersionMismatch: () => {
    // Clear all cached data when version mismatch occurs
    set({
      cachedData: {
        userProgramInfo: null,
        userWorkouts: null,
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: 0,
          userWorkouts: 0,
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
      cacheVersion: CACHE_VERSION,
    })
  },

  clearExpiredCache: () => {
    const { cachedData, isCacheStale } = get()

    // Clear expired main caches
    const newCachedData = { ...cachedData }

    if (isCacheStale('userProgramInfo')) {
      newCachedData.userProgramInfo = null
      newCachedData.lastUpdated.userProgramInfo = 0
    }

    if (isCacheStale('userWorkouts')) {
      newCachedData.userWorkouts = null
      newCachedData.lastUpdated.userWorkouts = 0
    }

    if (isCacheStale('todaysWorkout')) {
      newCachedData.todaysWorkout = null
      newCachedData.lastUpdated.todaysWorkout = 0
    }

    // Clear expired exercise recommendations
    const newRecommendations = { ...newCachedData.exerciseRecommendations }
    const newTimestamps = {
      ...newCachedData.lastUpdated.exerciseRecommendations,
    }

    Object.keys(newRecommendations).forEach((exerciseIdStr) => {
      const exerciseId = Number(exerciseIdStr)
      if (isCacheStale('exerciseRecommendation', exerciseId)) {
        delete newRecommendations[exerciseId]
        delete newTimestamps[exerciseId]
      }
    })

    newCachedData.exerciseRecommendations = newRecommendations
    newCachedData.lastUpdated.exerciseRecommendations = newTimestamps

    set({ cachedData: newCachedData })
  },

  clearAllCache: () => {
    set({
      cachedData: initialCachedData,
      cacheStats: {
        hits: 0,
        misses: 0,
        hitRate: 0,
        operationCount: 0,
        averageLatency: 0,
        totalLatency: 0,
        totalSize: 0,
        itemCount: 0,
        oldestDataAge: 0,
        freshDataCount: 0,
        staleDataCount: 0,
        hydrationTime: 0,
      },
    })
  },
})
