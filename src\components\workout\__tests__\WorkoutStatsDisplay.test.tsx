import { describe, it, expect } from 'vitest'
import { calculateWorkoutStats } from '../WorkoutStatsDisplay'
import type { WorkoutSession } from '@/types'

describe('calculateWorkoutStats', () => {
  describe('excluding warmup sets from calculations', () => {
    it('should exclude warmup sets from totalSets count', () => {
      // Test rationale: Verify that warmup sets are not counted in the total sets displayed to users
      const mockSession: WorkoutSession = {
        id: 'test-1',
        startTime: new Date(),
        exercises: [
          {
            exerciseId: 1,
            name: 'Bench Press',
            sets: [
              // Warmup sets
              {
                setNumber: 1,
                reps: 10,
                weight: { Lb: 45, Kg: 20 },
                isWarmup: true,
              },
              {
                setNumber: 2,
                reps: 8,
                weight: { Lb: 95, Kg: 43 },
                isWarmup: true,
              },
              // Work sets
              {
                setNumber: 3,
                reps: 10,
                weight: { Lb: 135, Kg: 61 },
                isWarmup: false,
                rir: 2,
              },
              {
                setNumber: 4,
                reps: 8,
                weight: { Lb: 155, Kg: 70 },
                isWarmup: false,
                rir: 1,
              },
            ],
          },
        ],
      }

      const stats = calculateWorkoutStats(mockSession)

      // Currently this test WILL FAIL because the implementation includes warmup sets
      // Expected: 2 (only work sets), Actual: 4 (includes warmup sets)
      expect(stats?.totalSets).toBe(2)
      expect(stats?.workingSets).toBe(2)
    })

    it('should exclude warmup sets from totalVolume calculation', () => {
      // Test rationale: Verify that warmup sets weight is not included in total volume
      const mockSession: WorkoutSession = {
        id: 'test-2',
        startTime: new Date(),
        exercises: [
          {
            exerciseId: 1,
            name: 'Squat',
            sets: [
              // Warmup set: 10 * 100 = 1000 lbs
              {
                setNumber: 1,
                reps: 10,
                weight: { Lb: 100, Kg: 45 },
                isWarmup: true,
              },
              // Work set: 5 * 200 = 1000 lbs
              {
                setNumber: 2,
                reps: 5,
                weight: { Lb: 200, Kg: 90 },
                isWarmup: false,
              },
            ],
          },
        ],
      }

      const stats = calculateWorkoutStats(mockSession)

      // Currently this test WILL FAIL because the implementation includes warmup sets
      // Expected: 1000 (only work set), Actual: 2000 (includes warmup set)
      expect(stats?.totalVolume).toBe(1000)
    })

    it('should return 0 totalSets when all sets are warmups', () => {
      // Test rationale: Edge case where workout has only warmup sets
      const mockSession: WorkoutSession = {
        id: 'test-3',
        startTime: new Date(),
        exercises: [
          {
            exerciseId: 1,
            name: 'Deadlift',
            sets: [
              {
                setNumber: 1,
                reps: 5,
                weight: { Lb: 135, Kg: 61 },
                isWarmup: true,
              },
              {
                setNumber: 2,
                reps: 3,
                weight: { Lb: 225, Kg: 102 },
                isWarmup: true,
              },
            ],
          },
        ],
      }

      const stats = calculateWorkoutStats(mockSession)

      // Currently this test WILL FAIL
      expect(stats?.totalSets).toBe(0)
      expect(stats?.totalVolume).toBe(0)
    })

    it('should include all sets when there are no warmup sets', () => {
      // Test rationale: Verify normal behavior when no warmup sets exist
      const mockSession: WorkoutSession = {
        id: 'test-4',
        startTime: new Date(),
        exercises: [
          {
            exerciseId: 1,
            name: 'Bench Press',
            sets: [
              {
                setNumber: 1,
                reps: 10,
                weight: { Lb: 135, Kg: 61 },
                isWarmup: false,
              },
              {
                setNumber: 2,
                reps: 8,
                weight: { Lb: 155, Kg: 70 },
                isWarmup: false,
              },
            ],
          },
        ],
      }

      const stats = calculateWorkoutStats(mockSession)

      // This test should PASS with current implementation
      expect(stats?.totalSets).toBe(2)
      expect(stats?.totalVolume).toBe(10 * 135 + 8 * 155) // 1350 + 1240 = 2590
    })

    it('should handle multiple exercises with mixed warmup and work sets', () => {
      // Test rationale: Real-world scenario with multiple exercises
      const mockSession: WorkoutSession = {
        id: 'test-5',
        startTime: new Date(),
        exercises: [
          {
            exerciseId: 1,
            name: 'Bench Press',
            sets: [
              {
                setNumber: 1,
                reps: 10,
                weight: { Lb: 45, Kg: 20 },
                isWarmup: true,
              },
              {
                setNumber: 2,
                reps: 10,
                weight: { Lb: 135, Kg: 61 },
                isWarmup: false,
              },
            ],
          },
          {
            exerciseId: 2,
            name: 'Incline Press',
            sets: [
              {
                setNumber: 1,
                reps: 8,
                weight: { Lb: 95, Kg: 43 },
                isWarmup: true,
              },
              {
                setNumber: 2,
                reps: 8,
                weight: { Lb: 115, Kg: 52 },
                isWarmup: false,
              },
              {
                setNumber: 3,
                reps: 6,
                weight: { Lb: 125, Kg: 57 },
                isWarmup: false,
              },
            ],
          },
        ],
      }

      const stats = calculateWorkoutStats(mockSession)

      // Expected: 3 work sets total (1 from bench, 2 from incline)
      expect(stats?.totalSets).toBe(3)
      // Expected volume: (10*135) + (8*115) + (6*125) = 1350 + 920 + 750 = 3020
      expect(stats?.totalVolume).toBe(3020)
    })

    it('should handle null session gracefully', () => {
      // Test rationale: Edge case handling
      const stats = calculateWorkoutStats(null)
      expect(stats).toBeNull()
    })

    it('should handle empty exercises array', () => {
      // Test rationale: Edge case with valid session but no exercises
      const mockSession: WorkoutSession = {
        id: 'test-6',
        startTime: new Date(),
        exercises: [],
      }

      const stats = calculateWorkoutStats(mockSession)

      expect(stats?.totalExercises).toBe(0)
      expect(stats?.totalSets).toBe(0)
      expect(stats?.workingSets).toBe(0)
      expect(stats?.totalVolume).toBe(0)
    })

    it('should handle bodyweight exercises with 0 weight correctly', () => {
      // Test rationale: Bodyweight exercises may have 0 weight
      const mockSession: WorkoutSession = {
        id: 'test-7',
        startTime: new Date(),
        exercises: [
          {
            exerciseId: 1,
            name: 'Pull-ups',
            sets: [
              {
                setNumber: 1,
                reps: 5,
                weight: { Lb: 0, Kg: 0 },
                isWarmup: true,
              },
              {
                setNumber: 2,
                reps: 10,
                weight: { Lb: 0, Kg: 0 },
                isWarmup: false,
              },
            ],
          },
        ],
      }

      const stats = calculateWorkoutStats(mockSession)

      expect(stats?.totalSets).toBe(1) // Only work set
      expect(stats?.totalVolume).toBe(0) // 10 * 0 = 0
    })
  })

  describe('existing functionality preservation', () => {
    it('should calculate workingSets correctly', () => {
      // Test rationale: Ensure existing workingSets calculation is not affected
      const mockSession: WorkoutSession = {
        id: 'test-8',
        startTime: new Date(),
        exercises: [
          {
            exerciseId: 1,
            name: 'Squat',
            sets: [
              {
                setNumber: 1,
                reps: 10,
                weight: { Lb: 135, Kg: 61 },
                isWarmup: true,
              },
              {
                setNumber: 2,
                reps: 5,
                weight: { Lb: 225, Kg: 102 },
                isWarmup: false,
              },
              {
                setNumber: 3,
                reps: 5,
                weight: { Lb: 225, Kg: 102 },
                isWarmup: false,
              },
            ],
          },
        ],
      }

      const stats = calculateWorkoutStats(mockSession)

      expect(stats?.workingSets).toBe(2) // This should already work correctly
    })

    it('should calculate average RIR correctly', () => {
      // Test rationale: Ensure RIR calculation is not affected by warmup exclusion
      const mockSession: WorkoutSession = {
        id: 'test-9',
        startTime: new Date(),
        exercises: [
          {
            exerciseId: 1,
            name: 'Bench Press',
            sets: [
              {
                setNumber: 1,
                reps: 10,
                weight: { Lb: 135, Kg: 61 },
                isWarmup: true,
                // No RIR for warmup sets typically
              },
              {
                setNumber: 2,
                reps: 8,
                weight: { Lb: 185, Kg: 84 },
                isWarmup: false,
                rir: 2,
              },
              {
                setNumber: 3,
                reps: 6,
                weight: { Lb: 205, Kg: 93 },
                isWarmup: false,
                rir: 1,
              },
            ],
          },
        ],
      }

      const stats = calculateWorkoutStats(mockSession)

      expect(stats?.avgRir).toBe(1.5) // (2 + 1) / 2 = 1.5
    })

    it('should count totalExercises correctly', () => {
      // Test rationale: Exercise count should not be affected by set filtering
      const mockSession: WorkoutSession = {
        id: 'test-10',
        startTime: new Date(),
        exercises: [
          {
            exerciseId: 1,
            name: 'Bench Press',
            sets: [
              {
                setNumber: 1,
                reps: 10,
                weight: { Lb: 45, Kg: 20 },
                isWarmup: true,
              },
            ],
          },
          {
            exerciseId: 2,
            name: 'Squat',
            sets: [
              {
                setNumber: 1,
                reps: 5,
                weight: { Lb: 225, Kg: 102 },
                isWarmup: false,
              },
            ],
          },
        ],
      }

      const stats = calculateWorkoutStats(mockSession)

      expect(stats?.totalExercises).toBe(2)
    })
  })
})
