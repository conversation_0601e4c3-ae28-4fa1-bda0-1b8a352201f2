/**
 * Error boundary component for exercise page
 * Handles loading and workout errors with retry functionality
 */

import { useRouter } from 'next/navigation'

interface ExercisePageErrorBoundaryProps {
  error: Error
  onRetry: () => Promise<void>
}

export function ExercisePageErrorBoundary({
  error,
  onRetry,
}: ExercisePageErrorBoundaryProps) {
  const router = useRouter()

  // Check if this is a "not found in workout" error
  const isNotInWorkout = error.message.includes('not found in workout')

  // Check if it's an authentication error
  const isAuthError =
    error.name === 'AuthenticationError' ||
    error.message.toLowerCase().includes('authentication') ||
    error.message.toLowerCase().includes('session') ||
    error.message.toLowerCase().includes('expired')

  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh] p-4">
      <h2 className="text-xl font-semibold text-gray-800 mb-2">
        {(() => {
          if (isAuthError) return 'Session Expired'
          if (isNotInWorkout) return 'Exercise Not Available'
          return 'Loading Error'
        })()}
      </h2>
      <p className="text-gray-600 mb-4 text-center max-w-sm">
        {(() => {
          if (isAuthError) {
            return 'Your session has expired. Please log in again to continue.'
          }
          if (isNotInWorkout) {
            return 'This exercise is not part of your current workout. Please select an exercise from your assigned workout.'
          }
          return `Failed to load exercise: ${error.message}`
        })()}
      </p>
      {!isNotInWorkout && !isAuthError && (
        <button
          className="px-4 py-2 bg-blue-600 text-white rounded-lg mb-2 min-h-[44px]"
          onClick={onRetry}
        >
          Retry
        </button>
      )}
      <button
        className="px-4 py-2 bg-gray-600 text-white rounded-lg min-h-[44px]"
        onClick={() => {
          if (isAuthError) {
            router.push('/login')
          } else {
            router.push('/workout')
          }
        }}
      >
        {isAuthError ? 'Go to Login' : 'Back to Workout'}
      </button>
    </div>
  )
}

interface WorkoutErrorBoundaryProps {
  error: string | Error
}

export function WorkoutErrorBoundary({ error }: WorkoutErrorBoundaryProps) {
  const router = useRouter()

  const errorMessage =
    typeof error === 'string'
      ? error
      : error.message || 'Unable to load workout data.'

  // Check if it's an authentication error
  const isAuthError =
    (typeof error === 'object' && error.name === 'AuthenticationError') ||
    errorMessage.toLowerCase().includes('authentication') ||
    errorMessage.toLowerCase().includes('session') ||
    errorMessage.toLowerCase().includes('expired')

  return (
    <div className="flex items-center justify-center min-h-[100dvh] bg-gray-50">
      <div className="text-center p-6 max-w-md">
        <h2 className="text-2xl font-bold text-red-600 mb-4">
          {isAuthError ? 'Session Expired' : 'Failed to Load Workout'}
        </h2>
        <p className="text-gray-600 mb-6">
          {isAuthError
            ? 'Your session has expired. Please log in again to continue.'
            : errorMessage}
        </p>
        <button
          onClick={() => router.push(isAuthError ? '/login' : '/workout')}
          className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors min-h-[44px]"
        >
          {isAuthError ? 'Go to Login' : 'Back to Workout'}
        </button>
      </div>
    </div>
  )
}
