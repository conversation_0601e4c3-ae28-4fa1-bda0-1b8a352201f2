'use client'

import { RepsInput } from '@/components/workout/inputs/RepsInput'
import { WeightInput } from '@/components/workout/inputs/WeightInput'
import type { RecommendationModel } from '@/types'

interface CurrentSetCardControlsProps {
  setData: { reps: number; weight: number; duration: number }
  onSetDataChange: (data: {
    reps: number
    weight: number
    duration: number
  }) => void
  unit: 'kg' | 'lbs'
  isSaving: boolean
  animationClass: string
  recommendation?: RecommendationModel | null
}

export function CurrentSetCardControls({
  setData,
  onSetDataChange,
  unit,
  isSaving,
  animationClass,
  recommendation,
}: CurrentSetCardControlsProps) {
  const handleRepsChange = (value: string) => {
    const newReps = parseInt(value) || 0
    onSetDataChange({
      ...setData,
      reps: newReps,
    })
  }

  const handleWeightChange = (value: string) => {
    const newWeight = parseFloat(value) || 0
    onSetDataChange({
      ...setData,
      weight: newWeight,
    })
  }

  const handleRepsIncrement = () => {
    onSetDataChange({
      ...setData,
      reps: setData.reps + 1,
    })
  }

  const handleRepsDecrement = () => {
    onSetDataChange({
      ...setData,
      reps: Math.max(1, setData.reps - 1),
    })
  }

  const handleWeightIncrement = () => {
    // FORCE kg increments to 1, use API for lbs
    let increment: number
    if (unit === 'kg') {
      increment = 1 // Always use 1kg increment regardless of API
    } else if (recommendation?.Increments) {
      increment = recommendation.Increments.Lb
    } else {
      increment = 2.5 // Default lbs increment
    }

    onSetDataChange({
      ...setData,
      weight: setData.weight + increment,
    })
  }

  const handleWeightDecrement = () => {
    // FORCE kg increments to 1, use API for lbs
    let increment: number
    if (unit === 'kg') {
      increment = 1 // Always use 1kg increment regardless of API
    } else if (recommendation?.Increments) {
      increment = recommendation.Increments.Lb
    } else {
      increment = 2.5 // Default lbs increment
    }

    onSetDataChange({
      ...setData,
      weight: Math.max(0, setData.weight - increment),
    })
  }

  return (
    <div
      className={`flex flex-col gap-8 mb-12 ${animationClass}`}
      data-testid="input-controls-container"
    >
      {/* Reps control */}
      <div data-testid="reps-section">
        <RepsInput
          reps={setData.reps}
          onChange={handleRepsChange}
          onIncrement={handleRepsIncrement}
          onDecrement={handleRepsDecrement}
          disabled={isSaving}
        />
      </div>

      {/* Weight control */}
      <div data-testid="weight-section">
        <WeightInput
          weight={setData.weight}
          unit={unit}
          onChange={handleWeightChange}
          onIncrement={handleWeightIncrement}
          onDecrement={handleWeightDecrement}
          disabled={isSaving}
        />
      </div>
    </div>
  )
}
