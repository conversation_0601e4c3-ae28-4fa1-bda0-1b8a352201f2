import { describe, it, expect, vi, beforeEach } from 'vitest'
import { getExerciseRecommendation } from '../workout'
import { ExerciseHelpers } from '@/utils/exerciseHelpers'
import { getUserSettings } from '@/services/userSettings'
import type { RecommendationModel } from '@/types'
import type { GetRecommendationForExerciseRequest } from '../workout-types'

// Mock dependencies
vi.mock('@/services/userSettings')
vi.mock('@/lib/auth-utils', () => ({
  getCurrentUserEmail: vi.fn(() => '<EMAIL>'),
}))
vi.mock('@/api/client', () => ({
  apiClient: {
    post: vi.fn(),
  },
}))

const mockGetUserSettings = vi.mocked(getUserSettings)

describe('Workout Warmup Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Mock user settings
    mockGetUserSettings.mockResolvedValue({
      unit: 'lbs',
      barbellWeight: 45,
      bodyWeight: 180,
      availablePlates: 'all',
    })
  })

  describe('Warmup Calculation Integration', () => {
    it('should calculate warmups when API returns WarmupsCount but empty WarmUpsList', async () => {
      // Mock API response with warmups count but no warmup list
      const mockApiResponse = {
        data: {
          StatusCode: 200,
          Result: {
            WarmupsCount: 3,
            WarmUpsList: [], // Empty - should trigger local calculation
            Weight: { Kg: 100, Lb: 220 },
            Reps: 10,
            Increments: { Kg: 1, Lb: 2.5 },
            isPlateAvailable: true,
            IsBodyweight: false,
            ExerciseName: 'Bench Press',
          } as RecommendationModel,
        },
      }

      // Mock the API client
      const { apiClient } = await import('@/api/client')
      vi.mocked(apiClient.post).mockResolvedValue(mockApiResponse)

      const request: GetRecommendationForExerciseRequest = {
        ExerciseId: 123,
        WorkoutId: 456,
        Username: '<EMAIL>',
      }

      const result = await getExerciseRecommendation(request)

      expect(result).toBeDefined()
      expect(result?.WarmupsCount).toBe(3)
      expect(result?.WarmUpsList).toHaveLength(3)

      // Verify warmup progression
      const warmups = result?.WarmUpsList || []

      // First warmup should be bodyweight only
      expect(warmups[0].WarmUpWeightSet.Kg).toBe(0)
      expect(warmups[0].WarmUpWeightSet.Lb).toBe(0)

      // Second warmup should be around 50% of working weight
      expect(warmups[1].WarmUpWeightSet.Kg).toBeGreaterThan(40)
      expect(warmups[1].WarmUpWeightSet.Kg).toBeLessThan(70)

      // Third warmup should be around 85% of working weight
      expect(warmups[2].WarmUpWeightSet.Kg).toBeGreaterThan(80)
      expect(warmups[2].WarmUpWeightSet.Kg).toBeLessThan(90)

      // Reps should decrease
      expect(warmups[0].WarmUpReps).toBeGreaterThan(warmups[2].WarmUpReps)
    })

    it('should not calculate warmups when API provides WarmUpsList', async () => {
      // Mock API response with existing warmup list
      const existingWarmups = [
        {
          WarmUpReps: 10,
          WarmUpWeightSet: { Kg: 20, Lb: 44 },
          warmUpReps: 10,
          warmUpWeightSet: { Kg: 20, Lb: 44 },
        },
      ]

      const mockApiResponse = {
        data: {
          StatusCode: 200,
          Result: {
            WarmupsCount: 1,
            WarmUpsList: existingWarmups,
            Weight: { Kg: 100, Lb: 220 },
            Reps: 10,
          } as RecommendationModel,
        },
      }

      const { apiClient } = await import('@/api/client')
      vi.mocked(apiClient.post).mockResolvedValue(mockApiResponse)

      const request: GetRecommendationForExerciseRequest = {
        ExerciseId: 123,
        WorkoutId: 456,
        Username: '<EMAIL>',
      }

      const result = await getExerciseRecommendation(request)

      expect(result?.WarmUpsList).toEqual(existingWarmups)
    })

    it('should handle bodyweight exercises correctly', async () => {
      const mockApiResponse = {
        data: {
          StatusCode: 200,
          Result: {
            WarmupsCount: 2,
            WarmUpsList: [],
            Weight: { Kg: 80, Lb: 176 }, // User bodyweight
            Reps: 20,
            IsBodyweight: true,
            ExerciseName: 'Push-up',
          } as RecommendationModel,
        },
      }

      const { apiClient } = await import('@/api/client')
      vi.mocked(apiClient.post).mockResolvedValue(mockApiResponse)

      const request: GetRecommendationForExerciseRequest = {
        ExerciseId: 123,
        WorkoutId: 456,
        Username: '<EMAIL>',
      }

      const result = await getExerciseRecommendation(request)

      expect(result?.WarmUpsList).toHaveLength(2)

      const warmups = result?.WarmUpsList || []

      // For bodyweight exercises, weight should remain constant
      expect(warmups[0].WarmUpWeightSet.Kg).toBe(80)
      expect(warmups[1].WarmUpWeightSet.Kg).toBe(80)

      // Reps should progress from 40% to 60%
      expect(warmups[0].WarmUpReps).toBeLessThan(warmups[1].WarmUpReps)
    })

    it('should handle zero warmups count', async () => {
      const mockApiResponse = {
        data: {
          StatusCode: 200,
          Result: {
            WarmupsCount: 0,
            WarmUpsList: [],
            Weight: { Kg: 100, Lb: 220 },
            Reps: 10,
          } as RecommendationModel,
        },
      }

      const { apiClient } = await import('@/api/client')
      vi.mocked(apiClient.post).mockResolvedValue(mockApiResponse)

      const request: GetRecommendationForExerciseRequest = {
        ExerciseId: 123,
        WorkoutId: 456,
        Username: '<EMAIL>',
      }

      const result = await getExerciseRecommendation(request)

      expect(result?.WarmUpsList).toEqual([])
    })

    it('should handle API errors gracefully', async () => {
      const { apiClient } = await import('@/api/client')
      vi.mocked(apiClient.post).mockRejectedValue(new Error('API Error'))

      const request: GetRecommendationForExerciseRequest = {
        ExerciseId: 123,
        WorkoutId: 456,
        Username: '<EMAIL>',
      }

      // The function catches errors and returns null instead of throwing
      const result = await getExerciseRecommendation(request)
      expect(result).toBeNull()
    })
  })

  describe('Exercise Type Detection Integration', () => {
    it('should correctly identify exercise types for warmup calculation', () => {
      expect(ExerciseHelpers.getExerciseInfo('Bench Press').exerciseType).toBe(
        'barbell'
      )
      expect(
        ExerciseHelpers.getExerciseInfo('Dumbbell Press').exerciseType
      ).toBe('dumbbell')
      expect(ExerciseHelpers.getExerciseInfo('Push-up').exerciseType).toBe(
        'bodyweight'
      )
      expect(ExerciseHelpers.getExerciseInfo('Cable Row').exerciseType).toBe(
        'cable'
      )
    })

    it('should provide correct barbell weights for different exercises', () => {
      const userSettings = { unit: 'lbs' as const }

      expect(
        ExerciseHelpers.getBarbellWeight('Bench Press', userSettings)
      ).toBe(45)
      expect(
        ExerciseHelpers.getBarbellWeight('EZ Bar Curl', userSettings)
      ).toBe(15)
      expect(
        ExerciseHelpers.getBarbellWeight('Trap Bar Deadlift', userSettings)
      ).toBe(55)
    })

    it('should estimate plate availability correctly', () => {
      expect(ExerciseHelpers.estimatePlateAvailability('Bench Press')).toBe(
        true
      )
      expect(ExerciseHelpers.estimatePlateAvailability('Dumbbell Press')).toBe(
        false
      )
      expect(ExerciseHelpers.estimatePlateAvailability('Push-up')).toBe(false)
    })
  })
})
