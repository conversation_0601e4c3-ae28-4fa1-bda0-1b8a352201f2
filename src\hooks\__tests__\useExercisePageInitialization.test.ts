import { renderHook, act, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useExercisePageInitialization } from '../useExercisePageInitialization'
import { useRouter } from 'next/navigation'
import { useWorkout } from '../useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

vi.mock('../useWorkout', () => ({
  useWorkout: vi.fn(),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(),
}))

vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(
    vi.fn(() => {}), // Main function
    {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      info: vi.fn(),
    }
  ),
}))

describe('useExercisePageInitialization - Race Condition Fix', () => {
  const mockRouter = {
    replace: vi.fn(),
    push: vi.fn(),
  }

  const mockSetCurrentExerciseById = vi.fn()
  const mockLoadRecommendation = vi.fn()
  const mockUpdateExerciseWorkSets = vi.fn()
  const mockStartWorkout = vi.fn()
  const mockGetCachedExerciseRecommendation = vi.fn()

  const defaultWorkoutHookReturn = {
    todaysWorkout: null,
    isLoadingWorkout: false,
    startWorkout: mockStartWorkout,
    exercises: [],
    workoutSession: null,
    loadRecommendation: mockLoadRecommendation,
    updateExerciseWorkSets: mockUpdateExerciseWorkSets,
  }

  const defaultStoreReturn = {
    setCurrentExerciseById: mockSetCurrentExerciseById,
    loadingStates: new Map(),
    getCachedExerciseRecommendation: mockGetCachedExerciseRecommendation,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
    vi.mocked(useWorkout).mockReturnValue(defaultWorkoutHookReturn as any)
    vi.mocked(useWorkoutStore).mockReturnValue(defaultStoreReturn as any)
  })

  describe('Race Condition: Exercise validation before workout loads', () => {
    it('should remain in initializing state when exercises array is empty but workout is not loading', async () => {
      // This test verifies the race condition fix
      const exerciseId = 123

      // Simulate the race condition state:
      // - isLoadingWorkout is false (loading finished)
      // - exercises array is empty (data not yet populated)
      // - workoutSession exists (session started)
      vi.mocked(useWorkout).mockReturnValue({
        ...defaultWorkoutHookReturn,
        isLoadingWorkout: false,
        exercises: [], // Empty array - data not loaded yet
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
      } as any)

      const { result } = renderHook(() =>
        useExercisePageInitialization(exerciseId)
      )

      // Should remain initializing, not throw error
      expect(result.current.isInitializing).toBe(true)
      expect(result.current.loadingError).toBeNull()

      // Should NOT attempt to validate or throw error
      expect(mockSetCurrentExerciseById).not.toHaveBeenCalled()
      expect(mockRouter.replace).not.toHaveBeenCalled()
    })

    it('should validate exercise only after exercises array is populated', async () => {
      const exerciseId = 123
      const mockExercise = { Id: 123, Label: 'Bench Press' }

      // Start with empty exercises
      const { result, rerender } = renderHook(() =>
        useExercisePageInitialization(exerciseId)
      )

      // Initially should be initializing
      expect(result.current.isInitializing).toBe(true)

      // Simulate workout data loading completing
      vi.mocked(useWorkout).mockReturnValue({
        ...defaultWorkoutHookReturn,
        isLoadingWorkout: false,
        exercises: [mockExercise], // Now populated
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
        todaysWorkout: [{ WorkoutTemplates: [{ Id: 1 }] }] as any,
      } as any)

      // Trigger re-render with new data
      rerender()

      await waitFor(() => {
        expect(result.current.isInitializing).toBe(false)
        expect(result.current.loadingError).toBeNull()
      })

      // Should have validated and set current exercise
      expect(mockSetCurrentExerciseById).toHaveBeenCalledWith(exerciseId)
    })

    it('should handle invalid exercise ID after workout loads', async () => {
      const exerciseId = 999 // Not in workout
      const mockExercise = { Id: 123, Label: 'Bench Press' }

      // Start with loading state
      vi.mocked(useWorkout).mockReturnValue({
        ...defaultWorkoutHookReturn,
        isLoadingWorkout: true,
      } as any)

      const { result, rerender } = renderHook(() =>
        useExercisePageInitialization(exerciseId)
      )

      // Simulate workout data loading completing without the requested exercise
      vi.mocked(useWorkout).mockReturnValue({
        ...defaultWorkoutHookReturn,
        isLoadingWorkout: false,
        exercises: [mockExercise], // Different exercise ID
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
        todaysWorkout: [{ WorkoutTemplates: [{ Id: 1 }] }] as any,
      } as any)

      rerender()

      await waitFor(() => {
        expect(result.current.isInitializing).toBe(false)
        expect(result.current.loadingError).not.toBeNull()
        expect(result.current.loadingError?.message).toContain(
          'Exercise 999 not found in workout'
        )
      })

      // Should NOT have set current exercise
      expect(mockSetCurrentExerciseById).not.toHaveBeenCalled()
    })

    it('should not validate when todaysWorkout exists but no session (still starting)', async () => {
      const exerciseId = 123

      // Simulate state where workout data exists but session hasn't started yet
      vi.mocked(useWorkout).mockReturnValue({
        ...defaultWorkoutHookReturn,
        isLoadingWorkout: false,
        exercises: [],
        workoutSession: null, // No session yet
        todaysWorkout: [{ WorkoutTemplates: [{ Id: 1 }] }] as any, // But workout data exists
      } as any)

      const { result } = renderHook(() =>
        useExercisePageInitialization(exerciseId)
      )

      // Should skip validation when no session but workout exists
      expect(result.current.isInitializing).toBe(true)
      expect(result.current.loadingError).toBeNull()
      expect(mockSetCurrentExerciseById).not.toHaveBeenCalled()
    })
  })

  describe('Retry functionality', () => {
    it('should retry initialization on manual retry', async () => {
      const exerciseId = 123
      const mockExercise = { Id: 123, Label: 'Bench Press' }

      // Start with error state
      vi.mocked(useWorkout).mockReturnValue({
        ...defaultWorkoutHookReturn,
        isLoadingWorkout: false,
        exercises: [], // Empty - will cause validation to fail
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
        todaysWorkout: [{ WorkoutTemplates: [{ Id: 1 }] }] as any,
      } as any)

      const { result } = renderHook(() =>
        useExercisePageInitialization(exerciseId)
      )

      // Wait for initial error (when exercises finally load but don't contain our ID)
      vi.mocked(useWorkout).mockReturnValue({
        ...defaultWorkoutHookReturn,
        isLoadingWorkout: false,
        exercises: [{ Id: 456, Label: 'Squats' }], // Different exercise
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
        todaysWorkout: [{ WorkoutTemplates: [{ Id: 1 }] }] as any,
      } as any)

      // Update mock to have correct exercise for retry
      vi.mocked(useWorkout).mockReturnValue({
        ...defaultWorkoutHookReturn,
        isLoadingWorkout: false,
        exercises: [mockExercise], // Now has our exercise
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
        todaysWorkout: [{ WorkoutTemplates: [{ Id: 1 }] }] as any,
      } as any)

      // Retry
      await act(async () => {
        await result.current.retryInitialization()
      })

      expect(result.current.isInitializing).toBe(false)
      expect(result.current.loadingError).toBeNull()
      expect(mockSetCurrentExerciseById).toHaveBeenCalledWith(exerciseId)
    })
  })
})
