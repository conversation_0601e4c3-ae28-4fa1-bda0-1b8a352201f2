'use client'

import React, { useState } from 'react'
import type { SetType } from '@/utils/setTypeUtils'
import { formatWeightForDisplay } from '@/utils/weightUtils'
import { SetTypeExplainer } from './SetTypeExplainer'
import { Set<PERSON><PERSON>Header } from './SetCellHeader'
import { SetCellArrowButtons } from './SetCellArrowButtons'
import { SetCellActions } from './SetCellActions'
import { SetCellRow } from './SetCellRow'
import { useSetCellHandlers } from '@/hooks/useSetCellHandlers'

interface SetCellProps {
  isHeaderCell?: boolean
  setNo?: number | string
  reps?: number
  weight?: number
  isFinished?: boolean
  isBodyweight?: boolean
  isLastSet?: boolean
  isExerciseFinished?: boolean
  isNext?: boolean
  backColor?: string
  weightSingal?: string
  setType?: SetType
  setData?: {
    IsBackOffSet?: boolean
    IsWarmups?: boolean
    IsFinished?: boolean
    IsEditing?: boolean
    IsFirstWorkSet?: boolean
  } // WorkoutLogSerieModelRef data for MAUI-style handlers
  exercise?: {
    Id?: number
    Name?: string
    IsBodyweight?: boolean
  } // ExerciseModel for 1RM calculations
  recommendation?: {
    ExerciseId?: number
    Weight?: { Lb: number; Kg: number }
    Reps?: number
    Series?: number
    Increments?: { Lb: number; Kg: number }
  } // RecommendationModel for 1RM calculations
  userBodyWeight?: number
  onRepsChange?: (reps: number) => void
  onWeightChange?: (weight: number) => void
  onSetComplete?: () => void
  onFinishExercise?: () => void
  onAddSet?: () => void
  onOneRMUpdate?: (data: {
    weight: number
    reps: number
    exercise?: {
      Id?: number
      Name?: string
      IsBodyweight?: boolean
    }
    recommendation?: {
      ExerciseId?: number
      Weight?: { Lb: number; Kg: number }
      Reps?: number
      Series?: number
      Increments?: { Lb: number; Kg: number }
    }
    isKg: boolean
    userBodyWeight: number
    isFirstWorkSet?: boolean
  }) => void
  unit?: 'kg' | 'lbs'
}

export function SetCell({
  isHeaderCell = false,
  setNo = 1,
  reps = 0,
  weight = 0,
  isFinished = false,
  isBodyweight = false,
  isLastSet = false,
  isExerciseFinished = false,
  isNext = false,
  backColor = 'transparent',
  weightSingal,
  setType = 'Normal',
  setData,
  exercise,
  recommendation,
  userBodyWeight = 80,
  onRepsChange,
  onWeightChange,
  onSetComplete,
  onFinishExercise,
  onAddSet,
  onOneRMUpdate,
  unit = 'lbs',
}: SetCellProps) {
  const [isExplainerOpen, setIsExplainerOpen] = useState(false)

  const {
    handleRepsChange,
    handleWeightChange,
    handleRepsArrowClick,
    handleWeightArrowClick,
  } = useSetCellHandlers({
    reps,
    weight,
    exercise,
    recommendation,
    setData,
    userBodyWeight,
    unit,
    isNext,
    isFinished,
    isBodyweight,
    onRepsChange,
    onWeightChange,
    onOneRMUpdate,
  })

  // Format weight display (matching MAUI WeightSingal logic)
  const getFormattedWeight = () => {
    if (weightSingal) return weightSingal
    if (isBodyweight) return '0'

    // Get the increment for the current unit
    let increment: number
    if (recommendation?.Increments) {
      increment =
        unit === 'kg'
          ? recommendation.Increments.Kg
          : recommendation.Increments.Lb
    } else {
      increment = unit === 'kg' ? 1 : 2.5 // Default increments
    }

    // Format weight with proper decimals
    return formatWeightForDisplay(weight, increment)
  }

  /**
   * Get dynamic background color based on set state
   * Matches MAUI BackColor property logic but uses web app theme colors
   */
  const getBackgroundColor = () => {
    if (backColor && backColor !== 'transparent') {
      return backColor
    }

    // Web app theme-aware background colors
    if (isNext) {
      return 'bg-brand-primary/10' // Highlight next set
    }

    if (isFinished) {
      return 'bg-success/5' // Subtle success background for completed sets
    }

    return '' // Default transparent
  }

  /**
   * Get dynamic styling classes based on set state
   */
  const getSetStateClasses = () => {
    const baseClasses = 'px-4'
    const backgroundClass = getBackgroundColor()

    let stateClasses = ''

    if (isNext) {
      stateClasses += ' ring-2 ring-brand-primary'
    }

    if (isFinished) {
      stateClasses += ' opacity-75'
    }

    return `${baseClasses} ${backgroundClass} ${stateClasses}`.trim()
  }

  if (isHeaderCell) {
    return <SetCellHeader unit={unit} />
  }

  return (
    <div className={getSetStateClasses()}>
      {/* Set Type Explainer */}
      <SetTypeExplainer setType={setType} isOpen={isExplainerOpen} />

      {/* Arrow buttons above inputs for active set */}
      <SetCellArrowButtons
        isActive={isNext && !isFinished}
        onRepsUp={() => handleRepsArrowClick('up')}
        onRepsDown={() => handleRepsArrowClick('down')}
        onWeightUp={() => handleWeightArrowClick('up')}
        onWeightDown={() => handleWeightArrowClick('down')}
        position="above"
      />

      {/* Main Set Row */}
      <SetCellRow
        setNo={setNo}
        reps={reps}
        isFinished={isFinished}
        isBodyweight={isBodyweight}
        setType={setType}
        onRepsChange={handleRepsChange}
        onWeightChange={handleWeightChange}
        onSetComplete={onSetComplete}
        onSetTypeBadgeClick={() => setIsExplainerOpen(!isExplainerOpen)}
        getFormattedWeight={getFormattedWeight}
      />

      {/* Arrow buttons below inputs for active set */}
      <SetCellArrowButtons
        isActive={isNext && !isFinished}
        onRepsUp={() => handleRepsArrowClick('up')}
        onRepsDown={() => handleRepsArrowClick('down')}
        onWeightUp={() => handleWeightArrowClick('up')}
        onWeightDown={() => handleWeightArrowClick('down')}
        position="below"
      />

      {/* Action buttons */}
      <SetCellActions
        isLastSet={isLastSet}
        isFinished={isFinished}
        isExerciseFinished={isExerciseFinished}
        onFinishExercise={onFinishExercise}
        onAddSet={onAddSet}
      />
    </div>
  )
}
