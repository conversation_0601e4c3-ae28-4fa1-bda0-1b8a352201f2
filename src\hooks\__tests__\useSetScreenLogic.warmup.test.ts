import { renderHook } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useSetScreenLogic } from '../useSetScreenLogic'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import type { RecommendationModel } from '@/types'
import React from 'react'

// Mock dependencies
vi.mock('@/stores/workoutStore')
vi.mock('@/stores/authStore', () => ({
  useAuthStore: Object.assign(
    () => ({
      getCachedUserInfo: () => ({ MassUnit: 'lbs' }),
    }),
    {
      getState: () => ({
        getCachedUserInfo: () => ({ MassUnit: 'lbs' }),
      }),
    }
  ),
}))
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
  }),
}))
vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    saveSet: vi.fn(),
    isLoading: false,
    error: null,
    getRecommendation: vi.fn().mockResolvedValue(null),
  }),
}))
vi.mock('@/hooks/useRIR', () => ({
  useRIR: () => ({
    mapRIRValueToNumber: vi.fn(),
    saveRIR: vi.fn(),
  }),
}))

describe('useSetScreenLogic - Warmup Weight Filtering', () => {
  let queryClient: QueryClient

  const wrapper = ({ children }: { children: React.ReactNode }) =>
    React.createElement(QueryClientProvider, { client: queryClient }, children)

  const mockRecommendation: RecommendationModel = {
    ExerciseId: 1,
    Weight: { Lb: 135, Kg: 61.2 },
    Reps: 10,
    Series: 3,
    WarmupsCount: 2,
    WarmUpsList: [
      {
        WarmUpReps: 5,
        WarmUpWeightSet: { Lb: 95, Kg: 43.1 },
      },
      {
        WarmUpReps: 3,
        WarmUpWeightSet: { Lb: 115, Kg: 52.2 },
      },
    ],
  } as RecommendationModel

  beforeEach(() => {
    vi.clearAllMocks()
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })

    // Setup workout store mock
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: [{ Id: 1, Label: 'Bench Press' }],
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      workoutSession: {
        id: 'test-session',
        exercises: [
          {
            exerciseId: 1,
            sets: [
              // Warmup with 0 weight from API
              { reps: 5, weight: 0, isWarmup: true, setNumber: 1 },
              // Warmup with actual weight
              { reps: 3, weight: 115, isWarmup: true, setNumber: 2 },
              // Work set
              { reps: 10, weight: 135, isWarmup: false, setNumber: 3 },
            ],
          },
        ],
      },
      getCachedExerciseRecommendation: () => mockRecommendation,
      setCurrentExerciseById: vi.fn(),
      nextSet: vi.fn(),
      nextExercise: vi.fn(),
      setCurrentSetIndex: vi.fn(),
    } as any)
  })

  it('should filter out API warmup sets with 0 weight', () => {
    const { result } = renderHook(() => useSetScreenLogic(1), { wrapper })

    // completedSets should not include the warmup with 0 weight
    expect(result.current.completedSets).toHaveLength(2) // Only warmup with weight and work set
    expect(result.current.completedSets[0].Weight?.Lb).toBe(115)
    expect(result.current.completedSets[1].Weight?.Lb).toBe(135)
  })

  it('should preserve warmup weight from recommendation when API returns 0', () => {
    const { result } = renderHook(() => useSetScreenLogic(1), { wrapper })

    // The filtered warmup should get weight from recommendation
    const completedWarmups = result.current.completedSets.filter(
      (s) => s.IsWarmups
    )
    expect(completedWarmups).toHaveLength(1)

    // If this was the first warmup (now filtered), when regenerated it should use recommendation
    // This test verifies the filtering logic works
  })

  it('should update form data when current set index changes', async () => {
    const mockSetCurrentSetIndex = vi.fn()
    vi.mocked(useWorkoutStore).mockReturnValue({
      ...vi.mocked(useWorkoutStore)(),
      currentSetIndex: 0,
      setCurrentSetIndex: mockSetCurrentSetIndex,
    } as any)

    const { result, rerender } = renderHook(() => useSetScreenLogic(1), {
      wrapper,
    })

    // Initial form data should match first set
    expect(result.current.setData.reps).toBe(5) // First warmup reps from recommendation
    expect(result.current.setData.weight).toBe(95) // First warmup weight

    // Change to work set index
    vi.mocked(useWorkoutStore).mockReturnValue({
      ...vi.mocked(useWorkoutStore)(),
      currentSetIndex: 2, // Work set index
      setCurrentSetIndex: mockSetCurrentSetIndex,
    } as any)

    rerender()

    // Form data should update to work set values
    expect(result.current.setData.reps).toBe(10)
    expect(result.current.setData.weight).toBe(135)
  })

  it('should respect user mass unit preference in completed sets', () => {
    // Override auth store mock for kg preference
    vi.mocked(useAuthStore).getState = () => ({
      getCachedUserInfo: () => ({ MassUnit: 'kg' }),
    })

    // Also need to update the workoutSession to have kg weights
    vi.mocked(useWorkoutStore).mockReturnValue({
      ...vi.mocked(useWorkoutStore)(),
      workoutSession: {
        id: 'test-session',
        exercises: [
          {
            exerciseId: 1,
            sets: [
              // Work set with kg weight
              { reps: 10, weight: 61.2, isWarmup: false, setNumber: 3 },
            ],
          },
        ],
      },
    } as any)

    const { result } = renderHook(() => useSetScreenLogic(1), { wrapper })

    // Completed sets should have weight in kg with proper conversion
    const workSet = result.current.completedSets.find((s) => !s.IsWarmups)
    expect(workSet?.Weight?.Kg).toBeCloseTo(61.2, 1) // Weight is already in kg
    expect(workSet?.Weight?.Lb).toBeCloseTo(135, 0) // Converted to lbs
  })

  it('should handle warmup sets with fallback weight correctly', () => {
    // Add a saved warmup that had weight stripped by API
    vi.mocked(useWorkoutStore).mockReturnValue({
      ...vi.mocked(useWorkoutStore)(),
      workoutSession: {
        id: 'test-session',
        exercises: [
          {
            exerciseId: 1,
            sets: [
              // Saved warmup - API stripped weight
              { reps: 5, weight: 0, isWarmup: true, setNumber: 1, rir: null },
            ],
          },
        ],
      },
    } as any)

    const { result } = renderHook(() => useSetScreenLogic(1), { wrapper })

    // Should filter out the 0-weight warmup
    expect(result.current.completedSets).toHaveLength(0)
  })
})
