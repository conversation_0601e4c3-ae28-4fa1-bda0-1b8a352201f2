import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import React from 'react'
import { IOSNavigationBar } from '../IOSNavigationBar'

// Mock usePathname
vi.mock('next/navigation', () => ({
  usePathname: () => '/workout/exercise/1',
}))

describe('IOSNavigationBar - Auto-scroll for Long Exercise Names', () => {
  beforeEach(() => {
    // Mock getBoundingClientRect to simulate container width
    Element.prototype.getBoundingClientRect = vi.fn(() => ({
      width: 200, // Simulate container width
      height: 0,
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      x: 0,
      y: 0,
      toJSON: () => {},
    }))

    // Mock clientWidth and scrollWidth for long text detection
    Object.defineProperty(Element.prototype, 'clientWidth', {
      configurable: true,
      value: 200, // Container width
    })

    Object.defineProperty(Element.prototype, 'scrollWidth', {
      configurable: true,
      value: 400, // Simulated long text width
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should auto-scroll long exercise names like TV news ticker', () => {
    // Given: Very long exercise name that exceeds container width
    const longExerciseName =
      'Barbell Back Squat with Safety Bar and Accommodating Resistance'

    render(
      <IOSNavigationBar
        title={longExerciseName}
        isExercisePage
        setInfo="Set 1 of 3"
      />
    )

    // When: Component renders
    const titleElement = screen.getByText(longExerciseName)

    // Then: Should have auto-scroll animation classes (enhanced by default)
    expect(titleElement.classList.contains('animate-scroll-enhanced')).toBe(
      true
    )
    expect(titleElement.classList.contains('whitespace-nowrap')).toBe(true)

    // Should NOT have truncate class for long names
    expect(titleElement.classList.contains('truncate')).toBe(false)
  })

  it('should support different display modes for short exercise names', () => {
    // Given: Short exercise name
    const shortExerciseName = 'Push-ups'

    render(
      <IOSNavigationBar
        title={shortExerciseName}
        isExercisePage
        setInfo="Set 1 of 3"
      />
    )

    // When: Component renders
    const titleElement = screen.getByText(shortExerciseName)

    // Then: Should render title element (specific behavior depends on size detection)
    expect(titleElement).toBeInTheDocument()
    expect(titleElement.tagName).toBe('H1')
  })

  it('should provide fallback scrolling options for better UX', () => {
    // Given: Long exercise name that needs scrolling
    const longName =
      'Romanian Deadlift with Deficit and Pause at Bottom Position'

    // Reset to long text
    Object.defineProperty(Element.prototype, 'scrollWidth', {
      configurable: true,
      value: 400, // Longer than container width (200)
    })

    render(
      <IOSNavigationBar title={longName} isExercisePage setInfo="Set 2 of 4" />
    )

    // When: Component renders
    const titleElement = screen.getByText(longName)

    // Then: Should have enhanced scroll animation
    expect(titleElement.classList.contains('animate-scroll-enhanced')).toBe(
      true
    )
  })

  it('should handle empty or very short exercise names gracefully', () => {
    // Given: Empty exercise name
    render(<IOSNavigationBar title="" isExercisePage setInfo="Set 1 of 1" />)

    // Then: Should not crash and render heading element
    const titleElement = screen.getByRole('heading')
    expect(titleElement).toBeInTheDocument()
    expect(titleElement.textContent).toBe('')
  })

  it('should remove white divider line at bottom of nav bar', () => {
    // Given: Navigation bar component
    const { container } = render(
      <IOSNavigationBar title="Test Exercise" isExercisePage />
    )

    // When: Component renders
    const header = container.querySelector('header')

    // Then: Should NOT have bottom border
    expect(header?.classList.contains('border-b')).toBe(false)
    expect(header?.classList.contains('border-brand-primary/10')).toBe(false)
  })

  it('should include progress bar in navigation aligned with text', () => {
    // Given: Exercise page with progress data
    render(
      <IOSNavigationBar
        title="Bench Press"
        isExercisePage
        setInfo="Set 2 of 5"
        progressValue={40} // 2/5 = 40%
        totalSets={5}
        completedSets={2}
      />
    )

    // When: Component renders
    // Then: Should display progress bar
    expect(screen.getByTestId('nav-progress-bar')).toBeInTheDocument()

    // Progress bar should be in the component structure
    const progressBar = screen.getByTestId('nav-progress-bar')
    expect(progressBar).toBeInTheDocument()
  })

  describe('Enhanced Auto-Scroll Features', () => {
    it('should pause auto-scroll on touch/hover interaction', () => {
      // Given: Long exercise name with auto-scroll
      const longName =
        'Very Long Exercise Name That Requires Scrolling Animation'

      render(<IOSNavigationBar title={longName} isExercisePage />)

      // When: Component renders with auto-scroll
      const titleElement = screen.getByText(longName)

      // Then: Should have enhanced auto-scroll with pause capability
      expect(titleElement.classList.contains('animate-scroll-enhanced')).toBe(
        true
      )
      expect(titleElement).toHaveAttribute('data-scroll-pausable', 'true')
    })

    it('should support multiline display option for long exercise names', () => {
      // Given: Long exercise name with multiline option
      const longName =
        'Incline Dumbbell Bench Press with Controlled Eccentric Movement'

      render(
        <IOSNavigationBar
          title={longName}
          isExercisePage
          titleDisplayMode="multiline"
        />
      )

      // When: Component renders
      const titleElement = screen.getByText(longName)

      // Then: Should use multiline display instead of auto-scroll
      expect(titleElement.classList.contains('animate-scroll')).toBe(false)
      expect(titleElement.classList.contains('line-clamp-2')).toBe(true)
      expect(titleElement.classList.contains('whitespace-normal')).toBe(true)
    })

    it('should support swipe-to-scroll for manual control', () => {
      // Given: Long exercise name with swipe scroll option
      const longName =
        'Seated Cable Row with Wide Grip and Pause at Contraction'

      render(
        <IOSNavigationBar
          title={longName}
          isExercisePage
          titleDisplayMode="swipe-scroll"
        />
      )

      // When: Component renders
      const titleContainer = screen.getByText(longName).parentElement

      // Then: Should have swipe scroll capabilities
      expect(titleContainer).toHaveClass('overflow-x-auto')
      expect(titleContainer).toHaveClass('scrollbar-hide')
      expect(titleContainer).toHaveAttribute('data-swipe-enabled', 'true')
    })

    it('should support smart truncation with tooltip', () => {
      // Given: Long exercise name with smart truncation
      const longName = 'Romanian Deadlift with Deficit Platform and Pause'

      render(
        <IOSNavigationBar
          title={longName}
          isExercisePage
          titleDisplayMode="smart-truncate"
        />
      )

      // When: Component renders
      const titleElement = screen.getByText(longName)

      // Then: Should use smart truncation with tooltip capability
      expect(titleElement.classList.contains('truncate')).toBe(true)
      expect(titleElement).toHaveAttribute('data-tooltip-content', longName)
      expect(titleElement).toHaveAttribute('data-expandable', 'true')
    })
  })
})
