import { test, expect } from '@playwright/test'
import { mockApiResponses } from './helpers/api-mocks'
import { setupAuthenticatedUser } from './helpers/auth-helper'

test.describe('Workout Finish and Reset', () => {
  test.beforeEach(async ({ page }) => {
    await setupAuthenticatedUser(page)
  })

  test('should clear workout memory after finishing and saving', async ({
    page,
  }) => {
    // Mock the workout API responses
    await mockApiResponses(page, {
      todaysWorkout: {
        Result: [
          {
            Id: 1,
            Label: 'Test Workout',
            WorkoutTemplates: [
              {
                Id: 100,
                Label: 'Test Template',
                Exercises: [
                  {
                    Id: 1001,
                    Label: 'Bench Press',
                    BodyPartId: 1,
                    IsBodyweight: false,
                    EquipmentId: 1,
                  },
                ],
              },
            ],
          },
        ],
      },
    })

    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForSelector('text=Test Workout')

    // Start workout
    await page.click('text=Start Workout')
    await page.waitForURL('**/workout/exercise/**')

    // Complete a set
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '135')
    await page.click('text=Save set')

    // Navigate back to workout overview
    await page.goto('/workout')

    // Click Finish and save workout
    await page.click('text=Finish and save workout')

    // Wait for completion page
    await page.waitForURL('**/workout/complete')

    // Navigate back to workout page
    await page.goto('/workout')

    // Verify it shows "Start Workout" instead of "Finish and save workout"
    // This indicates the workout session has been cleared
    await expect(page.getByText('Start Workout')).toBeVisible()
    await expect(page.getByText('Finish and save workout')).not.toBeVisible()

    // Start a new workout to verify it behaves as fresh
    await page.click('text=Start Workout')
    await page.waitForURL('**/workout/exercise/**')

    // Verify we're on the first exercise with no completed sets
    await expect(page.getByText('Bench Press')).toBeVisible()
    await expect(page.getByText('Set 1')).toBeVisible()
  })

  test('should clear exercise progress when finishing workout', async ({
    page,
  }) => {
    // Mock the workout API responses with multiple exercises
    await mockApiResponses(page, {
      todaysWorkout: {
        Result: [
          {
            Id: 1,
            Label: 'Multi Exercise Workout',
            WorkoutTemplates: [
              {
                Id: 100,
                Label: 'Test Template',
                Exercises: [
                  {
                    Id: 1001,
                    Label: 'Bench Press',
                    BodyPartId: 1,
                    IsBodyweight: false,
                    EquipmentId: 1,
                  },
                  {
                    Id: 1002,
                    Label: 'Shoulder Press',
                    BodyPartId: 2,
                    IsBodyweight: false,
                    EquipmentId: 2,
                  },
                ],
              },
            ],
          },
        ],
      },
    })

    // Start workout and complete sets for first exercise
    await page.goto('/workout')
    await page.click('text=Start Workout')

    // Complete 2 sets of bench press
    // Set 1
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '135')
    await page.click('text=Save set')
    await page.waitForTimeout(500) // Small delay for state update

    // Set 2
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '135')
    await page.click('text=Save set')
    await page.waitForTimeout(500) // Small delay for state update

    // Move to second exercise
    await page.click('text=Next exercise')
    await expect(page.getByText('Shoulder Press')).toBeVisible()

    // Complete 1 set of shoulder press
    await page.fill('input[placeholder="Reps"]', '8')
    await page.fill('input[placeholder*="Weight"]', '95')
    await page.click('text=Save set')

    // Finish workout
    await page.goto('/workout')
    await page.click('text=Finish and save workout')
    await page.waitForURL('**/workout/complete')

    // Return to workout and start fresh
    await page.goto('/workout')
    await page.click('text=Start Workout')

    // Verify we're back at the first exercise with no progress
    await expect(page.getByText('Bench Press')).toBeVisible()
    await expect(page.getByText('Set 1')).toBeVisible()

    // Verify exercise list shows no completed sets
    await page.goto('/workout')
    const exerciseItems = await page
      .locator('[data-testid="exercise-item"]')
      .all()
    // Check each exercise item
    await Promise.all(
      exerciseItems.map(async (item) => {
        await expect(item.getByText('0 sets')).toBeVisible()
      })
    )
  })

  test('should show workout data on summary page, not "No workout data"', async ({
    page,
  }) => {
    // Mock the workout API responses
    await mockApiResponses(page, {
      todaysWorkout: {
        Result: [
          {
            Id: 1,
            Label: 'Test Summary Workout',
            WorkoutTemplates: [
              {
                Id: 100,
                Label: 'Test Template',
                Exercises: [
                  {
                    Id: 1001,
                    Label: 'Bench Press',
                    BodyPartId: 1,
                    IsBodyweight: false,
                    EquipmentId: 1,
                  },
                ],
              },
            ],
          },
        ],
      },
    })

    // Navigate to workout page and start workout
    await page.goto('/workout')
    await page.click('text=Start Workout')
    await page.waitForURL('**/workout/exercise/**')

    // Complete a set
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '135')
    await page.click('text=Save set')

    // Navigate to workout overview
    await page.goto('/workout')

    // Click Finish and save workout
    await page.click('text=Finish and save workout')

    // Wait for completion page
    await page.waitForURL('**/workout/complete')

    // Verify workout data is shown on summary page
    await expect(page.getByText('No workout data')).not.toBeVisible()
    await expect(page.getByText('Exercises')).toBeVisible()
    await expect(page.getByText('Sets')).toBeVisible()

    // Verify we can see actual workout stats
    const exercisesText = await page
      .getByText('Exercises')
      .locator('..')
      .textContent()
    expect(exercisesText).toContain('1') // We completed 1 exercise

    const setsText = await page.getByText('Sets').locator('..').textContent()
    expect(setsText).toContain('1') // We completed 1 set
  })
})
