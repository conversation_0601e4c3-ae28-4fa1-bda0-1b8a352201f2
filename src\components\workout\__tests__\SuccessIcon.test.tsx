import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { SuccessIcon } from '../SuccessIcon'
import { useSuccessAnimation } from '@/hooks/useSuccessAnimation'

// Mock the animation hook
vi.mock('@/hooks/useSuccessAnimation')

describe('SuccessIcon', () => {
  beforeEach(() => {
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'idle',
      isAnimating: false,
      prefersReducedMotion: false,
      start: vi.fn(),
      reset: vi.fn(),
    })
  })
  it('should render with default size', () => {
    render(<SuccessIcon />)

    const svg = screen.getByRole('img', { hidden: true })
    expect(svg).toBeInTheDocument()
    expect(svg).toHaveAttribute('width', '120')
    expect(svg).toHaveAttribute('height', '120')
  })

  it('should render with custom size', () => {
    render(<SuccessIcon size={200} />)

    const svg = screen.getByRole('img', { hidden: true })
    expect(svg).toHaveAttribute('width', '200')
    expect(svg).toHaveAttribute('height', '200')
  })

  it('should apply custom className', () => {
    render(<SuccessIcon className="custom-class" />)

    const container = screen.getByTestId('success-icon')
    expect(container).toHaveClass('custom-class')
  })

  it('should have proper accessibility attributes', () => {
    render(<SuccessIcon />)

    const svg = screen.getByRole('img', { hidden: true })
    expect(svg).toHaveAttribute('aria-label', 'Success checkmark')
  })

  it('should render circle background', () => {
    render(<SuccessIcon />)

    const circle = screen.getByTestId('success-circle')
    expect(circle).toBeInTheDocument()
    expect(circle).toHaveAttribute('stroke', 'url(#metallicGoldGradient)')
    expect(circle).toHaveAttribute('stroke-width', '10')
  })

  it('should render checkmark path', () => {
    render(<SuccessIcon />)

    const checkmark = screen.getByTestId('success-checkmark')
    expect(checkmark).toBeInTheDocument()
    expect(checkmark).toHaveAttribute('stroke', 'url(#metallicGoldGradient)')
    expect(checkmark).toHaveAttribute('stroke-width', '12')
  })

  it('should apply animation classes based on state', () => {
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'active',
      isAnimating: true,
      prefersReducedMotion: false,
      start: vi.fn(),
      reset: vi.fn(),
    })

    render(<SuccessIcon />)

    const checkmark = screen.getByTestId('success-checkmark')
    expect(checkmark).toHaveClass('animate-checkmark-draw')
  })

  it('should start animation on mount when autoPlay is true', () => {
    const mockStart = vi.fn()
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'idle',
      isAnimating: false,
      prefersReducedMotion: false,
      start: mockStart,
      reset: vi.fn(),
    })

    render(<SuccessIcon autoPlay />)

    expect(mockStart).toHaveBeenCalledTimes(1)
  })

  it('should not start animation on mount when autoPlay is false', () => {
    const mockStart = vi.fn()
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'idle',
      isAnimating: false,
      prefersReducedMotion: false,
      start: mockStart,
      reset: vi.fn(),
    })

    render(<SuccessIcon autoPlay={false} />)

    expect(mockStart).not.toHaveBeenCalled()
  })

  it('should call onAnimationComplete when animation completes', () => {
    const onComplete = vi.fn()

    vi.mocked(useSuccessAnimation).mockImplementation((options) => {
      // Call the onComplete callback immediately for testing
      if (options?.onComplete) {
        options.onComplete()
      }

      return {
        state: 'complete',
        isAnimating: false,
        prefersReducedMotion: false,
        start: vi.fn(),
        reset: vi.fn(),
      }
    })

    render(<SuccessIcon onAnimationComplete={onComplete} />)

    expect(onComplete).toHaveBeenCalledTimes(1)
  })

  it('should skip animations when user prefers reduced motion', () => {
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'complete',
      isAnimating: false,
      prefersReducedMotion: true,
      start: vi.fn(),
      reset: vi.fn(),
    })

    render(<SuccessIcon />)

    const checkmark = screen.getByTestId('success-checkmark')
    // Should not have animation class when reduced motion is preferred
    expect(checkmark).not.toHaveClass('animate-checkmark-draw')
  })

  it('should apply bounce animation to container when active', () => {
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'active',
      isAnimating: true,
      prefersReducedMotion: false,
      start: vi.fn(),
      reset: vi.fn(),
    })

    render(<SuccessIcon />)

    const container = screen.getByTestId('success-icon')
    expect(container).toHaveClass('animate-scale-bounce')
  })

  it('should have will-animate class when animating', () => {
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'active',
      isAnimating: true,
      prefersReducedMotion: false,
      start: vi.fn(),
      reset: vi.fn(),
    })

    render(<SuccessIcon />)

    const container = screen.getByTestId('success-icon')
    expect(container).toHaveClass('will-animate')
  })

  it('should render with correct viewBox', () => {
    render(<SuccessIcon />)

    const svg = screen.getByRole('img', { hidden: true })
    expect(svg).toHaveAttribute('viewBox', '0 0 120 120')
  })

  it('should support custom colors', () => {
    render(<SuccessIcon color="#ff0000" />)

    const circle = screen.getByTestId('success-circle')
    const checkmark = screen.getByTestId('success-checkmark')

    expect(circle).toHaveAttribute('stroke', '#ff0000')
    expect(checkmark).toHaveAttribute('stroke', '#ff0000')
  })

  it('should use gold gradient by default instead of solid color', () => {
    render(<SuccessIcon />)

    const svg = screen.getByRole('img', { hidden: true })
    const gradientDef = svg.querySelector('defs linearGradient')
    const circle = screen.getByTestId('success-circle')
    const checkmark = screen.getByTestId('success-checkmark')

    // Should have gradient definition in SVG
    expect(gradientDef).toBeInTheDocument()
    expect(gradientDef).toHaveAttribute('id', 'metallicGoldGradient')

    // Circle and checkmark should reference the gradient
    expect(circle).toHaveAttribute('stroke', 'url(#metallicGoldGradient)')
    expect(checkmark).toHaveAttribute('stroke', 'url(#metallicGoldGradient)')
  })

  it('should define gradient with correct metallic gold color stops', () => {
    render(<SuccessIcon />)

    const svg = screen.getByRole('img', { hidden: true })
    const gradientStops = svg.querySelectorAll('linearGradient stop')

    // Should have 6 color stops for metallic gold gradient
    expect(gradientStops).toHaveLength(6)

    // Check key color stops
    expect(gradientStops[0]).toHaveAttribute('stop-color', '#d4af37')
    expect(gradientStops[0]).toHaveAttribute('offset', '0%')
    expect(gradientStops[1]).toHaveAttribute('stop-color', '#f4e5b2')
    expect(gradientStops[1]).toHaveAttribute('offset', '20%')
    expect(gradientStops[5]).toHaveAttribute('stop-color', '#d4af37')
    expect(gradientStops[5]).toHaveAttribute('offset', '100%')
  })

  it('should fallback to solid color when custom color is provided', () => {
    render(<SuccessIcon color="#ff0000" />)

    const circle = screen.getByTestId('success-circle')
    const checkmark = screen.getByTestId('success-checkmark')

    // Should use custom color instead of gradient
    expect(circle).toHaveAttribute('stroke', '#ff0000')
    expect(checkmark).toHaveAttribute('stroke', '#ff0000')
  })
})
