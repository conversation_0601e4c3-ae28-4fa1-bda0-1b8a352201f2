import { test, expect } from '@playwright/test'
import { <PERSON>ginHelper } from './helpers/login-helper'
import { WorkoutHelper } from './helpers/workout-helper'

test.describe('Exercise V2 Timer Overlay', () => {
  let loginHelper: LoginHelper
  let workoutHelper: WorkoutHelper

  test.beforeEach(async ({ page }) => {
    loginHelper = new LoginHelper(page)
    workoutHelper = new WorkoutHelper(page)
    await loginHelper.loginAsTestUser()
  })

  test('should show timer overlay after clicking Save Sets button', async ({
    page,
  }) => {
    // Given: User has started a workout and navigated to exercise V2 page
    await workoutHelper.startWorkout()
    const firstExercise = await workoutHelper.getFirstExercise()
    await page.goto(`/workout/exercise-v2/${firstExercise.id}`)
    await page.waitForSelector('[data-testid="current-set-card"]')

    // When: User clicks the Save Sets button
    await page.click('button:has-text("Save set")')

    // Then: Timer overlay should appear at the bottom of the page
    await expect(
      page.locator('[data-testid="rest-timer-container"]')
    ).toBeVisible()

    // And: Timer overlay should contain countdown text
    await expect(page.locator('[data-testid="countdown-text"]')).toBeVisible()

    // And: Should NOT navigate to full-screen timer page
    await expect(page).not.toHaveURL(/\/workout\/rest-timer/)

    // And: Should still be on the exercise V2 page
    await expect(page).toHaveURL(
      new RegExp(`/workout/exercise-v2/${firstExercise.id}`)
    )
  })

  test('should show timer overlay after swiping right on current set card', async ({
    page,
  }) => {
    // Given: User has started a workout and navigated to exercise V2 page
    await workoutHelper.startWorkout()
    const firstExercise = await workoutHelper.getFirstExercise()
    await page.goto(`/workout/exercise-v2/${firstExercise.id}`)
    await page.waitForSelector('[data-testid="current-set-card"]')

    // When: User swipes right on the current set card
    const setCard = page.locator('[data-testid="current-set-card"]')
    const box = await setCard.boundingBox()
    if (!box) throw new Error('Set card not found')

    await page.mouse.move(box.x + 50, box.y + box.height / 2)
    await page.mouse.down()
    await page.mouse.move(box.x + 250, box.y + box.height / 2, { steps: 10 })
    await page.mouse.up()

    // Then: Timer overlay should appear at the bottom of the page
    await expect(
      page.locator('[data-testid="rest-timer-container"]')
    ).toBeVisible({
      timeout: 5000,
    })

    // And: Should NOT navigate to full-screen timer page
    await expect(page).not.toHaveURL(/\/workout\/rest-timer/)
  })

  test('timer overlay should have hide button', async ({ page }) => {
    // Given: User has started a workout and navigated to exercise V2 page
    await workoutHelper.startWorkout()
    const firstExercise = await workoutHelper.getFirstExercise()
    await page.goto(`/workout/exercise-v2/${firstExercise.id}`)
    await page.waitForSelector('[data-testid="current-set-card"]')

    // When: User saves a set
    await page.click('button:has-text("Save set")')
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // Then: Timer overlay should have a hide button
    const hideButton = page.locator('button:has-text("Hide")')
    await expect(hideButton).toBeVisible()

    // When: User clicks hide button
    await hideButton.click()

    // Then: Timer overlay should be hidden
    await expect(
      page.locator('[data-testid="rest-timer-container"]')
    ).not.toBeVisible()
  })
})
