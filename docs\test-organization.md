# Test Organization

## Overview

Our E2E tests are organized into three tiers based on criticality and execution frequency. This ensures fast feedback for critical paths while maintaining comprehensive coverage.

## Test Tiers

### 1. Critical Tests (@critical)

**Run on:** Every commit, CI/CD pipeline
**Max duration:** 5 minutes total
**Retry policy:** Up to 5 retries for WebKit stability

These tests cover the absolute core user journeys that must never break:

- **critical-flows.spec.ts** - Core user flows (login, start workout, exercise interaction, recommendations, rest-pause)
- **critical-paths.spec.ts** - General critical validations (homepage, performance, PWA, touch targets)
- **login-flows.spec.ts** - Authentication flows
- **login-logout-flow.spec.ts** - Session management
- **security.spec.ts** - Security validations
- **race-condition-prevention.spec.ts** - Data integrity during concurrent operations
- **error-boundaries.spec.ts** - Application stability and error handling

### 2. Important Tests

**Run on:** Pull requests, pre-merge validation
**Max duration:** 15 minutes total
**Retry policy:** Up to 3 retries

Tests that are important but not blocking for every commit:

- **navigation-routing.spec.ts** - Navigation behavior
- **loading-states.spec.ts** - Loading state management
- **offline-mode-behavior.spec.ts** - PWA offline functionality

### 3. Feature Tests

**Run on:** Nightly builds, on-demand
**Max duration:** Unlimited
**Retry policy:** 1 retry

Comprehensive feature-specific tests:

- **workout-components.spec.ts** - Component-level workout tests
- **workout-creation-tracking.spec.ts** - Workout creation features
- **workout-flows.spec.ts** - Detailed workout scenarios
- **exercise-flows.spec.ts** - Exercise-specific features
- **oauth-auth-header.spec.ts** - OAuth implementation details
- **data-sync-functionality.spec.ts** - Background sync features

## Running Tests by Tier

```bash
# Run only critical tests
npm run test:e2e -- --grep @critical

# Run important tests (excluding critical)
npm run test:e2e -- --grep -v @critical tests/e2e/{navigation-routing,loading-states,offline-mode-behavior}.spec.ts

# Run all tests
npm run test:e2e
```

## Adding New Tests

When adding new E2E tests, consider:

1. **Is this a core user journey?** → Add `@critical` tag
2. **Does it block user functionality?** → Important tier
3. **Is it feature-specific?** → Feature tier

## Maintenance

- Review critical tests quarterly to ensure they remain minimal
- Move tests between tiers based on failure patterns
- Keep critical test suite under 5 minutes total execution time
