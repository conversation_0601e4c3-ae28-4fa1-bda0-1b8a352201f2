import { test, expect } from '@playwright/test'

// Helper function to perform login
async function login(page: any, email: string, password: string) {
  await page.getByRole('textbox', { name: 'Email' }).fill(email)
  await page.locator('#password').fill(password)
  await page.waitForFunction(
    () => {
      const button = document.querySelector(
        'button[type="submit"]'
      ) as HTMLButtonElement
      return button && !button.disabled
    },
    { timeout: 10000 }
  )
  const loginButton = page.locator('button[type="submit"]')
  await loginButton.waitFor({ state: 'visible', timeout: 10000 })
  await loginButton.click({ force: true, timeout: 10000 })
}

test.describe('ExplainerBox Text Flexibility', () => {
  test('should display "Last time:" text without requiring exact match', async ({
    page,
  }) => {
    test.setTimeout(60000)

    // Login and navigate to workout
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')
    await page.waitForURL('/program', { timeout: 15000 })

    // Start or continue workout
    const continueButton = page.getByRole('button', { name: /continue/i })
    const startButton = page.getByRole('button', { name: /open workout/i })

    if (await continueButton.isVisible({ timeout: 2000 }).catch(() => false)) {
      await continueButton.click({ force: true })
    } else {
      await startButton.click({ force: true })
    }

    await page.waitForURL(/\/workout/, { timeout: 20000 })
    await page.waitForLoadState('networkidle')

    // Navigate to first exercise
    const exerciseCards = page.locator('[data-testid="exercise-card"]')
    await exerciseCards.first().click()
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 15000 })
    await page.waitForLoadState('networkidle')

    // Wait for the sets grid to load
    await page.waitForSelector('.grid', { timeout: 10000 })

    // Look for ExplainerBox content
    const explainerBox = page.locator('.bg-bg-secondary\\/50')

    // Check if ExplainerBox is visible
    const isExplainerVisible = await explainerBox.isVisible().catch(() => false)

    if (isExplainerVisible) {
      // Check for "Last time:" text without being strict about the exact format
      const hasLastTimeText = await explainerBox
        .locator('text=/Last time:/i')
        .isVisible()
        .catch(() => false)

      if (hasLastTimeText) {
        // Verify the text contains "Last time:" but don't check for exact numbers
        await expect(explainerBox).toContainText('Last time:')

        // Also check it contains some pattern of numbers and units without being exact
        const explainerText = await explainerBox.textContent()
        expect(explainerText).toMatch(/Last time:.*\d+.*×.*\d+.*(lbs|kg)/i)

        console.log('ExplainerBox found with flexible "Last time:" text')
      } else {
        console.log(
          'ExplainerBox visible but no "Last time:" text - this is OK if no history exists'
        )
      }
    } else {
      console.log('No ExplainerBox visible - may not be on active set')
    }
  })
})
