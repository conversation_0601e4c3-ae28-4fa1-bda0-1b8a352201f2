import type { WarmUps, MultiUnityWeight } from '@/types'

// WarmupSet is the same as WarmUps from the API
export type WarmupSet = WarmUps

export interface WarmupCalculationConfig {
  warmupsCount: number
  workingWeight: MultiUnityWeight
  workingReps: number
  incrementValue: number
  minWeight?: number
  maxWeight?: number
  isPlateAvailable: boolean
  isDumbbellAvailable?: boolean
  isBandsAvailable?: boolean
  isPulleyAvailable?: boolean
  isBodyweight: boolean
  isWeighted?: boolean
  isAssisted?: boolean
  barbellWeight: number
  availablePlates: string
  availableDumbbells?: string
  availableBands?: string
  userBodyWeight: number
  isKg: boolean
}

// Special exercise IDs that require specific handling
export const WEIGHTED_EXERCISE_IDS = [
  18627, // Weighted Pull-up
  18628, // Weighted Chin-up
  21234, // Weighted Chest Dip
  14297, // Weighted Triceps Dip
  862,
  6993,
  13446, // Weighted Crunch variations
  863,
  6992,
  13449, // Weighted Twisting Crunch variations
  // Add more weighted bodyweight exercise IDs as needed
]

// TODO: Add band exercise IDs when implementing band support
// const BAND_EXERCISE_IDS = [
//   16897, 16898, 16899, 16900, 16901, 16902, 16903, 16904, 16905, 16906, 16907,
//   14279, 21508, 21509, 21510, 21511, 21512, 21513, 21514
// ]

// TODO: Add cardio exercise handling when needed
// const CARDIO_EXERCISE_ID = 16508
// const CARDIO_BODYPART_ID = 12

// TODO: Add getUserSettings function when needed for user preferences
// function getUserSettings() {
//   return {
//     barbellWeight: 45,
//     unit: 'lbs',
//     bodyWeight: 180,
//     availablePlates: 'all',
//   }
// }

/**
 * Calculate weight using available plates
 */
function calculatePlatesWeight(
  _availablePlates: string,
  targetWeight: number,
  barbellWeight: number,
  isKg: boolean
): number {
  if (targetWeight <= barbellWeight) {
    return barbellWeight
  }

  const weightPerSide = (targetWeight - barbellWeight) / 2
  const plates = isKg
    ? [20, 15, 10, 5, 2.5, 1.25, 0.5, 0.25]
    : [45, 35, 25, 10, 5, 2.5]

  let remainingWeight = weightPerSide
  let totalPlateWeight = 0

  plates.forEach((plate) => {
    while (remainingWeight >= plate) {
      totalPlateWeight += plate
      remainingWeight -= plate
    }
  })

  return barbellWeight + totalPlateWeight * 2
}

/**
 * Get closest available dumbbell weight
 */
function getDumbbellWeight(
  availableDumbbells: string,
  targetWeight: number,
  isKg: boolean
): number {
  // Parse available dumbbells from format: "weight_count_isSystem|weight_count_isSystem"
  if (!availableDumbbells || availableDumbbells === 'all') {
    // Default dumbbell progression
    const defaultWeights = isKg
      ? [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30]
      : [5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80]

    // Find closest weight not exceeding target
    for (let i = defaultWeights.length - 1; i >= 0; i--) {
      const weight = defaultWeights[i]
      if (weight !== undefined && weight <= targetWeight) {
        return weight
      }
    }
    return defaultWeights[0] || 0
  }

  const dumbbells = availableDumbbells
    .split('|')
    .map((item) => {
      const [weight, count] = item.split('_')
      return {
        weight: parseFloat(weight || '0'),
        count: parseInt(count || '0'),
      }
    })
    .filter((db) => db.count > 0)

  // Sort descending
  dumbbells.sort((a, b) => b.weight - a.weight)

  // Find closest dumbbell not exceeding target
  const suitableDumbbell = dumbbells.find((db) => db.weight <= targetWeight)
  if (suitableDumbbell) {
    return suitableDumbbell.weight
  }

  // If no suitable dumbbell, return smallest available
  return dumbbells[dumbbells.length - 1]?.weight || 0
}

/**
 * Get closest resistance band weight
 */
function getBandsWeight(
  availableBands: string,
  targetWeight: number,
  isKg: boolean
): number {
  // Default band resistances
  const defaultBands = isKg
    ? [2, 5, 8, 12, 16, 20, 25, 30]
    : [5, 10, 15, 25, 35, 45, 55, 65]

  if (!availableBands || availableBands === 'all') {
    // Find closest band
    let closest = defaultBands[0]
    let minDiff = Math.abs(targetWeight - (defaultBands[0] || 0))

    defaultBands.forEach((band) => {
      const diff = Math.abs(targetWeight - band)
      if (diff < minDiff) {
        minDiff = diff
        closest = band
      }
    })
    return closest || 0
  }

  // Parse custom bands (similar to dumbbells)
  const bands = availableBands
    .split('|')
    .map((item) => {
      const [weight, count] = item.split('_')
      return {
        weight: parseFloat(weight || '0'),
        count: parseInt(count || '0'),
      }
    })
    .filter((band) => band.count > 0)

  if (bands.length === 0) return 0

  let closest = bands[0]
  let minDiff = Math.abs(targetWeight - (bands[0]?.weight || 0))

  bands.forEach((band) => {
    const diff = Math.abs(targetWeight - band.weight)
    if (diff < minDiff) {
      minDiff = diff
      closest = band
    }
  })

  return closest?.weight || 0
}

/**
 * WarmupCalculator class implementing the exact MAUI app warmup algorithm
 */
export class WarmupCalculator {
  private static readonly KG_TO_LB = 2.20462

  private static readonly INITIAL_WEIGHT_PERCENTAGE = 0.5

  private static readonly FINAL_WEIGHT_PERCENTAGE = 0.85

  private static readonly BODYWEIGHT_INITIAL_REPS_PERCENTAGE = 0.5

  private static readonly BODYWEIGHT_FINAL_REPS_PERCENTAGE = 0.6

  private static readonly WEIGHTED_INITIAL_REPS_PERCENTAGE = 0.75

  private static readonly WEIGHTED_FINAL_REPS_PERCENTAGE = 0.4

  private static readonly MIN_REPS_THRESHOLD = 5.01

  private static readonly MIN_REPS_DEFAULT = 6

  private static readonly MIN_REPS_WEIGHTED = 3

  // /**
  //  * Check if exercise is a weighted bodyweight exercise
  //  */
  // private static isWeightedBodyweightExercise(exerciseId?: number): boolean {
  //   return exerciseId ? WEIGHTED_EXERCISE_IDS.includes(exerciseId) : false
  // }

  /**
   * Calculate warmups for bodyweight exercises
   */
  private static calculateBodyweightWarmups(
    config: WarmupCalculationConfig
  ): WarmupSet[] {
    const warmups: WarmupSet[] = []

    // Initial reps: 50% of working reps
    const initialReps =
      config.workingReps * this.BODYWEIGHT_INITIAL_REPS_PERCENTAGE

    // Single warmup set
    if (config.warmupsCount === 1) {
      warmups.push({
        WarmUpWeightSet: { Kg: 0, Lb: 0 }, // Bodyweight exercises show 0 weight
        WarmUpReps: Math.ceil(initialReps),
      })
      return warmups
    }

    // Multiple warmup sets: progress from 50% to 60% of working reps
    const newWarmupCount = config.warmupsCount - 1
    const repsIncrement =
      (config.workingReps * this.BODYWEIGHT_FINAL_REPS_PERCENTAGE -
        config.workingReps * this.BODYWEIGHT_INITIAL_REPS_PERCENTAGE) /
      newWarmupCount

    for (let i = 0; i < config.warmupsCount; i++) {
      warmups.push({
        WarmUpWeightSet: { Kg: 0, Lb: 0 }, // Bodyweight exercises show 0 weight
        WarmUpReps: Math.ceil(initialReps + repsIncrement * i),
      })
    }

    return warmups
  }

  /**
   * Calculate warmups for assisted exercises
   */
  private static calculateAssistedWarmups(
    config: WarmupCalculationConfig
  ): WarmupSet[] {
    const warmups: WarmupSet[] = []
    const resistanceWeight = config.workingWeight.Kg

    const initialWeight = resistanceWeight * this.INITIAL_WEIGHT_PERCENTAGE
    const newWarmupCount = config.warmupsCount > 1 ? config.warmupsCount - 1 : 1
    const weightIncrement =
      (resistanceWeight * this.FINAL_WEIGHT_PERCENTAGE -
        resistanceWeight * this.INITIAL_WEIGHT_PERCENTAGE) /
      newWarmupCount

    const initialReps =
      config.workingReps * this.WEIGHTED_INITIAL_REPS_PERCENTAGE
    const repsIncrement =
      (config.workingReps * this.WEIGHTED_INITIAL_REPS_PERCENTAGE -
        config.workingReps * this.WEIGHTED_FINAL_REPS_PERCENTAGE) /
      newWarmupCount

    for (let i = 0; i < config.warmupsCount; i++) {
      const warmupResistance = initialWeight + weightIncrement * i
      const assistanceWeight = config.userBodyWeight - warmupResistance

      warmups.push({
        WarmUpWeightSet: {
          Kg: assistanceWeight,
          Lb: assistanceWeight * this.KG_TO_LB,
        },
        WarmUpReps: Math.ceil(initialReps - repsIncrement * i),
      })
    }

    return warmups
  }

  /**
   * Apply equipment-specific weight rounding
   */
  private static applyEquipmentRounding(
    weight: number,
    config: WarmupCalculationConfig
  ): number {
    if (config.isPlateAvailable) {
      return calculatePlatesWeight(
        config.availablePlates,
        weight,
        config.barbellWeight,
        config.isKg
      )
    }

    if (config.isDumbbellAvailable && config.availableDumbbells) {
      return getDumbbellWeight(config.availableDumbbells, weight, config.isKg)
    }

    if (config.isBandsAvailable && config.availableBands) {
      return getBandsWeight(config.availableBands, weight, config.isKg)
    }

    return weight
  }

  /**
   * Round weight to nearest increment
   */
  private static roundToNearestIncrement(
    weight: number,
    increment: number,
    min?: number,
    max?: number
  ): number {
    if (increment === 0 || increment === 0.01) {
      return weight
    }

    const rounded = Math.round(weight / increment) * increment

    if (min && rounded < min) return min
    if (max && rounded > max) return max

    return rounded
  }

  static computeWarmups(config: WarmupCalculationConfig): WarmupSet[] {
    // Debug logging removed - was causing lint warnings

    // No warmups if count is 0
    if (config.warmupsCount === 0) {
      // No warmups requested (count = 0)
      return []
    }

    // Handle missing weight data for non-bodyweight exercises
    if (
      !config.workingWeight ||
      (config.workingWeight.Kg === 0 && !config.isBodyweight)
    ) {
      // Missing weight data for non-bodyweight exercise
      return []
    }

    // Calculate based on exercise type
    let warmups: WarmupSet[]

    if (config.isBodyweight) {
      // Using bodyweight calculation
      warmups = this.calculateBodyweightWarmups(config)
    } else if (config.isAssisted) {
      // Using assisted calculation
      warmups = this.calculateAssistedWarmups(config)
    } else {
      // Using weighted calculation
      warmups = this.calculateWeightedWarmups(config)
    }

    // Final warmup sets calculated

    return warmups
  }

  /**
   * Calculate warmups for weighted exercises (improved version)
   */
  private static calculateWeightedWarmups(
    config: WarmupCalculationConfig
  ): WarmupSet[] {
    // Starting weighted warmup calculation
    const warmups: WarmupSet[] = []

    // Weight progression: 50% to 85% of working weight
    const initialWeight =
      config.workingWeight.Kg * this.INITIAL_WEIGHT_PERCENTAGE
    const newWarmupCount = config.warmupsCount > 1 ? config.warmupsCount - 1 : 1
    const weightIncrement =
      (config.workingWeight.Kg * this.FINAL_WEIGHT_PERCENTAGE -
        config.workingWeight.Kg * this.INITIAL_WEIGHT_PERCENTAGE) /
      newWarmupCount

    // Weight progression calculated

    // Reps progression: 75% to 40% of working reps
    let initialReps = config.workingReps * this.WEIGHTED_INITIAL_REPS_PERCENTAGE
    const repsIncrement =
      (config.workingReps * this.WEIGHTED_INITIAL_REPS_PERCENTAGE -
        config.workingReps * this.WEIGHTED_FINAL_REPS_PERCENTAGE) /
      newWarmupCount

    // Minimum reps adjustment
    if (initialReps < this.MIN_REPS_THRESHOLD && !config.isPlateAvailable) {
      initialReps = this.MIN_REPS_DEFAULT
    }
    if (config.isPlateAvailable && initialReps < 1) {
      initialReps = 1
    }

    for (let i = 0; i < config.warmupsCount; i++) {
      const warmupWeight = initialWeight + weightIncrement * i
      let warmupReps = Math.ceil(initialReps - repsIncrement * i)

      // Calculating warmup set

      // Minimum 3 reps for non-plate exercises
      if (warmupReps < this.MIN_REPS_WEIGHTED && !config.isPlateAvailable) {
        warmupReps = this.MIN_REPS_WEIGHTED
      }

      // Round weight to available increments
      const roundedWeight = this.roundToNearestIncrement(
        warmupWeight,
        config.incrementValue,
        config.minWeight,
        config.maxWeight
      )

      // Apply equipment-specific rounding
      const finalWeight = this.applyEquipmentRounding(roundedWeight, config)

      // Warmup set finalized

      warmups.push({
        WarmUpWeightSet: {
          Kg: finalWeight,
          Lb: finalWeight * this.KG_TO_LB,
        },
        WarmUpReps: warmupReps,
      })
    }

    // CRITICAL: Override first warmup for weighted bodyweight exercises
    // This applies to exercises like weighted pull-ups, weighted dips, etc.
    // where 0 kg means bodyweight only (no additional weight)
    if (config.isWeighted && warmups.length > 0 && warmups[0]) {
      if (config.minWeight === null || config.minWeight === undefined) {
        warmups[0].WarmUpWeightSet = { Kg: 0, Lb: 0 }
      } else {
        warmups[0].WarmUpWeightSet = {
          Kg: config.minWeight,
          Lb: config.minWeight * this.KG_TO_LB,
        }
      }
    }

    return warmups
  }
}
