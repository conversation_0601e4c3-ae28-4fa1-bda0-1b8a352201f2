import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ExerciseOverflowMenu } from '../ExerciseOverflowMenu'
import type { ExerciseModel } from '@/types'

describe('ExerciseOverflowMenu', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsFinished: false,
    IsBodyweight: false,
    IsSwapTarget: false,
    IsNextExercise: false,
  }

  const mockOnSkip = vi.fn()
  const mockOnViewHistory = vi.fn()
  const mockOnAddNote = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Menu Toggle', () => {
    it('should show menu button initially with menu closed', () => {
      render(
        <ExerciseOverflowMenu
          exercise={mockExercise}
          onSkip={mockOnSkip}
          onViewHistory={mockOnViewHistory}
          onAddNote={mockOnAddNote}
        />
      )

      const menuButton = screen.getByLabelText('Exercise options')
      expect(menuButton).toBeInTheDocument()
      expect(screen.queryByRole('menu')).not.toBeInTheDocument()
    })

    it('should open menu on click', async () => {
      render(
        <ExerciseOverflowMenu
          exercise={mockExercise}
          onSkip={mockOnSkip}
          onViewHistory={mockOnViewHistory}
          onAddNote={mockOnAddNote}
        />
      )

      const menuButton = screen.getByLabelText('Exercise options')
      fireEvent.click(menuButton)

      await waitFor(() => {
        expect(screen.getByRole('menu')).toBeInTheDocument()
        expect(screen.getByText('Skip Exercise')).toBeInTheDocument()
        expect(screen.getByText('View History')).toBeInTheDocument()
        expect(screen.getByText('Add Note')).toBeInTheDocument()
      })
    })

    it('should close menu when clicking outside', async () => {
      render(
        <div>
          <ExerciseOverflowMenu
            exercise={mockExercise}
            onSkip={mockOnSkip}
            onViewHistory={mockOnViewHistory}
            onAddNote={mockOnAddNote}
          />
          <div data-testid="outside">Outside element</div>
        </div>
      )

      // Open menu
      const menuButton = screen.getByLabelText('Exercise options')
      fireEvent.click(menuButton)
      expect(screen.getByRole('menu')).toBeInTheDocument()

      // Click outside
      fireEvent.mouseDown(screen.getByTestId('outside'))

      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument()
      })
    })
  })

  describe('Touch Event Handling', () => {
    it('should handle touch events without preventDefault errors', async () => {
      // Mock console.error to catch preventDefault warnings
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      render(
        <ExerciseOverflowMenu
          exercise={mockExercise}
          onSkip={mockOnSkip}
          onViewHistory={mockOnViewHistory}
          onAddNote={mockOnAddNote}
        />
      )

      const menuButton = screen.getByLabelText('Exercise options')

      // Simulate actual mobile touch sequence
      const touchEvent = new TouchEvent('touchend', {
        bubbles: true,
        cancelable: true,
      })

      // This should fail initially due to preventDefault in passive listener
      fireEvent(menuButton, touchEvent)

      // Menu should still open despite preventDefault issue
      await waitFor(() => {
        expect(screen.getByRole('menu')).toBeInTheDocument()
      })

      // Check no preventDefault errors in console
      expect(consoleErrorSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('preventDefault')
      )

      consoleErrorSpy.mockRestore()
    })

    it('should respond to both onClick and onTouchEnd events', async () => {
      render(
        <ExerciseOverflowMenu
          exercise={mockExercise}
          onSkip={mockOnSkip}
          onViewHistory={mockOnViewHistory}
          onAddNote={mockOnAddNote}
        />
      )

      const menuButton = screen.getByLabelText('Exercise options')

      // Test onClick
      fireEvent.click(menuButton)
      expect(screen.getByRole('menu')).toBeInTheDocument()

      // Close menu
      fireEvent.mouseDown(document.body)
      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument()
      })

      // Test onTouchEnd
      fireEvent.touchEnd(menuButton)
      await waitFor(() => {
        expect(screen.getByRole('menu')).toBeInTheDocument()
      })
    })

    it('should prevent event propagation to parent elements', () => {
      const parentClickHandler = vi.fn()

      render(
        <div onClick={parentClickHandler}>
          <ExerciseOverflowMenu
            exercise={mockExercise}
            onSkip={mockOnSkip}
            onViewHistory={mockOnViewHistory}
            onAddNote={mockOnAddNote}
          />
        </div>
      )

      const menuButton = screen.getByLabelText('Exercise options')
      fireEvent.click(menuButton)

      expect(parentClickHandler).not.toHaveBeenCalled()
    })
  })

  describe('Menu Actions', () => {
    it('should call onSkip when Skip Exercise is clicked', async () => {
      render(
        <ExerciseOverflowMenu
          exercise={mockExercise}
          onSkip={mockOnSkip}
          onViewHistory={mockOnViewHistory}
          onAddNote={mockOnAddNote}
        />
      )

      // Open menu
      fireEvent.click(screen.getByLabelText('Exercise options'))

      // Click Skip Exercise
      fireEvent.click(screen.getByText('Skip Exercise'))

      expect(mockOnSkip).toHaveBeenCalledWith(mockExercise.Id)

      // Menu should close after action
      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument()
      })
    })

    it('should call onViewHistory when View History is clicked', async () => {
      render(
        <ExerciseOverflowMenu
          exercise={mockExercise}
          onSkip={mockOnSkip}
          onViewHistory={mockOnViewHistory}
          onAddNote={mockOnAddNote}
        />
      )

      // Open menu
      fireEvent.click(screen.getByLabelText('Exercise options'))

      // Click View History
      fireEvent.click(screen.getByText('View History'))

      expect(mockOnViewHistory).toHaveBeenCalledWith(mockExercise.Id)

      // Menu should close after action
      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument()
      })
    })

    it('should call onAddNote when Add Note is clicked', async () => {
      render(
        <ExerciseOverflowMenu
          exercise={mockExercise}
          onSkip={mockOnSkip}
          onViewHistory={mockOnViewHistory}
          onAddNote={mockOnAddNote}
        />
      )

      // Open menu
      fireEvent.click(screen.getByLabelText('Exercise options'))

      // Click Add Note
      fireEvent.click(screen.getByText('Add Note'))

      expect(mockOnAddNote).toHaveBeenCalledWith(mockExercise.Id)

      // Menu should close after action
      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument()
      })
    })
  })

  describe('Edge Cases', () => {
    it('should handle rapid clicking without issues', async () => {
      render(
        <ExerciseOverflowMenu
          exercise={mockExercise}
          onSkip={mockOnSkip}
          onViewHistory={mockOnViewHistory}
          onAddNote={mockOnAddNote}
        />
      )

      const menuButton = screen.getByLabelText('Exercise options')

      // Rapid clicks
      fireEvent.click(menuButton)
      fireEvent.click(menuButton)
      fireEvent.click(menuButton)

      // Should still be in a valid state
      await waitFor(() => {
        const menus = screen.queryAllByRole('menu')
        expect(menus.length).toBeLessThanOrEqual(1)
      })
    })

    it('should position menu correctly near screen edges', () => {
      // Mock getBoundingClientRect to simulate near bottom edge
      const mockGetBoundingClientRect = vi.fn(() => ({
        top: window.innerHeight - 50,
        left: 10,
        right: 60,
        bottom: window.innerHeight - 10,
        width: 50,
        height: 40,
        x: 10,
        y: window.innerHeight - 50,
      }))

      render(
        <ExerciseOverflowMenu
          exercise={mockExercise}
          onSkip={mockOnSkip}
          onViewHistory={mockOnViewHistory}
          onAddNote={mockOnAddNote}
        />
      )

      const menuButton = screen.getByLabelText('Exercise options')
      menuButton.getBoundingClientRect = mockGetBoundingClientRect

      fireEvent.click(menuButton)

      const menu = screen.getByRole('menu')
      const menuStyles = window.getComputedStyle(menu)

      // Menu should open upward when near bottom
      expect(menuStyles.bottom).toBeTruthy()
    })

    it('should support keyboard navigation', async () => {
      render(
        <ExerciseOverflowMenu
          exercise={mockExercise}
          onSkip={mockOnSkip}
          onViewHistory={mockOnViewHistory}
          onAddNote={mockOnAddNote}
        />
      )

      const menuButton = screen.getByLabelText('Exercise options')

      // Open with Enter key
      menuButton.focus()
      fireEvent.keyDown(menuButton, { key: 'Enter' })

      expect(screen.getByRole('menu')).toBeInTheDocument()

      // Close with Escape
      fireEvent.keyDown(screen.getByRole('menu'), { key: 'Escape' })

      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument()
      })
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(
        <ExerciseOverflowMenu
          exercise={mockExercise}
          onSkip={mockOnSkip}
          onViewHistory={mockOnViewHistory}
          onAddNote={mockOnAddNote}
        />
      )

      const menuButton = screen.getByLabelText('Exercise options')
      expect(menuButton).toHaveAttribute('aria-haspopup', 'true')
      expect(menuButton).toHaveAttribute('aria-expanded', 'false')

      fireEvent.click(menuButton)

      expect(menuButton).toHaveAttribute('aria-expanded', 'true')

      const menu = screen.getByRole('menu')
      expect(menu).toHaveAttribute('aria-label', 'Exercise options menu')
    })

    it('should trap focus within menu when open', () => {
      render(
        <ExerciseOverflowMenu
          exercise={mockExercise}
          onSkip={mockOnSkip}
          onViewHistory={mockOnViewHistory}
          onAddNote={mockOnAddNote}
        />
      )

      fireEvent.click(screen.getByLabelText('Exercise options'))

      const menuItems = screen.getAllByRole('menuitem')
      expect(menuItems).toHaveLength(3)

      // First item should be focused
      expect(menuItems[0]).toHaveFocus()
    })
  })
})
