import { Badge } from '@/components/ui/Badge'
import {
  WorkoutCardSkeleton,
  ExerciseItemSkeleton,
} from '@/components/ui/Skeletons'
import { debugLog } from '@/utils/debugLog'
import type { WorkoutTemplateGroupModel } from '@/types'

interface LoadingStateProps {
  isStartingWorkout?: boolean
}

export function WorkoutLoadingState({ isStartingWorkout }: LoadingStateProps) {
  return (
    <div className="h-full bg-bg-primary">
      <div className="h-full overflow-y-auto">
        <div className="p-4 pb-24">
          <div className="mx-auto max-w-lg">
            {/* Title skeleton */}
            <div
              data-testid="workout-title-skeleton"
              className="mb-6 h-8 w-48 bg-bg-tertiary rounded animate-pulse"
            />

            {/* Workout info skeleton */}
            <div className="mb-6">
              <WorkoutCardSkeleton />
            </div>

            {/* Exercise list skeletons */}
            <div className="mb-8 space-y-3">
              {Array.from({ length: 5 }, (_, i) => (
                <ExerciseItemSkeleton key={i} />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Floating Action Button (disabled while loading) */}
      <div className="fixed bottom-6 left-0 right-0 z-50">
        <div className="mx-auto max-w-lg w-full px-4">
          <button
            disabled
            className="w-full rounded-full bg-bg-tertiary py-4 text-lg font-semibold text-text-tertiary cursor-not-allowed min-h-[56px] shadow-theme-xl"
            data-testid="start-workout-button"
          >
            {isStartingWorkout ? 'Starting...' : 'Loading...'}
          </button>
        </div>
      </div>
    </div>
  )
}

interface ErrorStateProps {
  error: unknown
}

export function WorkoutErrorState({ error }: ErrorStateProps) {
  const errorMessage = (() => {
    if (typeof error === 'string') return error
    if (error instanceof Error) return error.message
    return 'Failed to load workout'
  })()

  // Check if it's an authentication error
  const isAuthError =
    (error instanceof Error && error.name === 'AuthenticationError') ||
    errorMessage.toLowerCase().includes('authentication') ||
    errorMessage.toLowerCase().includes('session') ||
    errorMessage.toLowerCase().includes('expired')

  return (
    <div className="flex h-full items-center justify-center p-4">
      <div className="text-center">
        <h2 className="mb-2 text-xl font-semibold text-error">
          {isAuthError ? 'Session Expired' : 'Error loading workout'}
        </h2>
        <p className="mb-4 text-text-secondary">
          {isAuthError
            ? 'Your session has expired. Please log in again to continue.'
            : errorMessage}
        </p>
        <button
          onClick={() => {
            if (isAuthError) {
              window.location.href = '/login'
            } else {
              window.location.reload()
            }
          }}
          className="px-6 py-3 min-h-[56px] bg-gradient-metallic-gold text-text-inverse font-semibold tracking-wider rounded-theme shadow-theme-md hover:shadow-theme-lg hover:shadow-xl hover:shadow-brand-primary/20 transition-all shimmer-hover text-shadow-sm"
        >
          {isAuthError ? 'Go to Login' : 'Try Again'}
        </button>
      </div>
    </div>
  )
}

interface NoWorkoutStateProps {
  todaysWorkout: WorkoutTemplateGroupModel[] | null
  isLoadingWorkout: boolean
  hasInitialData: boolean
  userProgramInfo: unknown
}

export function NoWorkoutState({
  todaysWorkout,
  isLoadingWorkout,
  hasInitialData,
  userProgramInfo,
}: NoWorkoutStateProps) {
  // Log when showing no workout state
  debugLog('[WorkoutOverview] Showing "No Workout Available" state', {
    todaysWorkout,
    isLoadingWorkout,
    hasInitialData,
    userProgramInfo,
  })

  return (
    <div className="flex h-full items-center justify-center p-4">
      <div className="text-center max-w-md">
        <h2 className="mb-4 text-2xl font-semibold text-text-primary">
          No Workout Available
        </h2>
        <div className="space-y-3 text-text-secondary">
          <p>It looks like you don't have a workout program assigned yet.</p>
          <p className="text-sm">This could happen if:</p>
          <ul className="text-sm text-left list-disc list-inside space-y-1">
            <li>You're a new user and haven't been assigned a program</li>
            <li>Your current program has ended</li>
            <li>There's a sync issue with your account</li>
          </ul>
        </div>

        <div className="mt-6 space-y-3">
          <button
            onClick={() => window.location.reload()}
            className="w-full px-6 py-3 min-h-[56px] bg-gradient-metallic-gold text-text-inverse font-semibold tracking-wider rounded-theme shadow-theme-md hover:shadow-theme-lg hover:shadow-xl hover:shadow-brand-primary/20 transition-all shimmer-hover text-shadow-sm"
          >
            Refresh Page
          </button>

          <p className="text-sm text-text-tertiary">
            If this continues, please contact support or check the Dr. Muscle
            mobile app.
          </p>
        </div>

        {/* Show debug info only in development */}
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-6">
            <summary className="cursor-pointer text-sm text-text-tertiary hover:text-text-secondary">
              Show Debug Info
            </summary>
            <div className="mt-2 p-4 bg-bg-secondary rounded-theme text-left text-xs border border-brand-primary/10">
              <div className="space-y-2">
                <div>
                  <p className="font-semibold">userProgramInfo:</p>
                  <pre className="whitespace-pre-wrap overflow-auto max-h-40">
                    {userProgramInfo
                      ? JSON.stringify(userProgramInfo, null, 2)
                      : 'null'}
                  </pre>
                </div>
                <div>
                  <p className="font-semibold">todaysWorkout:</p>
                  <pre className="whitespace-pre-wrap overflow-auto max-h-40">
                    {todaysWorkout
                      ? JSON.stringify(todaysWorkout, null, 2)
                      : 'null'}
                  </pre>
                </div>
                <div>
                  <p className="font-semibold">Loading states:</p>
                  <pre className="whitespace-pre-wrap">
                    {JSON.stringify(
                      { isLoadingWorkout, hasInitialData },
                      null,
                      2
                    )}
                  </pre>
                </div>
              </div>
            </div>
          </details>
        )}
      </div>
    </div>
  )
}

interface StatusIndicatorsProps {
  isOffline: boolean
  isRefreshing: boolean
}

export function StatusIndicators({
  isOffline,
  isRefreshing,
}: StatusIndicatorsProps) {
  if (!isOffline && !isRefreshing) return null

  return (
    <div className="mb-4 text-center">
      {isRefreshing && (
        <Badge variant="primary" size="sm">
          Refreshing workout...
        </Badge>
      )}
      {isOffline && (
        <Badge variant="warning" size="sm">
          Offline Mode
        </Badge>
      )}
    </div>
  )
}
