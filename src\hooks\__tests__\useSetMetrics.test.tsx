import { describe, it, expect } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useSetMetrics } from '../useSetMetrics'
import type { RecommendationModel } from '@/types'

describe('useSetMetrics', () => {
  const mockRecommendation: RecommendationModel = {
    Id: 1,
    WarmupsCount: 2,
    Series: 3,
    HistorySet: [
      // Warmup sets
      { Id: 1, Reps: 8, Weight: { Kg: 40, Lb: 88 }, IsWarmups: true },
      { Id: 2, Reps: 10, Weight: { Kg: 50, Lb: 110 }, IsWarmups: true },
      // Work sets
      { Id: 3, Reps: 12, Weight: { Kg: 60, Lb: 132 }, IsWarmups: false },
      { Id: 4, Reps: 10, Weight: { Kg: 65, Lb: 143 }, IsWarmups: false },
    ],
    FirstWorkSetReps: 12,
    FirstWorkSetWeight: { Kg: 60, Lb: 132 },
    FirstWorkSet1RM: { Kg: 80, Lb: 176 },
  } as RecommendationModel

  describe('warmup sets', () => {
    it('should return last time data from HistorySet for warmup', () => {
      const { result } = renderHook(() =>
        useSetMetrics({
          recommendation: mockRecommendation,
          currentSetIndex: 0,
          isWarmup: true,
          unit: 'kg',
          isFirstWorkSet: false,
        })
      )

      expect(result.current.lastTimeReps).toBe(8)
      expect(result.current.lastTimeWeight).toBe(40)
      expect(result.current.oneRMProgress).toBeNull()
    })

    it('should return last time data for second warmup set', () => {
      const { result } = renderHook(() =>
        useSetMetrics({
          recommendation: mockRecommendation,
          currentSetIndex: 1,
          isWarmup: true,
          unit: 'lbs',
          isFirstWorkSet: false,
        })
      )

      expect(result.current.lastTimeReps).toBe(10)
      expect(result.current.lastTimeWeight).toBe(110)
      expect(result.current.oneRMProgress).toBeNull()
    })
  })

  describe('first work set', () => {
    it('should return FirstWorkSet data for first work set', () => {
      const { result } = renderHook(() =>
        useSetMetrics({
          recommendation: mockRecommendation,
          currentSetIndex: 2, // First work set (after 2 warmups)
          isWarmup: false,
          unit: 'kg',
          isFirstWorkSet: true,
        })
      )

      expect(result.current.lastTimeReps).toBe(12)
      expect(result.current.lastTimeWeight).toBe(60)
    })

    it('should calculate 1RM progress for first work set with current data', () => {
      const { result } = renderHook(() =>
        useSetMetrics({
          recommendation: mockRecommendation,
          currentSetIndex: 2,
          isWarmup: false,
          unit: 'kg',
          isFirstWorkSet: true,
          currentReps: 12,
          currentWeight: 70, // Increased from 60kg
        })
      )

      // Expected 1RM: 70 * (1 + 12/30) = 70 * 1.4 = 98kg
      // Previous 1RM: 80kg
      // Progress: (98 - 80) / 80 * 100 = 22.5%
      expect(result.current.oneRMProgress).toBeCloseTo(22.5, 1)
    })

    it('should handle lbs unit for 1RM progress calculation', () => {
      const { result } = renderHook(() =>
        useSetMetrics({
          recommendation: mockRecommendation,
          currentSetIndex: 2,
          isWarmup: false,
          unit: 'lbs',
          isFirstWorkSet: true,
          currentReps: 12,
          currentWeight: 154, // Increased from 132lbs
        })
      )

      // Expected 1RM: 154 * (1 + 12/30) = 154 * 1.4 = 215.6lbs
      // Previous 1RM: 176lbs
      // Progress: (215.6 - 176) / 176 * 100 = 22.5%
      expect(result.current.oneRMProgress).toBeCloseTo(22.5, 1)
    })
  })

  describe('subsequent work sets', () => {
    it('should return filtered work set data for second work set', () => {
      const { result } = renderHook(() =>
        useSetMetrics({
          recommendation: mockRecommendation,
          currentSetIndex: 3, // Second work set
          isWarmup: false,
          unit: 'kg',
          isFirstWorkSet: false,
        })
      )

      expect(result.current.lastTimeReps).toBe(10) // From second work set in history
      expect(result.current.lastTimeWeight).toBe(65)
      expect(result.current.oneRMProgress).toBeNull()
    })
  })

  describe('edge cases', () => {
    it('should return null values when no recommendation', () => {
      const { result } = renderHook(() =>
        useSetMetrics({
          recommendation: null,
          currentSetIndex: 0,
          isWarmup: false,
          unit: 'kg',
          isFirstWorkSet: false,
        })
      )

      expect(result.current.lastTimeReps).toBeNull()
      expect(result.current.lastTimeWeight).toBeNull()
      expect(result.current.oneRMProgress).toBeNull()
    })

    it('should handle missing HistorySet data', () => {
      const recommendationWithoutHistory = {
        ...mockRecommendation,
        HistorySet: null,
      }

      const { result } = renderHook(() =>
        useSetMetrics({
          recommendation: recommendationWithoutHistory,
          currentSetIndex: 0,
          isWarmup: true,
          unit: 'kg',
          isFirstWorkSet: false,
        })
      )

      expect(result.current.lastTimeReps).toBeNull()
      expect(result.current.lastTimeWeight).toBeNull()
    })

    it('should handle missing FirstWorkSet1RM data', () => {
      const recommendationWithout1RM = {
        ...mockRecommendation,
        FirstWorkSet1RM: null,
      }

      const { result } = renderHook(() =>
        useSetMetrics({
          recommendation: recommendationWithout1RM,
          currentSetIndex: 2,
          isWarmup: false,
          unit: 'kg',
          isFirstWorkSet: true,
          currentReps: 12,
          currentWeight: 70,
        })
      )

      expect(result.current.oneRMProgress).toBeNull()
    })

    it('should handle zero or negative previous 1RM', () => {
      const recommendationWithZero1RM = {
        ...mockRecommendation,
        FirstWorkSet1RM: { Kg: 0, Lb: 0 },
      }

      const { result } = renderHook(() =>
        useSetMetrics({
          recommendation: recommendationWithZero1RM,
          currentSetIndex: 2,
          isWarmup: false,
          unit: 'kg',
          isFirstWorkSet: true,
          currentReps: 12,
          currentWeight: 70,
        })
      )

      expect(result.current.oneRMProgress).toBeNull()
    })

    it('should handle single rep calculations (1RM = weight)', () => {
      const { result } = renderHook(() =>
        useSetMetrics({
          recommendation: mockRecommendation,
          currentSetIndex: 2,
          isWarmup: false,
          unit: 'kg',
          isFirstWorkSet: true,
          currentReps: 1,
          currentWeight: 90,
        })
      )

      // For 1 rep, 1RM = weight = 90kg
      // Previous 1RM: 80kg
      // Progress: (90 - 80) / 80 * 100 = 12.5%
      expect(result.current.oneRMProgress).toBeCloseTo(12.5, 1)
    })
  })
})
