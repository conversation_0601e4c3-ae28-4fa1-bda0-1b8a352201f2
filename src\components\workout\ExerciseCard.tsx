'use client'

import React, { useEffect, useState } from 'react'
import type {
  ExerciseWorkSetsModel,
  ExerciseModel,
  WorkoutTemplateModel,
} from '@/types'
import { SetList } from './SetList'
import { ExerciseSwapModal } from './ExerciseSwapModal'
import { ExerciseCardActions } from './ExerciseCardActions'
import { ExerciseItemSkeleton } from '@/components/ui/Skeletons'
import { useWorkoutStore } from '@/stores/workoutStore'
import { logger } from '@/utils/logger'

interface ExerciseCardProps {
  exercise: ExerciseWorkSetsModel
  onExerciseClick: (exerciseId: number) => void
  onRetry: (exerciseId: number) => void
  isCurrentExercise?: boolean
  workout?: WorkoutTemplateModel | null
}

export function ExerciseCard({
  exercise,
  onExerciseClick,
  onRetry,
  isCurrentExercise = false,
  workout,
}: ExerciseCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isSwapModalOpen, setIsSwapModalOpen] = useState(false)
  const currentWorkoutFromStore = useWorkoutStore(
    (state) => state.currentWorkout
  )
  const currentWorkout = workout || currentWorkoutFromStore

  // Auto-expand current exercise
  useEffect(() => {
    if (isCurrentExercise) {
      setIsExpanded(true)
    }
  }, [isCurrentExercise])

  const handleCardClick = () => {
    onExerciseClick(exercise.Id)
  }

  const handleRetryClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onRetry(exercise.Id)
  }

  const toggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsExpanded(!isExpanded)
  }

  const handleSwapClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!currentWorkout) {
      logger.warn('[ExerciseCard] No workout available for swap')
      // TODO: Show toast notification when implemented
      return
    }
    setIsSwapModalOpen(true)
  }

  const handleSwap = async (result: {
    sourceExercise: { Label: string }
    targetExercise: { Label: string }
    savedToDb: boolean
  }) => {
    try {
      setIsSwapModalOpen(false)

      if (result.savedToDb) {
        // For permanent swaps, the workout template has been modified on the server
        logger.log(
          `[ExerciseCard] Permanent swap saved: ${result.sourceExercise.Label} -> ${result.targetExercise.Label}`
        )
        // You might want to trigger a workout reload here for permanent swaps
        // window.location.reload() or refetch workout data
      } else {
        // For temporary swaps, the change is applied locally
        logger.log(
          `[ExerciseCard] Temporary swap applied: ${result.sourceExercise.Label} -> ${result.targetExercise.Label}`
        )
      }
    } catch (error) {
      logger.error('[ExerciseCard] Failed to handle swap result:', error)
    }
  }

  const handleSkipExercise = (exerciseId: number) => {
    logger.log(`[ExerciseCard] Skipping exercise ${exerciseId}`)
    // TODO: Implement skip logic
    // This might involve marking the exercise as skipped and moving to the next one
  }

  const handleViewHistory = (exerciseId: number) => {
    logger.log(`[ExerciseCard] Viewing history for exercise ${exerciseId}`)
    // TODO: Implement view history logic
    // This might open a modal or navigate to a history page
  }

  const handleAddNote = (exerciseId: number) => {
    logger.log(`[ExerciseCard] Adding note for exercise ${exerciseId}`)
    // TODO: Implement add note logic
    // This might open a modal for entering notes
  }

  // Show skeleton if loading sets
  if (exercise.isLoadingSets && exercise.sets.length === 0) {
    return <ExerciseItemSkeleton />
  }

  return (
    <div
      className={`rounded-theme bg-gradient-overlay-subtle bg-bg-secondary p-4 shadow-theme-md hover:shadow-theme-lg transition-all duration-300 border border-brand-primary/5 hover:border-brand-primary/20 ${
        isCurrentExercise ? 'ring-2 ring-brand-primary shadow-theme-lg' : ''
      } ${exercise.IsFinished ? 'opacity-60' : ''}`}
      onClick={handleCardClick}
      role="button"
      tabIndex={0}
      aria-label={`Exercise: ${exercise.Label}`}
      aria-expanded={isExpanded}
      data-testid="exercise-card"
    >
      {/* Exercise Header */}
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="font-heading font-medium text-text-primary tracking-luxury text-shadow-sm flex items-center">
            {/* Status indicator dot */}
            <span className="mr-2 text-lg">
              {(() => {
                if (exercise.IsFinished) {
                  return <span className="text-brand-primary">●</span>
                }
                if (exercise.IsInProgress) {
                  return <span className="text-yellow-500">●</span>
                }
                return <span className="text-text-tertiary">○</span>
              })()}
            </span>
            {exercise.Label}
            {exercise.IsFinished && (
              <span className="ml-2 text-sm text-brand-primary text-shadow-gold">
                ✓
              </span>
            )}
            {exercise.IsSwapTarget && (
              <span className="ml-2 px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded">
                Swapped
              </span>
            )}
          </h3>

          {/* Quick Stats */}
          <div className="mt-1 text-sm text-text-secondary">
            {exercise.sets.length > 0 && (
              <span>{exercise.sets.length} sets</span>
            )}
            {exercise.IsBodyweight && (
              <span className="ml-2">• Bodyweight</span>
            )}
            {exercise.IsNextExercise && (
              <span className="ml-2 text-brand-primary">• Next</span>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <ExerciseCardActions
          exercise={exercise as ExerciseModel}
          isExpanded={isExpanded}
          hasSets={exercise.sets.length > 0}
          onSwapClick={handleSwapClick}
          onToggleExpand={toggleExpand}
          onSkipExercise={handleSkipExercise}
          onViewHistory={handleViewHistory}
          onAddNote={handleAddNote}
        />
      </div>

      {/* Error State */}
      {exercise.setsError && (
        <div className="mt-3 rounded-md bg-red-50 p-3">
          <div className="flex items-center justify-between">
            <p className="text-sm text-red-600">{exercise.setsError}</p>
            <button
              onClick={handleRetryClick}
              className="ml-2 text-sm font-medium text-red-600 hover:text-red-500"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Sets List (Expandable) */}
      {isExpanded && (
        <div className="mt-4 animate-fadeIn">
          <SetList sets={exercise.sets} isLoading={exercise.isLoadingSets} />
        </div>
      )}

      {/* Loading Indicator for Sets */}
      {exercise.isLoadingSets && exercise.sets.length === 0 && (
        <div className="mt-3 text-center text-sm text-text-secondary">
          Loading sets...
        </div>
      )}

      {/* Exercise Swap Modal */}
      {currentWorkout && isSwapModalOpen && (
        <ExerciseSwapModal
          isOpen={isSwapModalOpen}
          currentExercise={exercise as ExerciseModel}
          workout={currentWorkout}
          onSwap={handleSwap}
          onClose={() => setIsSwapModalOpen(false)}
        />
      )}
    </div>
  )
}
