import { render, screen } from '@testing-library/react'
import { ProgramStats } from '../ProgramStats'
import type { UserStats } from '@/types'

// Mock the SwipeableStatCard component
vi.mock('../SwipeableStatCard', () => ({
  SwipeableStatCard: ({ stats, isLoading }: any) => (
    <div data-testid="swipeable-stat-card">
      {isLoading ? (
        <div>Loading...</div>
      ) : (
        <div>
          <div>{stats?.weekStreak ?? 0}</div>
          <div>Week Streak</div>
        </div>
      )}
    </div>
  ),
}))

describe('ProgramStats V2', () => {
  const mockStats: UserStats = {
    weekStreak: 5,
    workoutsCompleted: 42,
    lbsLifted: 12500,
  }

  it('renders SwipeableStatCard instead of individual stat cards', () => {
    render(
      <ProgramStats
        stats={mockStats}
        isLoadingStats={false}
        showStatsLoaded={false}
      />
    )

    // Should render the swipeable stat card
    expect(screen.getByTestId('swipeable-stat-card')).toBeInTheDocument()

    // Should not render individual LoadingAnimatedStat components
    expect(screen.queryByText('Weeks streak')).not.toBeInTheDocument()
    expect(screen.queryByText('Workouts done')).not.toBeInTheDocument()
    expect(screen.queryByText('Lbs lifted')).not.toBeInTheDocument()
  })

  it('passes loading state to SwipeableStatCard', () => {
    render(
      <ProgramStats stats={mockStats} isLoadingStats showStatsLoaded={false} />
    )

    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })

  it('removes loading message UI since SwipeableStatCard handles its own loading', () => {
    render(
      <ProgramStats stats={mockStats} isLoadingStats showStatsLoaded={false} />
    )

    // Should not show the old loading message
    expect(screen.queryByText('Loading your stats...')).not.toBeInTheDocument()
  })

  it('applies minimal V2 styling without extra padding', () => {
    const { container } = render(
      <ProgramStats
        stats={mockStats}
        isLoadingStats={false}
        showStatsLoaded={false}
      />
    )

    // Should have minimal or no padding
    const wrapper = container.firstChild
    expect(wrapper).not.toHaveClass('py-6')
  })
})
