import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { SetCell } from '../SetCell'
import type { RecommendationModel } from '@/types'

describe('SetCell - kg increment override', () => {
  const defaultProps = {
    setNo: 1,
    reps: 10,
    weight: 50,
    isActive: true,
    unit: 'kg' as const,
    onWeightChange: vi.fn(),
    onRepsChange: vi.fn(),
    onSave: vi.fn(),
    onComplete: vi.fn(),
    onSkip: vi.fn(),
    onQuickComplete: vi.fn(),
    isCompleted: false,
    isSkipped: false,
    isSaving: false,
  }

  describe('Force kg increments to 1 regardless of API response', () => {
    it('should always increment by 1 for kg even when API returns different value', () => {
      const onWeightChange = vi.fn()
      const recommendation: Partial<RecommendationModel> = {
        Increments: { Kg: 2, Lb: 2.5 }, // API returns 2kg increment (wrong)
      }

      render(
        <SetCell
          {...defaultProps}
          onWeightChange={onWeightChange}
          recommendation={recommendation as RecommendationModel}
        />
      )

      // Find and click the weight increment button
      const incrementButton = screen.getByLabelText('Increase weight')
      fireEvent.click(incrementButton)

      // Should increment by 1 (not 2)
      expect(onWeightChange).toHaveBeenCalledWith(51) // 50 + 1
    })

    it('should always decrement by 1 for kg even when API returns different value', () => {
      const onWeightChange = vi.fn()
      const recommendation: Partial<RecommendationModel> = {
        Increments: { Kg: 2.5, Lb: 5 }, // API returns 2.5kg increment
      }

      render(
        <SetCell
          {...defaultProps}
          onWeightChange={onWeightChange}
          recommendation={recommendation as RecommendationModel}
        />
      )

      // Find and click the weight decrement button
      const decrementButton = screen.getByLabelText('Decrease weight')
      fireEvent.click(decrementButton)

      // Should decrement by 1 (not 2.5)
      expect(onWeightChange).toHaveBeenCalledWith(49) // 50 - 1
    })

    it('should respect API increments for lbs unit', () => {
      const onWeightChange = vi.fn()
      const recommendation: Partial<RecommendationModel> = {
        Increments: { Kg: 2, Lb: 5 }, // API values
      }

      render(
        <SetCell
          {...defaultProps}
          unit="lbs"
          weight={100}
          onWeightChange={onWeightChange}
          recommendation={recommendation as RecommendationModel}
        />
      )

      const incrementButton = screen.getByLabelText('Increase weight')
      fireEvent.click(incrementButton)

      // Should use API value for lbs
      expect(onWeightChange).toHaveBeenCalledWith(105) // 100 + 5
    })

    it('should handle edge cases for kg increments', () => {
      const testCases = [
        { apiIncrement: 0, expectedIncrement: 1 },
        { apiIncrement: 0.5, expectedIncrement: 1 },
        { apiIncrement: 3, expectedIncrement: 1 },
        { apiIncrement: 10, expectedIncrement: 1 },
      ]

      testCases.forEach(({ apiIncrement, expectedIncrement }) => {
        const onWeightChange = vi.fn()
        const recommendation: Partial<RecommendationModel> = {
          Increments: { Kg: apiIncrement, Lb: 2.5 },
        }

        const { unmount } = render(
          <SetCell
            {...defaultProps}
            weight={50}
            onWeightChange={onWeightChange}
            recommendation={recommendation as RecommendationModel}
          />
        )

        const incrementButton = screen.getByLabelText('Increase weight')
        fireEvent.click(incrementButton)

        expect(onWeightChange).toHaveBeenCalledWith(50 + expectedIncrement)

        onWeightChange.mockClear()
        unmount()
      })
    })

    it('should log increment override for debugging', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      const recommendation: Partial<RecommendationModel> = {
        Increments: { Kg: 2, Lb: 2.5 },
      }

      render(
        <SetCell
          {...defaultProps}
          recommendation={recommendation as RecommendationModel}
        />
      )

      const incrementButton = screen.getByLabelText('Increase weight')
      fireEvent.click(incrementButton)

      // Should log the override
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('SetCell: Weight increment override'),
        expect.objectContaining({
          unit: 'kg',
          apiIncrement: 2,
          forcedIncrement: 1,
        })
      )

      consoleSpy.mockRestore()
    })
  })
})
