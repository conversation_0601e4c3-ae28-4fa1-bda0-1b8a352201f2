import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { apiClient } from '@/api/client'

import { useAuthStore } from '@/stores/authStore'

// Mock window.location
const mockLocation = {
  href: 'http://localhost:3000/workout',
  origin: 'http://localhost:3000',
  replace: vi.fn(),
}

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
})

// Mock the auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: {
    getState: vi.fn(() => ({
      logout: vi.fn(),
      token: 'test-token',
    })),
  },
}))

// Mock logger
vi.mock('@/utils/logger', () => ({
  logger: {
    warn: vi.fn(),
    error: vi.fn(),
    log: vi.fn(),
  },
}))

describe('API Client - Auth Redirect', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocation.href = 'http://localhost:3000/workout'
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should redirect to login immediately on 401 response', async () => {
    const error = {
      response: { status: 401, data: { error: 'Unauthorized' } },
      config: { _retry: false },
      isAxiosError: true,
    }

    // Trigger the response interceptor error handler
    try {
      await apiClient.interceptors.response.handlers[0].rejected(error)
    } catch (e) {
      // Expected to throw
    }

    // Should redirect to login
    expect(mockLocation.href).toBe('/login')
  })

  it('should clear auth state before redirecting on 401', async () => {
    const mockLogout = vi.fn()

    vi.mocked(useAuthStore.getState).mockReturnValue({
      logout: mockLogout,
      token: 'test-token',
    } as any)

    const error = {
      response: { status: 401, data: { error: 'Token expired' } },
      config: { _retry: false },
      isAxiosError: true,
    }

    try {
      await apiClient.interceptors.response.handlers[0].rejected(error)
    } catch (e) {
      // Expected to throw
    }

    // Should have cleared auth token
    expect(apiClient.defaults.headers.common['Authorization']).toBeUndefined()

    // Should redirect to login
    expect(mockLocation.href).toBe('/login')
  })

  it('should not retry request on 401 to prevent delays', async () => {
    const error = {
      response: { status: 401, data: { error: 'Unauthorized' } },
      config: {
        _retry: false,
        url: '/api/workout',
        method: 'GET',
      },
      isAxiosError: true,
    }

    let requestMade = false

    // Mock the request method to track if retry happens
    apiClient.request = vi.fn().mockImplementation(() => {
      requestMade = true
      return Promise.reject(new Error('Should not retry'))
    })

    try {
      await apiClient.interceptors.response.handlers[0].rejected(error)
    } catch (e) {
      // Expected to throw
    }

    // Should not have retried the request
    expect(requestMade).toBe(false)

    // Should redirect immediately
    expect(mockLocation.href).toBe('/login')
  })

  it('should handle 401 during workout operations gracefully', async () => {
    mockLocation.href = 'http://localhost:3000/workout/exercise/123'

    const error = {
      response: {
        status: 401,
        data: { error_description: 'Your session has expired' },
      },
      config: {
        _retry: false,
        url: '/api/SaveSetLog',
        method: 'POST',
        data: JSON.stringify({ exerciseId: 123, reps: 10 }),
      },
      isAxiosError: true,
    }

    try {
      await apiClient.interceptors.response.handlers[0].rejected(error)
    } catch (e) {
      // Expected to throw
    }

    // Should redirect from exercise page to login
    expect(mockLocation.href).toBe('/login')
  })

  it('should differentiate between auth errors and other errors', async () => {
    // Test network error - should not redirect
    const networkError = {
      message: 'Network Error',
      code: 'ERR_NETWORK',
      config: { _retry: false },
      isAxiosError: true,
    }

    mockLocation.href = 'http://localhost:3000/workout'

    try {
      await apiClient.interceptors.response.handlers[0].rejected(networkError)
    } catch (e) {
      // Expected to throw
    }

    // Should NOT redirect on network error
    expect(mockLocation.href).toBe('http://localhost:3000/workout')

    // Test 500 error - should not redirect
    const serverError = {
      response: { status: 500, data: { error: 'Internal Server Error' } },
      config: { _retry: false },
      isAxiosError: true,
    }

    try {
      await apiClient.interceptors.response.handlers[0].rejected(serverError)
    } catch (e) {
      // Expected to throw
    }

    // Should NOT redirect on server error
    expect(mockLocation.href).toBe('http://localhost:3000/workout')
  })
})
