import { test, expect } from '@playwright/test'
import { login } from './helpers/auth-helper'
import { startWorkout } from './helpers/workout-helper'

test.describe('RIR Popup in V2 Exercise Page', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
  })

  test('should show RIR popup after first work set in v2 exercise page', async ({
    page,
  }) => {
    // Start workout
    await startWorkout(page)

    // Get first exercise from the workout (assuming it has warmups)
    const firstExerciseCard = page.locator('.exercise-card').first()
    const exerciseLink = await firstExerciseCard.getAttribute('href')

    // Extract exercise ID and navigate to v2 page
    const exerciseId = exerciseLink?.split('/').pop()
    await page.goto(`/workout/exercise-v2/${exerciseId}`)

    // Wait for exercise page to load
    await page.waitForSelector('[data-testid="current-set-card"]')

    // Complete warmup sets
    const warmupSets = await page.locator('text=/Warmup/i').count()
    // eslint-disable-next-line no-await-in-loop
    for (let i = 0; i < warmupSets; i++) {
      // eslint-disable-next-line no-await-in-loop
      await page.click('text=Save set')
      // eslint-disable-next-line no-await-in-loop
      await page.waitForTimeout(500) // Small delay for animation
    }

    // Now we should be on the first work set
    await expect(page.locator('text=/Set 1/i')).toBeVisible()

    // Save the first work set
    await page.click('text=Save set')

    // RIR picker should appear
    await expect(page.locator('text=How hard was that?')).toBeVisible()

    // Verify all RIR options are present
    await expect(page.locator('text=Very hard (0 left)')).toBeVisible()
    await expect(page.locator('text=Could do 1-2 more')).toBeVisible()
    await expect(page.locator('text=Could do 3-4 more')).toBeVisible()
    await expect(page.locator('text=Could do 5-6 more')).toBeVisible()
    await expect(page.locator('text=Could do 7+ more')).toBeVisible()

    // Select an RIR option
    await page.click('text=Could do 1-2 more')

    // RIR picker should close and rest timer should start
    await expect(page.locator('text=How hard was that?')).not.toBeVisible()
    await expect(page.locator('[data-testid="rest-timer"]')).toBeVisible()
  })

  test('should not show RIR popup for warmup sets', async ({ page }) => {
    // Start workout
    await startWorkout(page)

    // Get first exercise
    const firstExerciseCard = page.locator('.exercise-card').first()
    const exerciseLink = await firstExerciseCard.getAttribute('href')
    const exerciseId = exerciseLink?.split('/').pop()
    await page.goto(`/workout/exercise-v2/${exerciseId}`)

    // Wait for exercise page
    await page.waitForSelector('[data-testid="current-set-card"]')

    // Verify we're on a warmup set
    await expect(page.locator('text=/Warmup/i')).toBeVisible()

    // Save the warmup set
    await page.click('text=Save set')

    // RIR picker should NOT appear
    await expect(page.locator('text=How hard was that?')).not.toBeVisible()

    // Should proceed to rest timer or next set
    await expect(page.locator('[data-testid="rest-timer"]')).toBeVisible()
  })

  test('should handle RIR cancel', async ({ page }) => {
    // Start workout and navigate to first work set
    await startWorkout(page)
    const firstExerciseCard = page.locator('.exercise-card').first()
    const exerciseLink = await firstExerciseCard.getAttribute('href')
    const exerciseId = exerciseLink?.split('/').pop()
    await page.goto(`/workout/exercise-v2/${exerciseId}`)

    await page.waitForSelector('[data-testid="current-set-card"]')

    // Complete warmups
    const warmupSets = await page.locator('text=/Warmup/i').count()
    // eslint-disable-next-line no-await-in-loop
    for (let i = 0; i < warmupSets; i++) {
      // eslint-disable-next-line no-await-in-loop
      await page.click('text=Save set')
      // eslint-disable-next-line no-await-in-loop
      await page.waitForTimeout(500)
    }

    // Save first work set
    await page.click('text=Save set')

    // RIR picker appears
    await expect(page.locator('text=How hard was that?')).toBeVisible()

    // Click cancel
    await page.click('text=Cancel')

    // RIR picker should close
    await expect(page.locator('text=How hard was that?')).not.toBeVisible()

    // Should continue to rest timer
    await expect(page.locator('[data-testid="rest-timer"]')).toBeVisible()
  })
})
