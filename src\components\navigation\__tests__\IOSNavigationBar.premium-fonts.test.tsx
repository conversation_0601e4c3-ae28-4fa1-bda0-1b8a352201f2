import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { IOSNavigationBar } from '../IOSNavigationBar'

/**
 * Test suite for PR #334 premium font size implementation
 */
describe('IOSNavigationBar Premium Font Sizes', () => {
  describe('Exercise name font size - text-5xl (60px)', () => {
    it('should display exercise name at text-5xl when on exercise page', () => {
      render(
        <IOSNavigationBar title="Bench Press" isExercisePage showBackButton />
      )

      const titleElement = screen.getByRole('heading', { level: 1 })

      // Test will fail initially - need to implement text-5xl
      expect(titleElement).toHaveClass('text-5xl')
      expect(titleElement).toHaveClass('font-bold')
    })

    it('should maintain regular font size on non-exercise pages', () => {
      render(
        <IOSNavigationBar
          title="Workout Overview"
          isExercisePage={false}
          showBackButton
        />
      )

      const titleElement = screen.getByRole('heading', { level: 1 })

      // Should keep existing text-lg for non-exercise pages
      expect(titleElement).toHaveClass('text-lg')
      expect(titleElement).not.toHaveClass('text-5xl')
    })

    it('should handle long exercise names with text-5xl font', () => {
      const longExerciseName =
        'Incline Dumbbell Chest Press With Resistance Bands'

      render(
        <IOSNavigationBar
          title={longExerciseName}
          isExercisePage
          showBackButton
        />
      )

      const titleElement = screen.getByRole('heading', { level: 1 })

      // Should use text-5xl even for long names
      expect(titleElement).toHaveClass('text-5xl')
      // Should maintain truncate for overflow handling
      expect(titleElement).toHaveClass('truncate')
    })

    it('should apply premium minimal guidelines for exercise page typography', () => {
      render(
        <IOSNavigationBar title="Bench Press" isExercisePage showBackButton />
      )

      const titleElement = screen.getByRole('heading', { level: 1 })

      // Premium minimal style guide requirements
      expect(titleElement).toHaveClass('text-5xl') // 60px
      expect(titleElement).toHaveClass('font-bold') // Bold weight
      expect(titleElement).toHaveClass('text-text-inverse') // White on gradient
    })
  })

  describe('Mobile viewport compatibility', () => {
    it('should maintain 44px touch targets with larger fonts', () => {
      render(
        <IOSNavigationBar title="Bench Press" isExercisePage showBackButton />
      )

      const backButton = screen.getByRole('button', { name: /go back/i })

      // Should maintain minimum 44px touch target
      expect(backButton).toHaveClass('px-2')
      expect(backButton).toHaveClass('py-2')
    })

    it('should fit within header height constraints', () => {
      render(
        <IOSNavigationBar title="Bench Press" isExercisePage showBackButton />
      )

      const header = screen.getByRole('banner')
      const titleContainer = header.querySelector(
        '.flex.items-center.justify-center'
      )

      // Header should maintain 66px height
      expect(header.querySelector('.h-\\[66px\\]')).toBeInTheDocument()

      // Title should be vertically centered
      expect(titleContainer).toHaveClass('items-center')
      expect(titleContainer).toHaveClass('justify-center')
    })
  })

  describe('Typography consistency', () => {
    it('should maintain gold gradient background with premium fonts', () => {
      render(
        <IOSNavigationBar title="Bench Press" isExercisePage showBackButton />
      )

      const header = screen.getByRole('banner')

      // Should preserve existing gradient
      expect(header).toHaveClass('bg-gradient-to-r')
      expect(header).toHaveClass('from-brand-gold-start')
      expect(header).toHaveClass('to-brand-gold-end')
    })

    it('should apply white text color for contrast on gradient', () => {
      render(
        <IOSNavigationBar title="Bench Press" isExercisePage showBackButton />
      )

      const titleElement = screen.getByRole('heading', { level: 1 })

      // Should use inverse text color on gradient background
      expect(titleElement).toHaveClass('text-text-inverse')
    })
  })
})
