'use client'

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useMemo,
} from 'react'
import { useRouter } from 'next/navigation'
import { isValidExerciseId } from '@/utils/exerciseValidation'

interface WorkoutProgress {
  totalExercises: number
  currentExerciseIndex: number
  visitedExercises: number[]
}

interface NavigationContextValue {
  title: string
  setTitle: (title: string) => void
  setInfo: string
  setSetInfo: (setInfo: string) => void
  canGoBack: boolean
  goBack: () => void
  navigationStack: string[]
  pushRoute: (route: string) => void
  popRoute: () => void

  // Enhanced navigation methods
  navigateWithHistory: (route: string) => void
  clearNavigationStack: () => void
  setCurrentRoute: () => void
  handleBrowserBack: () => void

  // Workout-specific navigation
  navigateToWorkout: () => void
  navigateToExercise: (exerciseId: number) => void
  navigateToRestTimer: (options?: { betweenSets?: boolean }) => void
  navigateToComplete: () => void

  // Workout progress tracking
  workoutProgress: WorkoutProgress
  startWorkoutNavigation: (exerciseIds: number[]) => void
  markExerciseVisited: (exerciseId: number) => void

  // Set progress tracking (for progress bar in nav)
  setProgress: {
    totalSets: number
    completedSets: number
    progressValue: number
  }
  setSetProgress: (totalSets: number, completedSets: number) => void
}

const NavigationContext = createContext<NavigationContextValue | null>(null)

interface NavigationProviderProps {
  children: React.ReactNode
}

export function NavigationProvider({ children }: NavigationProviderProps) {
  const router = useRouter()
  const [title, setTitle] = useState('')
  const [setInfo, setSetInfo] = useState('')
  const [navigationStack, setNavigationStack] = useState<string[]>([])
  const [workoutProgress, setWorkoutProgress] = useState<WorkoutProgress>({
    totalExercises: 0,
    currentExerciseIndex: 0,
    visitedExercises: [],
  })
  const [setProgress, setSetProgressState] = useState({
    totalSets: 0,
    completedSets: 0,
    progressValue: 0,
  })

  const canGoBack = navigationStack.length > 0

  const pushRoute = useCallback((route: string) => {
    setNavigationStack((prev) => {
      // Prevent duplicate consecutive routes
      if (prev.length > 0 && prev[prev.length - 1] === route) {
        return prev
      }
      return [...prev, route]
    })
  }, [])

  const popRoute = useCallback(() => {
    setNavigationStack((prev) => prev.slice(0, -1))
  }, [])

  const goBack = useCallback(() => {
    if (canGoBack && navigationStack.length > 0) {
      const previousRoute = navigationStack[navigationStack.length - 1]
      popRoute()
      if (previousRoute) {
        router.push(previousRoute)
      }
    }
  }, [canGoBack, navigationStack, popRoute, router])

  // Enhanced navigation methods
  const navigateWithHistory = useCallback(
    (route: string) => {
      // Don't push the current route if it's the root path
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname
        if (currentPath !== '/') {
          pushRoute(currentPath)
        }
      }
      router.push(route)
    },
    [pushRoute, router]
  )

  const clearNavigationStack = useCallback(() => {
    setNavigationStack([])
  }, [])

  const setCurrentRoute = useCallback(() => {
    // For direct URL access, don't add to stack
    // Just update the current location
    // Route parameter removed since it's not used in the current implementation
  }, [])

  const handleBrowserBack = useCallback(() => {
    // Sync with browser back button
    popRoute()
  }, [popRoute])

  // Workout-specific navigation helpers
  const navigateToWorkout = useCallback(() => {
    router.push('/workout')
  }, [router])

  const navigateToExercise = useCallback(
    (exerciseId: number) => {
      if (!isValidExerciseId(exerciseId)) {
        return
      }
      router.push(`/workout/exercise/${exerciseId}`)
    },
    [router]
  )

  const navigateToRestTimer = useCallback(
    (options?: { betweenSets?: boolean }) => {
      const params = new URLSearchParams()
      if (options?.betweenSets) {
        params.set('between-sets', 'true')
      }
      const queryString = params.toString()
      router.push(`/workout/rest-timer${queryString ? `?${queryString}` : ''}`)
    },
    [router]
  )

  const navigateToComplete = useCallback(() => {
    clearNavigationStack()
    router.push('/workout/complete')
  }, [clearNavigationStack, router])

  // Workout progress tracking
  const startWorkoutNavigation = useCallback((exerciseIds: number[]) => {
    setWorkoutProgress({
      totalExercises: exerciseIds.length,
      currentExerciseIndex: 0,
      visitedExercises: [],
    })
  }, [])

  const markExerciseVisited = useCallback((exerciseId: number) => {
    setWorkoutProgress((prev) => {
      const visited = [...new Set([...prev.visitedExercises, exerciseId])]
      // Update current exercise index based on visited exercises
      const currentIndex =
        prev.totalExercises > 0
          ? Math.min(visited.length - 1, prev.totalExercises - 1)
          : 0
      return {
        ...prev,
        visitedExercises: visited,
        currentExerciseIndex: currentIndex,
      }
    })
  }, [])

  const setSetProgress = useCallback(
    (totalSets: number, completedSets: number) => {
      const progressValue =
        totalSets > 0 ? (completedSets / totalSets) * 100 : 0
      setSetProgressState({
        totalSets,
        completedSets,
        progressValue,
      })
    },
    []
  )

  const value: NavigationContextValue = useMemo(
    () => ({
      title,
      setTitle,
      setInfo,
      setSetInfo,
      canGoBack,
      goBack,
      navigationStack,
      pushRoute,
      popRoute,

      // Enhanced navigation
      navigateWithHistory,
      clearNavigationStack,
      setCurrentRoute,
      handleBrowserBack,

      // Workout navigation
      navigateToWorkout,
      navigateToExercise,
      navigateToRestTimer,
      navigateToComplete,

      // Progress tracking
      workoutProgress,
      startWorkoutNavigation,
      markExerciseVisited,

      // Set progress tracking
      setProgress,
      setSetProgress,
    }),
    [
      title,
      setTitle,
      setInfo,
      setSetInfo,
      canGoBack,
      goBack,
      navigationStack,
      pushRoute,
      popRoute,
      navigateWithHistory,
      clearNavigationStack,
      setCurrentRoute,
      handleBrowserBack,
      navigateToWorkout,
      navigateToExercise,
      navigateToRestTimer,
      navigateToComplete,
      workoutProgress,
      startWorkoutNavigation,
      markExerciseVisited,
      setProgress,
      setSetProgress,
    ]
  )

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  )
}

export function useNavigation() {
  const context = useContext(NavigationContext)
  if (!context) {
    throw new Error('useNavigation must be used within NavigationProvider')
  }
  return context
}
