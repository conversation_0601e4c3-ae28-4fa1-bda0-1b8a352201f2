import { describe, it, expect, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useSetInputHandlers } from '../useSetInputHandlers'

describe('useSetInputHandlers - Custom Increments', () => {
  it('should use default increments of 1kg when no custom increments provided', () => {
    const onChange = vi.fn()
    const { result } = renderHook(() =>
      useSetInputHandlers({
        reps: 10,
        weight: 50,
        duration: 0,
        unit: 'kg',
        onChange,
      })
    )

    act(() => {
      result.current.incrementWeight()
    })

    expect(onChange).toHaveBeenCalledWith({ reps: 10, weight: 51 })
  })

  it('should use default increments of 2.5lbs when no custom increments provided', () => {
    const onChange = vi.fn()
    const { result } = renderHook(() =>
      useSetInputHandlers({
        reps: 10,
        weight: 100,
        duration: 0,
        unit: 'lbs',
        onChange,
      })
    )

    act(() => {
      result.current.incrementWeight()
    })

    expect(onChange).toHaveBeenCalledWith({ reps: 10, weight: 102.5 })
  })

  it('should use custom increments from recommendation when provided', () => {
    const onChange = vi.fn()
    const customIncrements = { Kg: 1, Lb: 2.5 }

    const { result } = renderHook(() =>
      useSetInputHandlers({
        reps: 10,
        weight: 50,
        duration: 0,
        unit: 'kg',
        onChange,
        increments: customIncrements,
      })
    )

    act(() => {
      result.current.incrementWeight()
    })

    expect(onChange).toHaveBeenCalledWith({ reps: 10, weight: 52.5 })
  })

  it('should use custom lb increments when unit is lbs', () => {
    const onChange = vi.fn()
    const customIncrements = { Kg: 1, Lb: 2.5 }

    const { result } = renderHook(() =>
      useSetInputHandlers({
        reps: 10,
        weight: 100,
        duration: 0,
        unit: 'lbs',
        onChange,
        increments: customIncrements,
      })
    )

    act(() => {
      result.current.incrementWeight()
    })

    expect(onChange).toHaveBeenCalledWith({ reps: 10, weight: 102.5 })
  })

  it('should decrement by custom increments', () => {
    const onChange = vi.fn()
    const customIncrements = { Kg: 5, Lb: 10 }

    const { result } = renderHook(() =>
      useSetInputHandlers({
        reps: 10,
        weight: 50,
        duration: 0,
        unit: 'kg',
        onChange,
        increments: customIncrements,
      })
    )

    act(() => {
      result.current.decrementWeight()
    })

    expect(onChange).toHaveBeenCalledWith({ reps: 10, weight: 45 })
  })

  it('should allow weight to go to 0 when decrementing', () => {
    const onChange = vi.fn()

    const { result } = renderHook(() =>
      useSetInputHandlers({
        reps: 10,
        weight: 0.5,
        duration: 0,
        unit: 'kg',
        onChange,
      })
    )

    act(() => {
      result.current.decrementWeight()
    })

    const call = onChange.mock.calls[0][0]
    expect(call.reps).toBe(10)
    expect(Math.abs(call.weight)).toBe(0) // Handle -0 vs 0
  })
})
