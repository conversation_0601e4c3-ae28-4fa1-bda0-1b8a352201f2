import { useAnimation, PanInfo } from 'framer-motion'
import { useState, useCallback } from 'react'
import { vibrate } from '@/utils/haptics'

interface UseSwipeAnimationProps {
  onComplete: () => void
  onSkip: () => void
  threshold?: number
}

export function useSwipeAnimation({
  onComplete,
  onSkip,
  threshold = 100,
}: UseSwipeAnimationProps) {
  const controls = useAnimation()
  const [isDragging, setIsDragging] = useState(false)

  // Extract fade animation sequence for reuse
  const triggerFadeAnimation = useCallback(async () => {
    try {
      // First: fade out at current position
      await controls.start({ x: 0, opacity: 0, transition: { duration: 0.2 } })
      // Second: ensure we're at center with opacity 0
      await controls.start({ x: 0, opacity: 0 })
      // Third: fade in at center
      await controls.start({ x: 0, opacity: 1, transition: { duration: 0.2 } })
    } catch (error) {
      // Handle animation errors gracefully
      console.warn('Animation error:', error)
    }
  }, [controls])

  const handleDragEnd = async (
    _event: MouseEvent | TouchEvent | PointerEvent,
    info: PanInfo
  ) => {
    if (info.offset.x > threshold) {
      // Swiped right - complete
      vibrate('success')
      await controls.start({ x: 300, opacity: 0 })
      onComplete()
      // Reset to center with opacity 0 for fade effect
      await controls.start({ x: 0, opacity: 0 })
      // Fade in at center to give impression of new set
      await controls.start({ x: 0, opacity: 1, transition: { duration: 0.2 } })
    } else if (info.offset.x < -threshold) {
      // Swiped left - skip
      vibrate('warning')
      await controls.start({ x: -300, opacity: 0 })
      onSkip()
      // Reset to center with opacity 0 for fade effect
      await controls.start({ x: 0, opacity: 0 })
      // Fade in at center to give impression of new set
      await controls.start({ x: 0, opacity: 1, transition: { duration: 0.2 } })
    } else {
      // Snap back
      controls.start({ x: 0 })
    }
    setIsDragging(false)
  }

  const handleDragStart = () => {
    setIsDragging(true)
  }

  return {
    controls,
    isDragging,
    handleDragEnd,
    handleDragStart,
    triggerFadeAnimation,
  }
}
