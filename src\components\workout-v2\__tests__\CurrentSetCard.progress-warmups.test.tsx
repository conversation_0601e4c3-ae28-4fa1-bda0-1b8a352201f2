import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { CurrentSetCard } from '../CurrentSetCard'
import type { ExerciseModel } from '@/types'

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock swipe animation hook
vi.mock('@/hooks/useSwipeAnimation', () => ({
  useSwipeAnimation: () => ({
    controls: {},
    isDragging: false,
    handleDragEnd: vi.fn(),
    handleDragStart: vi.fn(),
  }),
}))

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, className, ...props }: any) => (
      <div className={className} data-testid="motion-div" {...props}>
        {children}
      </div>
    ),
  },
}))

describe('CurrentSetCard - Progress Bar Including Warmups', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsFinished: false,
  }

  const mockCurrentSet = {
    Id: 1,
    SetNo: '3',
    Reps: 8,
    Weight: { Kg: 60, Lb: 135 },
    IsWarmups: false,
    IsFinished: false,
    IsNext: true,
  }

  const mockSetData = {
    reps: 8,
    weight: 135,
    duration: 0,
  }

  const mockOnSetDataChange = vi.fn()
  const mockOnComplete = vi.fn()
  const mockOnSkip = vi.fn()

  it('should include warmup sets in progress calculation (5 warmups + 5 work sets, 1 warmup done = 10%)', () => {
    // Given: 5 warmup sets + 5 work sets, 1 warmup completed
    render(
      <CurrentSetCard
        exercise={mockExercise}
        currentSet={mockCurrentSet}
        setData={mockSetData}
        onSetDataChange={mockOnSetDataChange}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        isSaving={false}
        completedSets={1} // 1 warmup completed
        totalSets={10} // 5 warmups + 5 work sets
        unit="lbs"
      />
    )

    // When: Component renders
    // Then: Progress bar should show 10% (1/10)
    const progressBar = screen
      .getByTestId('motion-div')
      .querySelector(
        '.bg-gradient-to-r.from-brand-gold-start.to-brand-gold-end'
      )

    expect(progressBar).toHaveStyle({ width: '10%' })
  })

  it('should show 40% progress when 2 warmups + 2 work sets completed out of 5 total', () => {
    // Given: 2 warmups + 3 work sets total, 2 warmups + 2 work sets completed
    render(
      <CurrentSetCard
        exercise={mockExercise}
        currentSet={mockCurrentSet}
        setData={mockSetData}
        onSetDataChange={mockOnSetDataChange}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        isSaving={false}
        completedSets={4} // 2 warmups + 2 work sets completed
        totalSets={5} // 2 warmups + 3 work sets
        unit="lbs"
      />
    )

    // When: Component renders
    // Then: Progress bar should show 80% (4/5)
    const progressBar = screen
      .getByTestId('motion-div')
      .querySelector(
        '.bg-gradient-to-r.from-brand-gold-start.to-brand-gold-end'
      )

    expect(progressBar).toHaveStyle({ width: '80%' })
  })

  it('should handle no warmup sets correctly (work sets only)', () => {
    // Given: No warmup sets, only work sets
    render(
      <CurrentSetCard
        exercise={mockExercise}
        currentSet={mockCurrentSet}
        setData={mockSetData}
        onSetDataChange={mockOnSetDataChange}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        isSaving={false}
        completedSets={2} // 2 work sets completed
        totalSets={4} // 4 work sets total
        unit="lbs"
      />
    )

    // When: Component renders
    // Then: Progress bar should show 50% (2/4)
    const progressBar = screen
      .getByTestId('motion-div')
      .querySelector(
        '.bg-gradient-to-r.from-brand-gold-start.to-brand-gold-end'
      )

    expect(progressBar).toHaveStyle({ width: '50%' })
  })

  it('should show 0% progress when no sets completed', () => {
    // Given: No sets completed yet
    render(
      <CurrentSetCard
        exercise={mockExercise}
        currentSet={mockCurrentSet}
        setData={mockSetData}
        onSetDataChange={mockOnSetDataChange}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        isSaving={false}
        completedSets={0} // No sets completed
        totalSets={6} // 3 warmups + 3 work sets
        unit="lbs"
      />
    )

    // When: Component renders
    // Then: Progress bar should show 0%
    const progressBar = screen
      .getByTestId('motion-div')
      .querySelector(
        '.bg-gradient-to-r.from-brand-gold-start.to-brand-gold-end'
      )

    expect(progressBar).toHaveStyle({ width: '0%' })
  })

  it('should show 100% progress when all sets completed', () => {
    // Given: All sets completed
    render(
      <CurrentSetCard
        exercise={mockExercise}
        currentSet={mockCurrentSet}
        setData={mockSetData}
        onSetDataChange={mockOnSetDataChange}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        isSaving={false}
        completedSets={7} // All sets completed
        totalSets={7} // 3 warmups + 4 work sets
        unit="lbs"
      />
    )

    // When: Component renders
    // Then: Progress bar should show 100%
    const progressBar = screen
      .getByTestId('motion-div')
      .querySelector(
        '.bg-gradient-to-r.from-brand-gold-start.to-brand-gold-end'
      )

    expect(progressBar).toHaveStyle({ width: '100%' })
  })
})
