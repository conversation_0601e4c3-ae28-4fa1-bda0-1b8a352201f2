import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useWorkoutStore } from '../index'
import { getExerciseRecommendation } from '@/services/api/workout'
import type { RecommendationModel } from '@/types'

// Mock dependencies
vi.mock('@/services/api/workout')
vi.mock('@/lib/auth-utils', () => ({
  getCurrentUserEmail: vi.fn(() => '<EMAIL>'),
}))

describe('workoutStore - single cache source of truth', () => {
  const mockWorkoutId = 123
  const mockExerciseId = 456

  beforeEach(() => {
    vi.clearAllMocks()

    // Reset store
    const store = useWorkoutStore.getState()
    store.clearAllCache()

    // Set up current workout
    useWorkoutStore.setState({
      currentWorkout: {
        Id: mockWorkoutId,
        Label: 'Test Workout',
        Exercises: [
          {
            Id: mockExerciseId,
            Label: 'Test Exercise',
            SetStyle: 'Normal',
            IsFlexibility: false,
          },
        ],
      },
      exercises: [
        {
          Id: mockExerciseId,
          Label: 'Test Exercise',
          SetStyle: 'Normal',
          IsFlexibility: false,
        },
      ],
      hasHydrated: true,
    })
  })

  it('should have only one source of truth for recommendations', async () => {
    const mockRecommendation: RecommendationModel = {
      Reps: 10,
      Series: 3,
      Weight: { Kg: 20, Lb: 44 },
    }

    vi.mocked(getExerciseRecommendation).mockResolvedValue(mockRecommendation)

    const store = useWorkoutStore.getState()

    // Load recommendation
    await store.loadExerciseRecommendation(mockExerciseId)

    // Both selectors should return the same data from the same source
    const selectorResult = store.getExerciseRecommendation(mockExerciseId)
    const cacheResult = store.getCachedExerciseRecommendation(mockExerciseId)

    // Both should have the data
    expect(selectorResult).toEqual(mockRecommendation)
    expect(cacheResult).toEqual(mockRecommendation)

    // Verify it's stored in only one place (persisted cache)
    const state = useWorkoutStore.getState()
    expect(state.cachedData.exerciseRecommendations[mockExerciseId]).toEqual(
      mockRecommendation
    )

    // The in-memory Map should not exist or should not be used
    // This test will fail with current implementation since we have double caching
    expect(state.exerciseRecommendations).toBeUndefined()
  })

  it('should use persisted cache as the single source of truth', async () => {
    const mockRecommendation: RecommendationModel = {
      Reps: 8,
      Series: 4,
      Weight: { Kg: 15, Lb: 33 },
    }

    // Directly set data in persisted cache
    useWorkoutStore.setState({
      cachedData: {
        ...useWorkoutStore.getState().cachedData,
        exerciseRecommendations: {
          [mockExerciseId]: mockRecommendation,
        },
        lastUpdated: {
          ...useWorkoutStore.getState().cachedData.lastUpdated,
          exerciseRecommendations: {
            [mockExerciseId]: Date.now(),
          },
        },
      },
    })

    const store = useWorkoutStore.getState()

    // Both selectors should read from the same persisted cache
    const selectorResult = store.getExerciseRecommendation(mockExerciseId)
    const cacheResult = store.getCachedExerciseRecommendation(mockExerciseId)

    expect(selectorResult).toEqual(mockRecommendation)
    expect(cacheResult).toEqual(mockRecommendation)
  })
})
