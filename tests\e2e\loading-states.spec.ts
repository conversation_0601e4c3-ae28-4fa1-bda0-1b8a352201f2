import { test, expect } from '@playwright/test'

test.describe('Loading States', () => {
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456'
  }

  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login')
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)
    await page.getByRole('button', { name: /log in/i }).click()
    await page.waitForURL('/program')
  })

  test.describe('Initial Page Load', () => {
    test('should show skeleton loaders on initial load', async ({ page }) => {
      // Slow down network to see loading states
      await page.route('**/*', async route => {
        await new Promise(resolve => setTimeout(resolve, 1000))
        await route.continue()
      })

      // Navigate to workout
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()

      // Should show skeleton loaders immediately
      const skeletons = page.locator('[data-testid="skeleton-loader"], .skeleton')
      await expect(skeletons.first()).toBeVisible({ timeout: 100 })

      // Should have shimmer effect
      const shimmer = page.locator('[data-testid="shimmer-effect"], .shimmer')
      const hasShimmer = await shimmer.count() > 0
      expect(hasShimmer).toBeTruthy()

      // Wait for content to load
      await page.waitForURL('/workout')
      await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

      // Skeletons should disappear
      await expect(skeletons.first()).not.toBeVisible()
    })

    test('should maintain layout during loading', async ({ page }) => {
      // Get initial layout dimensions
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')
      
      const container = page.locator('[data-testid="workout-container"], main')
      const initialSize = await container.boundingBox()

      // Reload with slow network
      await page.route('**/*', async route => {
        await new Promise(resolve => setTimeout(resolve, 500))
        await route.continue()
      })

      await page.reload()

      // Check layout during loading
      const loadingSize = await container.boundingBox()
      
      // Layout should not shift significantly
      if (initialSize && loadingSize) {
        expect(Math.abs(initialSize.width - loadingSize.width)).toBeLessThan(50)
        // Height can vary more due to content
        expect(loadingSize.height).toBeGreaterThan(100)
      }
    })
  })

  test.describe('Data Fetching States', () => {
    test('should show loading indicator for data updates', async ({ page }) => {
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Look for refresh button
      const refreshButton = page.locator('[data-testid="refresh-button"], [aria-label="Refresh"]')
      if (await refreshButton.isVisible()) {
        // Slow down refresh request
        await page.route('**/api/workout/**', async route => {
          await new Promise(resolve => setTimeout(resolve, 1000))
          await route.continue()
        })

        // Click refresh
        await refreshButton.click()

        // Should show loading indicator
        const loadingIndicator = page.locator('[data-testid="refresh-loading"], [data-testid="updating"]')
        await expect(loadingIndicator).toBeVisible()

        // Should disable refresh button during load
        await expect(refreshButton).toBeDisabled()

        // Wait for load to complete
        await expect(loadingIndicator).not.toBeVisible({ timeout: 10000 })
        await expect(refreshButton).toBeEnabled()
      }
    })

    test('should handle concurrent loading states', async ({ page }) => {
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Slow down all API calls
      await page.route('**/api/**', async route => {
        await new Promise(resolve => setTimeout(resolve, 1500))
        await route.continue()
      })

      // Trigger multiple concurrent loads
      const exercise1 = page.locator('[data-testid="exercise-item"]').nth(0)
      const exercise2 = page.locator('[data-testid="exercise-item"]').nth(1)

      // Click both exercises quickly
      if (await exercise1.isVisible() && await exercise2.isVisible()) {
        await exercise1.click()
        await page.goBack()
        await exercise2.click()

        // Should handle loading states for both
        const loadingStates = page.locator('[data-testid="loading"], [data-testid="skeleton-loader"]')
        await expect(loadingStates.first()).toBeVisible()

        // Should eventually load
        await expect(page.locator('[data-testid="set-input"]')).toBeVisible({ timeout: 10000 })
      }
    })
  })

  test.describe('Progressive Loading', () => {
    test('should load content progressively', async ({ page }) => {
      // Monitor loaded elements
      const loadedElements: string[] = []

      await page.on('response', response => {
        if (response.url().includes('/api/')) {
          loadedElements.push(response.url())
        }
      })

      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Critical content should load first
      await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

      // Secondary content loads after
      const stats = page.locator('[data-testid="workout-stats"]')
      const recommendations = page.locator('[data-testid="recommendations"]')

      // These might load progressively
      if (await stats.isVisible({ timeout: 5000 })) {
        const statsIndex = loadedElements.findIndex(url => url.includes('stats'))
        const exercisesIndex = loadedElements.findIndex(url => url.includes('exercise'))
        
        // Stats should load after exercises (if progressive)
        if (statsIndex !== -1 && exercisesIndex !== -1) {
          expect(exercisesIndex).toBeLessThanOrEqual(statsIndex)
        }
      }
    })

    test('should prioritize above-the-fold content', async ({ page }) => {
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Get viewport height
      const viewportHeight = page.viewportSize()?.height || 667

      // Check which exercises are visible initially
      const exercises = page.locator('[data-testid="exercise-item"]')
      const count = await exercises.count()

      let visibleInViewport = 0
      for (let i = 0; i < count; i++) {
        const box = await exercises.nth(i).boundingBox()
        if (box && box.y < viewportHeight) {
          visibleInViewport++
        }
      }

      // At least the visible exercises should load quickly
      for (let i = 0; i < visibleInViewport; i++) {
        await expect(exercises.nth(i)).not.toHaveClass(/skeleton|loading/, { timeout: 3000 })
      }
    })
  })

  test.describe('Loading State Indicators', () => {
    test('should show appropriate loading text', async ({ page }) => {
      // Slow down network
      await page.route('**/*', async route => {
        await new Promise(resolve => setTimeout(resolve, 1000))
        await route.continue()
      })

      await page.getByRole('button', { name: /start workout|today's workout/i }).click()

      // Check for loading messages
      const loadingMessages = [
        'Loading workout...',
        'Loading exercises...',
        'Fetching data...',
        'Please wait...'
      ]

      const loadingText = page.locator('[data-testid="loading-text"], .loading-text')
      if (await loadingText.isVisible({ timeout: 2000 })) {
        const text = await loadingText.textContent()
        const hasAppropriateMessage = loadingMessages.some(msg => 
          text?.toLowerCase().includes(msg.toLowerCase())
        )
        expect(hasAppropriateMessage).toBeTruthy()
      }
    })

    test('should use loading spinners appropriately', async ({ page }) => {
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Navigate to exercise
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Slow down save request
      await page.route('**/SaveWorkoutLog**', async route => {
        await new Promise(resolve => setTimeout(resolve, 2000))
        await route.continue()
      })

      // Fill and save
      await page.locator('input[name="weight"]').fill('100')
      await page.locator('input[name="reps"]').fill('10')
      await page.getByRole('button', { name: /save|done/i }).click()

      // Should show spinner on button
      const saveButton = page.getByRole('button', { name: /save|done|saving/i })
      const spinner = saveButton.locator('[data-testid="spinner"], .spinner, svg[role="status"]')
      await expect(spinner).toBeVisible()

      // Button should be disabled
      await expect(saveButton).toBeDisabled()

      // Should complete eventually
      await expect(spinner).not.toBeVisible({ timeout: 10000 })
    })
  })

  test.describe('Optimistic Updates', () => {
    test('should show optimistic updates immediately', async ({ page }) => {
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Slow down save request
      await page.route('**/SaveWorkoutLog**', async route => {
        await new Promise(resolve => setTimeout(resolve, 3000))
        await route.continue()
      })

      // Fill and save
      await page.locator('input[name="weight"]').fill('100')
      await page.locator('input[name="reps"]').fill('10')
      
      // Capture state before save
      const beforeSave = Date.now()
      
      await page.getByRole('button', { name: /save|done/i }).click()

      // Should show success state quickly (optimistic)
      const successIndicator = page.locator('[data-testid="save-success"], [data-testid="set-saved"]')
      const afterOptimistic = Date.now()
      
      if (await successIndicator.isVisible({ timeout: 500 })) {
        const optimisticTime = afterOptimistic - beforeSave
        expect(optimisticTime).toBeLessThan(1000) // Should be nearly instant
      }

      // Should sync in background
      const syncIndicator = page.locator('[data-testid="syncing"], [data-testid="saving"]')
      await expect(syncIndicator).toBeVisible()
      await expect(syncIndicator).not.toBeVisible({ timeout: 10000 })
    })

    test('should rollback on failure', async ({ page }) => {
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Make save fail
      await page.route('**/SaveWorkoutLog**', route => {
        route.fulfill({ status: 500 })
      })

      // Fill and save
      await page.locator('input[name="weight"]').fill('100')
      await page.locator('input[name="reps"]').fill('10')
      await page.getByRole('button', { name: /save|done/i }).click()

      // Might show optimistic update briefly
      await page.waitForTimeout(500)

      // Should show error after failure
      const errorMessage = page.locator('[role="alert"]')
      await expect(errorMessage).toBeVisible()

      // Data should remain in form (rollback)
      await expect(page.locator('input[name="weight"]')).toHaveValue('100')
      await expect(page.locator('input[name="reps"]')).toHaveValue('10')
    })
  })

  test.describe('Loading Performance', () => {
    test('should not block user interactions during loading', async ({ page }) => {
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Slow down API calls
      await page.route('**/api/**', async route => {
        await new Promise(resolve => setTimeout(resolve, 2000))
        await route.continue()
      })

      // Trigger a background load
      const refreshButton = page.locator('[data-testid="refresh-button"]')
      if (await refreshButton.isVisible()) {
        await refreshButton.click()
      }

      // User should still be able to interact
      const firstExercise = page.locator('[data-testid="exercise-item"]').first()
      await expect(firstExercise).toBeEnabled()
      
      // Should be able to navigate
      await firstExercise.click()
      await expect(page.locator('[data-testid="set-input"]')).toBeVisible()
    })

    test('should cancel unnecessary requests', async ({ page }) => {
      const requests: string[] = []
      
      page.on('request', request => {
        if (request.url().includes('/api/')) {
          requests.push(request.url())
        }
      })

      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Quickly navigate between exercises
      const exercises = page.locator('[data-testid="exercise-item"]')
      const count = await exercises.count()

      if (count >= 3) {
        // Click multiple exercises quickly
        await exercises.nth(0).click()
        await page.waitForTimeout(100)
        await page.goBack()
        
        await exercises.nth(1).click()
        await page.waitForTimeout(100)
        await page.goBack()
        
        await exercises.nth(2).click()

        // Should not make redundant requests
        const uniqueRequests = new Set(requests)
        expect(uniqueRequests.size).toBeLessThanOrEqual(requests.length * 0.8)
      }
    })
  })

  test.describe('Mobile Loading States', () => {
    test.use({
      viewport: { width: 375, height: 667 },
      hasTouch: true,
      isMobile: true,
    })

    test('should show pull-to-refresh loading', async ({ page }) => {
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Simulate pull-to-refresh
      await page.locator('body').dispatchEvent('touchstart', {
        touches: [{ clientX: 200, clientY: 100 }],
      })
      
      await page.locator('body').dispatchEvent('touchmove', {
        touches: [{ clientX: 200, clientY: 300 }],
      })
      
      await page.locator('body').dispatchEvent('touchend')

      // Should show refresh indicator
      const refreshIndicator = page.locator('[data-testid="pull-to-refresh"], [data-testid="refreshing"]')
      await expect(refreshIndicator).toBeVisible({ timeout: 2000 })

      // Should complete refresh
      await expect(refreshIndicator).not.toBeVisible({ timeout: 10000 })
    })

    test('should optimize loading for mobile bandwidth', async ({ page }) => {
      // Monitor image requests
      const imageRequests: string[] = []
      
      page.on('request', request => {
        if (request.resourceType() === 'image') {
          imageRequests.push(request.url())
        }
      })

      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Check if images are optimized for mobile
      imageRequests.forEach(url => {
        // Should request mobile-optimized images
        const hasMobileParams = url.includes('w=') || url.includes('size=') || url.includes('mobile')
        const isIcon = url.includes('icon') || url.includes('.svg')
        
        expect(hasMobileParams || isIcon).toBeTruthy()
      })
    })
  })
})