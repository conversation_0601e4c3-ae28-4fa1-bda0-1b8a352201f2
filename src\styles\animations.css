/**
 * Success screen animations
 * Mobile-first, performant CSS animations using transform and opacity only
 */

/* Checkmark draw animation */
@keyframes checkmark-draw {
  0% {
    stroke-dashoffset: 100;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    stroke-dashoffset: 0;
    opacity: 1;
  }
}

/* Fade in animation */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Scale bounce animation */
@keyframes scale-bounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Slide up animation */
@keyframes slide-up {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Pulse animation for emphasis */
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* Auto-scroll animation for long text (TV news ticker style) */
@keyframes scroll-text {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(calc(-100% - var(--scroll-width, 0px)));
  }
}

/* Enhanced auto-scroll with pauses for better UX */
@keyframes scroll-text-enhanced {
  0% {
    transform: translateX(100%);
  }
  15% {
    transform: translateX(0);
  }
  75% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-100% - var(--scroll-width, 0px)));
  }
}

/* Save button fade animation - 200ms fade out then fade in */
@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fade-in-center {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Animation classes */
.animate-checkmark-draw {
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  animation: checkmark-draw 0.8s ease-out forwards;
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

.animate-scale-bounce {
  animation: scale-bounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.animate-slide-up {
  animation: slide-up 0.5s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-scroll {
  animation: scroll-text 8s linear infinite;
}

.animate-scroll-enhanced {
  animation: scroll-text-enhanced 10s ease-in-out infinite;
}

.animation-play-state-paused {
  animation-play-state: paused;
}

/* Save button fade animation classes */
.fade-out {
  animation: fade-out 0.2s ease-out forwards;
}

.fade-in {
  animation: fade-in-center 0.2s ease-out forwards;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .animate-checkmark-draw {
    stroke-dashoffset: 0;
    opacity: 1;
  }
}

/* Utility classes for animation states */
.animation-idle {
  opacity: 0;
  visibility: hidden;
}

.animation-entering {
  opacity: 0;
  visibility: visible;
}

.animation-active {
  opacity: 1;
  visibility: visible;
}

.animation-exiting {
  opacity: 0;
  visibility: visible;
}

.animation-complete {
  opacity: 1;
  visibility: visible;
}

/* Performance optimizations */
.will-animate {
  will-change: transform, opacity;
}

.animation-complete .will-animate {
  will-change: auto;
}
