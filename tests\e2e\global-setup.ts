/* eslint-disable no-console */
import { chromium, FullConfig } from '@playwright/test'
import { exec } from 'child_process'
import { promisify } from 'util'
import { WebKitBrowserFactory } from './webkit-browser-factory'

const execAsync = promisify(exec)

async function cleanupWebKitProcesses() {
  console.log('🧹 Cleaning up WebKit processes...')

  const commands = [
    'pkill -9 safaridriver || true',
    'pkill -9 WebKitWebContent || true',
    'pkill -9 WebKitNetworkProcess || true',
    'pkill -9 WebKitWebProcess || true',
    'pkill -9 "com.apple.WebKit.WebContent" || true',
    'pkill -9 "Safari" || true',
    'pkill -9 "WebKit" || true',
  ]

  // Execute all commands in parallel for efficiency
  await Promise.all(
    commands.map(async (command) => {
      try {
        await execAsync(command)
      } catch (error) {
        // Ignore errors - processes might not exist
      }
    })
  )

  console.log('✅ WebKit process cleanup completed')
}

async function globalSetup(config: FullConfig) {
  console.log('🔧 Global setup: Starting browser context validation...')

  // Clean up any existing WebKit processes first
  if (process.platform === 'darwin') {
    await cleanupWebKitProcesses()
  }

  // Test Chromium browser context creation with error handling
  console.log('🔧 Testing Chromium browser context...')
  try {
    const chromiumBrowser = await chromium.launch()
    const chromiumContext = await chromiumBrowser.newContext()
    const chromiumPage = await chromiumContext.newPage()

    try {
      await chromiumPage.goto(
        'data:text/html,<html><body>Chromium Test</body></html>'
      )
      console.log('✅ Chromium browser context test passed')
    } catch (error) {
      console.error('❌ Chromium browser context test failed:', error)
      throw error
    } finally {
      await chromiumContext.close()
      await chromiumBrowser.close()
    }
  } catch (error) {
    console.error('❌ Chromium browser not available:', error)
    console.error(
      'This usually means Playwright browsers are not installed correctly.'
    )
    console.error(
      'Make sure "npx playwright install --with-deps webkit chromium" was run successfully.'
    )
    throw new Error(`Chromium browser not available: ${error.message}`)
  }

  // Check WebKit browser installation first
  console.log('🔧 Checking WebKit browser installation...')

  // Determine the correct cache path based on OS
  const cacheBasePath = process.platform === 'darwin'
    ? `${process.env.HOME}/Library/Caches/ms-playwright`
    : `${process.env.HOME}/.cache/ms-playwright`

  console.log(`Looking for WebKit in: ${cacheBasePath}`)

  try {
    // eslint-disable-next-line @typescript-eslint/no-var-requires, global-require
    const fs = require('fs')

    if (fs.existsSync(cacheBasePath)) {
      const entries = fs.readdirSync(cacheBasePath)
      const webkitDirs = entries.filter((entry: string) => entry.startsWith('webkit-'))

      if (webkitDirs.length > 0) {
        console.log(`✅ Found WebKit installation: ${webkitDirs[0]}`)

        // Test WebKit browser context creation with enhanced factory
        console.log('🔧 Testing WebKit browser context with enhanced factory...')

        try {
          const webkitBrowser = await WebKitBrowserFactory.launch()
          const webkitContext =
            await WebKitBrowserFactory.createContext(webkitBrowser)
          const webkitPage = await webkitContext.newPage()

          await webkitPage.goto(
            'data:text/html,<html><body>WebKit Test</body></html>',
            {
              timeout: 30000,
            }
          )

          const content = await webkitPage.textContent('body')
          if (content === 'WebKit Test') {
            console.log('✅ WebKit browser context test passed with enhanced factory')
          }

          await webkitPage.close()
          await webkitContext.close()
          await WebKitBrowserFactory.closeBrowser(webkitBrowser)
        } catch (error) {
          console.warn('⚠️ WebKit browser context test failed:', error)
          console.warn(
            'Tests may be unstable, but will continue with retry logic during test execution.'
          )
        }
      } else {
        console.error('❌ No WebKit browser directories found in cache')
        console.error('Available directories:', entries)
        throw new Error('WebKit browser not installed')
      }
    } else {
      console.error('❌ Playwright cache directory not found:', cacheBasePath)
      throw new Error('Playwright cache directory not found')
    }
  } catch (error) {
    console.error('❌ WebKit installation check failed:', error)
    console.error('This will likely cause WebKit tests to fail')
    // Don't throw here - let tests run and fail individually with better error messages
  }

  // Log resource information
  console.log('📊 System resources at test start:')
  console.log(`- Workers: ${config.workers}`)
  console.log(`- Projects: ${config.projects.length}`)
  console.log(`- Version: ${config.version}`)
  console.log(`- Platform: ${process.platform}`)
  console.log(`- Node version: ${process.version}`)

  const memUsage = process.memoryUsage()
  console.log(
    `- Memory heap used: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`
  )
  console.log(
    `- Memory heap total: ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`
  )
  console.log(`- Memory RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB`)

  return async () => {
    console.log('🧹 Global setup cleanup completed')
  }
}

export default globalSetup
