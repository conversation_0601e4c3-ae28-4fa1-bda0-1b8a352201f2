import { describe, it, expect } from 'vitest'
import { getSetType, getSetTypeTitle } from '../setTypeUtils'
import type { RecommendationModel } from '@/types'

describe('setTypeUtils', () => {
  const baseRecommendation: RecommendationModel = {
    Series: 3,
    Reps: 10,
    Weight: { Lb: 135, Kg: 61.23 },
    OneRMProgress: 0,
    RecommendationInKg: 61.23,
    OneRMPercentage: 75,
    WarmUpReps1: 5,
    WarmUpReps2: 3,
    WarmUpWeightSet1: { Lb: 95, Kg: 43.09 },
    WarmUpWeightSet2: { Lb: 115, Kg: 52.16 },
    WarmUpsList: [],
    WarmupsCount: 2,
    RpRest: 0,
    NbPauses: 0,
    NbRepsPauses: 0,
    IsEasy: false,
    IsMedium: true,
    IsBodyweight: false,
    Increments: { Lb: 2.5, Kg: 1 },
    Max: { Lb: 200, Kg: 90.72 },
    Min: { Lb: 45, Kg: 20.41 },
    IsNormalSets: true,
    IsDeload: false,
    IsBackOffSet: false,
    BackOffSetWeight: { Lb: 0, Kg: 0 },
    IsMaxChallenge: false,
    IsLightSession: false,
    FirstWorkSetReps: 10,
    FirstWorkSetWeight: { Lb: 135, Kg: 61.23 },
    FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
    IsPyramid: false,
    IsReversePyramid: false,
    HistorySet: [],
    ReferenceSetHistory: {} as any,
    MinReps: 8,
    MaxReps: 12,
    isPlateAvailable: true,
    isDumbbellAvailable: false,
    isPulleyAvailable: false,
    isBandsAvailable: false,
    Speed: 1,
    IsManual: false,
    ReferenseReps: 10,
    ReferenseWeight: { Lb: 135, Kg: 61.23 },
    IsDropSet: false,
  }

  describe('getSetType', () => {
    it('should return "Normal" for normal sets', () => {
      expect(getSetType(baseRecommendation)).toBe('Normal')
    })

    it('should return "Rest-pause" for rest-pause sets', () => {
      const restPauseRec = {
        ...baseRecommendation,
        IsNormalSets: false,
        NbPauses: 2,
      }
      expect(getSetType(restPauseRec)).toBe('Rest-pause')
    })

    it('should return "Back-off" for back-off sets', () => {
      const backOffRec = {
        ...baseRecommendation,
        IsBackOffSet: true,
      }
      expect(getSetType(backOffRec)).toBe('Back-off')
    })

    it('should return "Drop set" for drop sets', () => {
      const dropSetRec = {
        ...baseRecommendation,
        IsDropSet: true,
      }
      expect(getSetType(dropSetRec)).toBe('Drop set')
    })

    it('should return "Pyramid" for pyramid sets', () => {
      const pyramidRec = {
        ...baseRecommendation,
        IsPyramid: true,
      }
      expect(getSetType(pyramidRec)).toBe('Pyramid')
    })

    it('should return "Reverse pyramid" for reverse pyramid sets', () => {
      const reversePyramidRec = {
        ...baseRecommendation,
        IsReversePyramid: true,
      }
      expect(getSetType(reversePyramidRec)).toBe('Reverse pyramid')
    })

    it('should prioritize Rest-pause over other types', () => {
      const multiTypeRec = {
        ...baseRecommendation,
        IsNormalSets: false,
        NbPauses: 2,
        IsBackOffSet: true,
        IsDropSet: true,
        IsPyramid: true,
        IsReversePyramid: true,
      }
      expect(getSetType(multiTypeRec)).toBe('Rest-pause')
    })
  })

  describe('getSetTypeTitle', () => {
    it('should return undefined for Normal sets', () => {
      expect(getSetTypeTitle('Normal')).toBeUndefined()
    })

    it('should return the type name for special sets', () => {
      expect(getSetTypeTitle('Rest-pause')).toBe('Rest-pause')
      expect(getSetTypeTitle('Back-off')).toBe('Back-off')
      expect(getSetTypeTitle('Drop set')).toBe('Drop set')
      expect(getSetTypeTitle('Pyramid')).toBe('Pyramid')
      expect(getSetTypeTitle('Reverse pyramid')).toBe('Reverse pyramid')
    })
  })
})
