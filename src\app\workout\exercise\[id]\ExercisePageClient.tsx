'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { SetScreenWithGrid } from '@/components/workout/SetScreenWithGrid'
import { SetScreenLoadingState } from '@/components/workout/SetScreenLoadingState'
import { ExerciseTransitionScreen } from '@/components/workout/ExerciseTransitionScreen'
import {
  ExercisePageErrorBoundary,
  WorkoutErrorBoundary,
} from '@/components/workout/ExercisePageErrorBoundary'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useExercisePageInitialization } from '@/hooks/useExercisePageInitialization'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { debugLog } from '@/utils/debugLog'

interface ExercisePageClientProps {
  exerciseId: number
}

export function ExercisePageClient({ exerciseId }: ExercisePageClientProps) {
  const { isLoadingWorkout, workoutError, exercises, workoutSession } =
    useWorkout()
  const { loadingStates } = useWorkoutStore()
  const searchParams = useSearchParams()
  const [showTransition, setShowTransition] = useState(false)
  const [transitionComplete, setTransitionComplete] = useState(false)

  const { isInitializing, loadingError, retryInitialization } =
    useExercisePageInitialization(exerciseId)

  // Get recommendation status from useSetScreenLogic
  const { recommendation } = useSetScreenLogic(exerciseId)

  // Get exercise name from search params
  const exerciseNameFromParams = searchParams.get('exerciseName')

  // Determine if we should show transition
  useEffect(() => {
    // Show transition if:
    // 1. We have an exercise name from navigation
    // 2. No recommendation is loaded yet
    // 3. We haven't shown the transition already
    if (exerciseNameFromParams && !recommendation && !transitionComplete) {
      setShowTransition(true)
    }
  }, [exerciseNameFromParams, recommendation, transitionComplete])

  debugLog('🏋️ [ExercisePageClient] Component rendered', {
    exerciseId,
    isLoadingWorkout,
    exercisesCount: exercises?.length || 0,
    isInitializing,
    hasLoadingError: !!loadingError,
    hasWorkoutError: !!workoutError,
    hasWorkoutSession: !!workoutSession,
    showTransition,
    transitionComplete,
    hasRecommendation: !!recommendation,
  })

  // Show transition screen if needed
  if (showTransition && !transitionComplete) {
    return (
      <ExerciseTransitionScreen
        exerciseName={exerciseNameFromParams || 'Loading exercise...'}
        onComplete={() => {
          setTransitionComplete(true)
          setShowTransition(false)
        }}
      />
    )
  }

  // Show error state with retry option
  if (loadingError) {
    return (
      <ExercisePageErrorBoundary
        error={loadingError}
        onRetry={retryInitialization}
      />
    )
  }

  // Handle workout errors
  if (workoutError) {
    return <WorkoutErrorBoundary error={workoutError} />
  }

  // Check if we're still loading recommendations for this exercise
  const isLoadingRecommendation = exerciseId
    ? loadingStates.get(exerciseId)
    : false

  debugLog('🎨 [ExercisePageClient] Rendering decision', {
    isInitializing,
    isLoadingWorkout,
    isLoadingRecommendation,
    hasError: !!loadingError || !!workoutError,
  })

  // Find exercise name for loading state
  const exercise = exercises?.find((ex) => ex.Id === exerciseId)
  const exerciseName = exercise?.Label

  // Show loading while initializing
  if (isInitializing || isLoadingWorkout || isLoadingRecommendation) {
    return (
      <SetScreenLoadingState
        exerciseName={exerciseName}
        showDetailedSkeleton
        isLoadingRecommendations={isLoadingRecommendation}
      />
    )
  }

  // Render the SetScreenWithGrid once everything is ready
  return <SetScreenWithGrid exerciseId={exerciseId} />
}
