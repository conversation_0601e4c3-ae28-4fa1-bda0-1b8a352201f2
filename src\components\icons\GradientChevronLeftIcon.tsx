import React from 'react'

interface GradientChevronLeftIconProps {
  className?: string
  onClick?: () => void
  size?: number
}

export function GradientChevronLeftIcon({
  className = '',
  onClick,
  size = 24,
}: GradientChevronLeftIconProps) {
  const gradientId = `chevron-gradient-${Math.random().toString(36).substr(2, 9)}`

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      onClick={onClick}
      aria-hidden="true"
    >
      <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#d4af37" />
          <stop offset="100%" stopColor="#f7e98e" />
        </linearGradient>
      </defs>
      <path
        d="M15 18l-6-6 6-6"
        stroke={`url(#${gradientId})`}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
