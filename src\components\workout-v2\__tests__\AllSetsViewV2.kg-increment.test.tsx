import { render, screen, fireEvent } from '@testing-library/react'
import { AllSetsViewV2 } from '../AllSetsViewV2'
import type { ExtendedWorkoutLogSerieModel } from '../AllSetsViewV2'

describe('AllSetsViewV2 - kg increment fix', () => {
  const mockOnSetDataChange = vi.fn()
  const mockOnComplete = vi.fn()
  const mockOnSkip = vi.fn()

  const defaultProps = {
    allSets: [
      {
        Id: 1,
        IsNext: true,
        IsWarmups: false,
        IsFinished: false,
        Reps: 10,
        Weight: { Lb: 100, Kg: 45 },
      },
    ] as ExtendedWorkoutLogSerieModel[],
    setData: { reps: 10, weight: 45, duration: 0 },
    onSetDataChange: mockOnSetDataChange,
    onComplete: mockOnComplete,
    onSkip: mockOnSkip,
    isSaving: false,
    unit: 'kg' as const,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Weight increment behavior', () => {
    it('should increment weight by 1 when unit is kg', () => {
      // GIVEN: Component rendered with kg unit
      render(<AllSetsViewV2 {...defaultProps} />)

      // WHEN: Click increment weight button
      const incrementButton = screen.getByLabelText('Increment weight')
      fireEvent.click(incrementButton)

      // THEN: Weight should increase by 1
      expect(mockOnSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 46, // 45 + 1
        duration: 0,
      })
    })

    it('should decrement weight by 1 when unit is kg', () => {
      // GIVEN: Component rendered with kg unit
      render(<AllSetsViewV2 {...defaultProps} />)

      // WHEN: Click decrement weight button
      const decrementButton = screen.getByLabelText('Decrement weight')
      fireEvent.click(decrementButton)

      // THEN: Weight should decrease by 1
      expect(mockOnSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 44, // 45 - 1
        duration: 0,
      })
    })

    it('should increment weight by 2.5 when unit is lbs', () => {
      // GIVEN: Component rendered with lbs unit
      const lbsProps = {
        ...defaultProps,
        unit: 'lbs' as const,
        setData: { reps: 10, weight: 100, duration: 0 },
      }
      render(<AllSetsViewV2 {...lbsProps} />)

      // WHEN: Click increment weight button
      const incrementButton = screen.getByLabelText('Increment weight')
      fireEvent.click(incrementButton)

      // THEN: Weight should increase by 2.5
      expect(mockOnSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 102.5, // 100 + 2.5
        duration: 0,
      })
    })

    it('should decrement weight by 2.5 when unit is lbs', () => {
      // GIVEN: Component rendered with lbs unit
      const lbsProps = {
        ...defaultProps,
        unit: 'lbs' as const,
        setData: { reps: 10, weight: 100, duration: 0 },
      }
      render(<AllSetsViewV2 {...lbsProps} />)

      // WHEN: Click decrement weight button
      const decrementButton = screen.getByLabelText('Decrement weight')
      fireEvent.click(decrementButton)

      // THEN: Weight should decrease by 2.5
      expect(mockOnSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 97.5, // 100 - 2.5
        duration: 0,
      })
    })

    it('should not allow weight to go below 0', () => {
      // GIVEN: Component with weight near 0
      const lowWeightProps = {
        ...defaultProps,
        setData: { reps: 10, weight: 0.5, duration: 0 },
      }
      render(<AllSetsViewV2 {...lowWeightProps} />)

      // WHEN: Click decrement weight button
      const decrementButton = screen.getByLabelText('Decrement weight')
      fireEvent.click(decrementButton)

      // THEN: Weight should be 0, not negative
      expect(mockOnSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 0, // Max(0, 0.5 - 1) = 0
        duration: 0,
      })
    })
  })

  describe('Reps increment behavior', () => {
    it('should increment reps by 1', () => {
      // GIVEN: Component rendered
      render(<AllSetsViewV2 {...defaultProps} />)

      // WHEN: Click increment reps button
      const incrementButton = screen.getByLabelText('Increment reps')
      fireEvent.click(incrementButton)

      // THEN: Reps should increase by 1
      expect(mockOnSetDataChange).toHaveBeenCalledWith({
        reps: 11, // 10 + 1
        weight: 45,
        duration: 0,
      })
    })

    it('should not allow reps to go below 0', () => {
      // GIVEN: Component with 0 reps
      const zeroRepsProps = {
        ...defaultProps,
        setData: { reps: 0, weight: 45, duration: 0 },
      }
      render(<AllSetsViewV2 {...zeroRepsProps} />)

      // WHEN: Click decrement reps button
      const decrementButton = screen.getByLabelText('Decrement reps')
      fireEvent.click(decrementButton)

      // THEN: Reps should stay at 0
      expect(mockOnSetDataChange).toHaveBeenCalledWith({
        reps: 0, // Max(0, 0 - 1) = 0
        weight: 45,
        duration: 0,
      })
    })
  })
})
