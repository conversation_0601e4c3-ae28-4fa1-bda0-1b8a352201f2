import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { CurrentSetCard } from '../CurrentSetCard'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  useAnimation: () => ({
    start: vi.fn(),
  }),
}))

const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsTimeBased: false,
  IsFinished: false,
}

const mockCurrentSet: WorkoutLogSerieModel = {
  Id: 1,
  Reps: 10,
  Weight: { Kg: 80, Lb: 175 },
  IsWarmups: false,
  IsNext: true,
  IsFinished: false,
}

const defaultProps = {
  exercise: mockExercise,
  currentSet: mockCurrentSet,
  setData: { reps: 10, weight: 80, duration: 0 },
  onSetDataChange: vi.fn(),
  onComplete: vi.fn(),
  onSkip: vi.fn(),
  isSaving: false,
  completedSets: 1,
  totalSets: 3,
  unit: 'kg' as const,
}

describe('CurrentSetCard - UI Layout Improvements', () => {
  describe('Label Positioning', () => {
    it('should display REPS label below the reps number', () => {
      // Given: CurrentSetCard is rendered
      render(<CurrentSetCard {...defaultProps} />)

      // When: User views the reps section
      const repsValue = screen.getByText('10')
      const repsLabel = screen.getByText('REPS')

      // Then: REPS label should appear after (below) the number in DOM order
      expect(repsValue.compareDocumentPosition(repsLabel)).toBe(
        Node.DOCUMENT_POSITION_FOLLOWING
      )
    })

    it('should display weight unit label below the weight number', () => {
      // Given: CurrentSetCard is rendered with kg unit
      render(<CurrentSetCard {...defaultProps} />)

      // When: User views the weight section
      const weightValue = screen.getByText('80')
      const unitLabel = screen.getByText('KG')

      // Then: Unit label should appear after (below) the number in DOM order
      expect(weightValue.compareDocumentPosition(unitLabel)).toBe(
        Node.DOCUMENT_POSITION_FOLLOWING
      )
    })

    it('should display LBS label when unit is lbs', () => {
      // Given: CurrentSetCard is rendered with lbs unit
      render(
        <CurrentSetCard
          {...defaultProps}
          unit="lbs"
          setData={{ reps: 10, weight: 175, duration: 0 }}
        />
      )

      // When: User views the weight section
      const weightValue = screen.getByText('175')
      const unitLabel = screen.getByText('LBS')

      // Then: LBS label should be displayed and below the number
      expect(weightValue.compareDocumentPosition(unitLabel)).toBe(
        Node.DOCUMENT_POSITION_FOLLOWING
      )
    })
  })

  describe('Asterisk Separator', () => {
    it('should display * symbol between reps and weight sections', () => {
      // Given: CurrentSetCard is rendered
      render(<CurrentSetCard {...defaultProps} />)

      // When: User views the input controls
      const asterisk = screen.getByText('*')

      // Then: Asterisk should be visible
      expect(asterisk).toBeInTheDocument()
      expect(asterisk).toHaveClass('text-4xl') // Should be prominent
    })

    it('should center the asterisk between sections', () => {
      // Given: CurrentSetCard is rendered
      render(<CurrentSetCard {...defaultProps} />)

      // When: Looking at the flex structure
      const asteriskElement = screen.getByText('*')

      // Then: Asterisk should be in a flex container with centering
      const asteriskParent = asteriskElement.parentElement
      expect(asteriskParent).toHaveClass(
        'flex',
        'items-center',
        'justify-center'
      )
    })
  })

  describe('Progress Bar Display', () => {
    it('should display only ONE progress bar at the top', () => {
      // Given: CurrentSetCard is rendered
      const { container } = render(<CurrentSetCard {...defaultProps} />)

      // When: User views the component
      const progressBars = container.querySelectorAll(
        '.h-2.bg-surface-secondary.rounded-full'
      )

      // Then: Should have only ONE progress bar (at the top)
      expect(progressBars).toHaveLength(1)
    })

    it('should display "Set X of Y" format instead of exercise name', () => {
      // Given: CurrentSetCard is rendered with 1 completed out of 3 total sets
      render(
        <CurrentSetCard {...defaultProps} completedSets={1} totalSets={3} />
      )

      // When: User views the progress bar text
      const setCounterText = screen.getByText('Set 2 of 3')

      // Then: Should show "Set X of Y" format
      expect(setCounterText).toBeInTheDocument()

      // And: Should NOT display exercise name in progress bar
      expect(screen.queryByText('Bench Press')).not.toBeInTheDocument()
    })

    it('should display gold gradient on progress bar', () => {
      // Given: CurrentSetCard is rendered
      const { container } = render(<CurrentSetCard {...defaultProps} />)

      // When: User views the progress bar fill
      const progressFill = container.querySelector('.h-full')

      // Then: Progress bar should use gold gradient classes
      expect(progressFill).toHaveClass(
        'bg-gradient-to-r',
        'from-brand-gold-start',
        'to-brand-gold-end'
      )
    })

    it('should include warmup sets in total count', () => {
      // Given: CurrentSetCard with warmup sets included
      render(
        <CurrentSetCard {...defaultProps} completedSets={2} totalSets={5} />
      )

      // When: User views the progress bar text
      const setCounterText = screen.getByText('Set 3 of 5')

      // Then: Should show total including warmup sets
      expect(setCounterText).toBeInTheDocument()
    })

    it('should NOT display progress bar below swipe instructions', () => {
      // Given: CurrentSetCard is rendered
      const { container } = render(<CurrentSetCard {...defaultProps} />)

      // When: User views below swipe text
      const swipeText = screen.getByText(
        'Swipe left to skip · right to complete'
      )
      const progressBars = container.querySelectorAll(
        '.h-2.bg-surface-secondary.rounded-full'
      )

      // Then: Should have only one progress bar
      expect(progressBars).toHaveLength(1)

      // And: The progress bar should be BEFORE the swipe text, not after
      const progressBar = progressBars[0]
      expect(progressBar.compareDocumentPosition(swipeText)).toBe(
        Node.DOCUMENT_POSITION_FOLLOWING
      )
    })

    it('should show correct progress in the top progress bar', () => {
      // Given: CurrentSetCard with 1 of 3 sets completed
      const { container } = render(<CurrentSetCard {...defaultProps} />)

      // When: Viewing the progress bar
      const progressBars = container.querySelectorAll(
        '.h-2.bg-surface-secondary.rounded-full'
      )
      const progressBar = progressBars[0]
      const progressFill = progressBar?.querySelector(
        '.h-full.bg-gradient-to-r.from-brand-gold-start.to-brand-gold-end'
      )

      // Then: Progress should show 33.33% (1/3)
      expect(progressFill).toHaveAttribute('animate')
      // Since framer-motion is mocked, we just verify the element exists
      expect(progressFill).toBeInTheDocument()
    })

    it('should show 0% progress when no sets completed', () => {
      // Given: CurrentSetCard with 0 completed sets
      const { container } = render(
        <CurrentSetCard {...defaultProps} completedSets={0} />
      )

      // When: Viewing the progress bar
      const progressBars = container.querySelectorAll(
        '.h-2.bg-surface-secondary.rounded-full'
      )
      const progressBar = progressBars[0]
      const progressFill = progressBar?.querySelector(
        '.h-full.bg-gradient-to-r.from-brand-gold-start.to-brand-gold-end'
      )

      // Then: Progress should show 0%
      expect(progressFill).toHaveAttribute('animate')
      // Since framer-motion is mocked, we just verify the element exists
      expect(progressFill).toBeInTheDocument()
    })

    it('should show 100% progress when all sets completed', () => {
      // Given: CurrentSetCard with all sets completed
      const { container } = render(
        <CurrentSetCard {...defaultProps} completedSets={3} totalSets={3} />
      )

      // When: Viewing the progress bar
      const progressBars = container.querySelectorAll(
        '.h-2.bg-surface-secondary.rounded-full'
      )
      const progressBar = progressBars[0]
      const progressFill = progressBar?.querySelector(
        '.h-full.bg-gradient-to-r.from-brand-gold-start.to-brand-gold-end'
      )

      // Then: Progress should show 100%
      expect(progressFill).toHaveAttribute('animate')
      // Since framer-motion is mocked, we just verify the element exists
      expect(progressFill).toBeInTheDocument()
    })
  })

  describe('Complete UI Layout', () => {
    it('should maintain proper spacing and alignment with all changes', () => {
      // Given: CurrentSetCard is rendered
      const { container } = render(<CurrentSetCard {...defaultProps} />)

      // When: Examining the complete layout
      const repsLabel = screen.getByText('REPS')
      const unitLabel = screen.getByText('KG')
      const asterisk = screen.getByText('*')
      const progressBars = container.querySelectorAll(
        '.h-2.bg-surface-secondary.rounded-full'
      )

      // Then: All new elements should be present
      expect(repsLabel).toBeInTheDocument()
      expect(unitLabel).toBeInTheDocument()
      expect(asterisk).toBeInTheDocument()
      expect(progressBars).toHaveLength(1) // Only top progress bar
    })
  })
})
