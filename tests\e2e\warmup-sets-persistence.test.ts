import { test, expect } from '@playwright/test'
import { login } from './helpers/auth'
import { skipOnboarding } from './helpers/onboarding'

test.describe('Warm-up Sets Persistence', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
    await skipOnboarding(page)
  })

  test('warm-up sets should remain editable after saving', async ({ page }) => {
    // Navigate to workout page
    await page.goto('/workout')

    // Click "Start workout" button
    await page.getByRole('button', { name: /start workout/i }).click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Wait for the sets grid to load
    await page.waitForSelector('[data-testid="set-cell-W"]', { timeout: 10000 })

    // Find the first warm-up set inputs
    const warmupRepsInput = page
      .locator('input[aria-label="Reps for set W"]')
      .first()
    const warmupWeightInput = page
      .locator('input[aria-label="Weight for set W"]')
      .first()

    // Verify warm-up set inputs are initially editable
    await expect(warmupRepsInput).toBeEnabled()
    await expect(warmupWeightInput).toBeEnabled()

    // Enter values for the warm-up set
    await warmupRepsInput.fill('5')
    await warmupWeightInput.fill('95')

    // Save the warm-up set
    await page.getByRole('button', { name: /save set/i }).click()

    // Wait for save to complete (wait for next set to become active or completion message)
    await page.waitForTimeout(1000)

    // Navigate back to the first warm-up set (if needed)
    // The warm-up set should still be visible in the grid

    // Verify that the saved warm-up set inputs are STILL editable
    const savedWarmupRepsInput = page
      .locator('input[aria-label="Reps for set W"]')
      .first()
    const savedWarmupWeightInput = page
      .locator('input[aria-label="Weight for set W"]')
      .first()

    await expect(savedWarmupRepsInput).toBeEnabled()
    await expect(savedWarmupWeightInput).toBeEnabled()

    // Verify values were saved
    await expect(savedWarmupRepsInput).toHaveValue('5')
    await expect(savedWarmupWeightInput).toHaveValue('95')

    // Test that we can edit the saved warm-up set
    await savedWarmupRepsInput.fill('6')
    await savedWarmupWeightInput.fill('100')

    // Verify new values are in the inputs
    await expect(savedWarmupRepsInput).toHaveValue('6')
    await expect(savedWarmupWeightInput).toHaveValue('100')
  })

  test('all finished sets (warm-up and work) should remain editable', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')

    // Start workout
    await page.getByRole('button', { name: /start workout/i }).click()

    // Wait for exercise page
    await page.waitForURL(/\/workout\/exercise\/\d+/)
    await page.waitForSelector('[data-testid="set-cell-W"]', { timeout: 10000 })

    // Save a warm-up set
    const firstWarmupReps = page
      .locator('input[aria-label="Reps for set W"]')
      .first()
    const firstWarmupWeight = page
      .locator('input[aria-label="Weight for set W"]')
      .first()

    await firstWarmupReps.fill('5')
    await firstWarmupWeight.fill('95')
    await page.getByRole('button', { name: /save set/i }).click()

    // Wait for next set to become active
    await page.waitForTimeout(1000)

    // If there's another warm-up set, save it too
    const secondWarmupReps = page
      .locator('input[aria-label="Reps for set W"]')
      .nth(1)
    if (await secondWarmupReps.isVisible()) {
      await secondWarmupReps.fill('3')
      const secondWarmupWeight = page
        .locator('input[aria-label="Weight for set W"]')
        .nth(1)
      await secondWarmupWeight.fill('115')
      await page.getByRole('button', { name: /save set/i }).click()
      await page.waitForTimeout(1000)
    }

    // Now we should be on a work set - save it
    const workSetReps = page
      .locator('input[aria-label="Reps for set 1"]')
      .first()
    const workSetWeight = page
      .locator('input[aria-label="Weight for set 1"]')
      .first()

    if (await workSetReps.isVisible()) {
      await workSetReps.fill('10')
      await workSetWeight.fill('135')
      await page.getByRole('button', { name: /save set/i }).click()

      // If RIR picker appears, select a value
      const rirOption = page.getByRole('button', { name: /2-3 RIR/i })
      if (await rirOption.isVisible({ timeout: 2000 })) {
        await rirOption.click()
      }
    }

    // Wait for all saves to complete
    await page.waitForTimeout(2000)

    // Now verify ALL saved sets remain editable
    const allRepsInputs = page.locator('input[aria-label*="Reps for set"]')
    const allWeightInputs = page.locator('input[aria-label*="Weight for set"]')

    const repsCount = await allRepsInputs.count()
    const weightCount = await allWeightInputs.count()

    // Check each input is enabled
    const repsPromises = []
    for (let i = 0; i < repsCount; i++) {
      repsPromises.push(expect(allRepsInputs.nth(i)).toBeEnabled())
    }
    await Promise.all(repsPromises)

    const weightPromises = []
    for (let i = 0; i < weightCount; i++) {
      const input = allWeightInputs.nth(i)
      // Skip bodyweight exercise weight inputs which are always disabled
      weightPromises.push(
        input
          .evaluate((el) => el.closest('[data-bodyweight="true"]') !== null)
          .then((isBodyweight) => {
            if (!isBodyweight) {
              return expect(input).toBeEnabled()
            }
          })
      )
    }
    await Promise.all(weightPromises)
  })
})
