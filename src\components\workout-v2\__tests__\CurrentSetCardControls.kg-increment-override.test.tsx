import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { CurrentSetCardControls } from '../CurrentSetCardControls'
import type { RecommendationModel } from '@/types'

describe('CurrentSetCardControls - kg increment override', () => {
  describe('Force kg increments to 1 regardless of API response', () => {
    it('should always increment by 1 for kg even when API returns 2', () => {
      // GIVEN: API returns wrong increment value of 2 for kg
      const onSetDataChange = vi.fn()
      const recommendation: Partial<RecommendationModel> = {
        Increments: { Kg: 2, Lb: 2.5 }, // API returns 2kg increment (wrong)
      }

      render(
        <CurrentSetCardControls
          setData={{ reps: 10, weight: 50, duration: 0 }}
          onSetDataChange={onSetDataChange}
          unit="kg"
          isSaving={false}
          animationClass=""
          recommendation={recommendation as RecommendationModel}
        />
      )

      // WHEN: User clicks increment button
      const incrementButton = screen.getByLabelText('Increase weight')
      fireEvent.click(incrementButton)

      // THEN: Should increment by 1 (not 2)
      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 51, // 50 + 1, not 50 + 2
        duration: 0,
      })
    })

    it('should always decrement by 1 for kg even when API returns 2', () => {
      // GIVEN: API returns wrong increment value of 2 for kg
      const onSetDataChange = vi.fn()
      const recommendation: Partial<RecommendationModel> = {
        Increments: { Kg: 2, Lb: 2.5 },
      }

      render(
        <CurrentSetCardControls
          setData={{ reps: 10, weight: 50, duration: 0 }}
          onSetDataChange={onSetDataChange}
          unit="kg"
          isSaving={false}
          animationClass=""
          recommendation={recommendation as RecommendationModel}
        />
      )

      // WHEN: User clicks decrement button
      const decrementButton = screen.getByLabelText('Decrease weight')
      fireEvent.click(decrementButton)

      // THEN: Should decrement by 1 (not 2)
      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 49, // 50 - 1, not 50 - 2
        duration: 0,
      })
    })

    it('should not affect lbs increments', () => {
      // GIVEN: API returns increments
      const onSetDataChange = vi.fn()
      const recommendation: Partial<RecommendationModel> = {
        Increments: { Kg: 2, Lb: 5 }, // API values
      }

      render(
        <CurrentSetCardControls
          setData={{ reps: 10, weight: 100, duration: 0 }}
          onSetDataChange={onSetDataChange}
          unit="lbs"
          isSaving={false}
          animationClass=""
          recommendation={recommendation as RecommendationModel}
        />
      )

      // WHEN: User clicks increment button
      const incrementButton = screen.getByLabelText('Increase weight')
      fireEvent.click(incrementButton)

      // THEN: Should use API value for lbs
      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 105, // 100 + 5 (API value is respected for lbs)
        duration: 0,
      })
    })

    it('should handle API returning 0 or fractional kg increments', () => {
      // Test case 1: API returns 0
      const onSetDataChange = vi.fn()
      const recommendation1: Partial<RecommendationModel> = {
        Increments: { Kg: 0, Lb: 2.5 },
      }

      const { rerender } = render(
        <CurrentSetCardControls
          setData={{ reps: 10, weight: 50, duration: 0 }}
          onSetDataChange={onSetDataChange}
          unit="kg"
          isSaving={false}
          animationClass=""
          recommendation={recommendation1 as RecommendationModel}
        />
      )

      fireEvent.click(screen.getByLabelText('Increase weight'))
      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 51, // Should still use 1, not 0
        duration: 0,
      })

      // Test case 2: API returns fractional value
      onSetDataChange.mockClear()
      const recommendation2: Partial<RecommendationModel> = {
        Increments: { Kg: 2.5, Lb: 5 },
      }

      rerender(
        <CurrentSetCardControls
          setData={{ reps: 10, weight: 50, duration: 0 }}
          onSetDataChange={onSetDataChange}
          unit="kg"
          isSaving={false}
          animationClass=""
          recommendation={recommendation2 as RecommendationModel}
        />
      )

      fireEvent.click(screen.getByLabelText('Increase weight'))
      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 51, // Should use 1, not 2.5
        duration: 0,
      })
    })

    it('should always use 1kg increment for consistency', () => {
      const onSetDataChange = vi.fn()
      const recommendation: Partial<RecommendationModel> = {
        Increments: { Kg: 2, Lb: 2.5 }, // API wants 2kg, we force to 1kg
      }

      render(
        <CurrentSetCardControls
          setData={{ reps: 10, weight: 50, duration: 0 }}
          onSetDataChange={onSetDataChange}
          unit="kg"
          isSaving={false}
          animationClass=""
          recommendation={recommendation as RecommendationModel}
        />
      )

      fireEvent.click(screen.getByLabelText('Increase weight'))

      // Should use 1kg increment for consistency regardless of API
      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 51, // 50 + 1, not 50 + 2
        duration: 0,
      })
    })
  })
})
