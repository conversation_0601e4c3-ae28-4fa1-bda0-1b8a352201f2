import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useExerciseV2Actions } from '../useExerciseV2Actions'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Mock dependencies
const mockSaveSet = vi.fn()
const mockNextSet = vi.fn()
const mockStartRestTimer = vi.fn()
const mockNavigateToExercise = vi.fn()
const mockPush = vi.fn()

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    saveSet: mockSaveSet,
  }),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    nextSet: mockNextSet,
    exercises: [
      { Id: 1, Label: 'Bench Press', IsFinished: false },
      { Id: 2, Label: 'Squats', IsFinished: false },
      { Id: 3, Label: 'Deadlifts', IsFinished: false },
    ],
  }),
}))

vi.mock('@/components/workout-v2/RestTimer', () => ({
  useRestTimer: () => ({
    startRestTimer: mockStartRestTimer,
  }),
}))

vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => ({
    navigateToExercise: mockNavigateToExercise,
  }),
}))

const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsTimeBased: false,
  IsFinished: false,
}

const mockCurrentSet: WorkoutLogSerieModel = {
  Id: 1,
  Reps: 10,
  Weight: { Kg: 80, Lb: 175 },
  IsWarmups: false,
  IsNext: true,
  IsFinished: false,
}

const defaultProps = {
  currentExercise: mockExercise,
  workoutSession: { id: '123' },
  setData: { reps: 10, weight: 80, duration: 0 },
  currentSetIndex: 5,
  allSets: Array(6).fill(mockCurrentSet),
  isWarmup: false,
  isLastSet: true,
  isFirstWorkSet: false,
  currentSet: mockCurrentSet,
  setSaveError: vi.fn(),
}

describe('useExerciseV2Actions - Last Set Flow', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should NOT call handleSaveSet on last set to avoid old flow', async () => {
    // Given: Hook is initialized with last set
    const { result } = renderHook(() => useExerciseV2Actions(defaultProps))

    // When: Completing the last set
    await act(async () => {
      await result.current.handleCompleteSet()
    })

    // Then: Should save the set directly without triggering old flow

    // But should save the set data
    expect(mockSaveSet).toHaveBeenCalledWith({
      exerciseId: 1,
      reps: 10,
      weight: 80,
      isWarmup: false,
      setNumber: 6,
      duration: undefined,
      RIR: undefined,
    })
  })

  it('should navigate to next exercise in V2 flow after last set', async () => {
    // Given: Hook with navigation capability for last set of first exercise
    const propsWithNavigation = {
      ...defaultProps,
      isLastExercise: false,
      exercises: [
        { Id: 1, Label: 'Bench Press', IsFinished: false },
        { Id: 2, Label: 'Squats', IsFinished: false },
      ],
    }
    const { result } = renderHook(() =>
      useExerciseV2Actions(propsWithNavigation)
    )

    // When: Completing the last set
    await act(async () => {
      await result.current.handleCompleteSet()
    })

    // Then: Should navigate to next exercise in V2 flow
    expect(mockPush).toHaveBeenCalledWith('/workout/exercise-v2/2')
  })

  it('should show exercise complete view if last set of last exercise', async () => {
    // Given: Last set of last exercise
    const propsLastExercise = {
      ...defaultProps,
      isLastExercise: true,
      onExerciseComplete: vi.fn(),
    }
    const { result } = renderHook(() => useExerciseV2Actions(propsLastExercise))

    // When: Completing the last set
    await act(async () => {
      await result.current.handleCompleteSet()
    })

    // Then: Should trigger exercise complete callback
    expect(propsLastExercise.onExerciseComplete).toHaveBeenCalled()
    // Should NOT navigate away
    expect(mockPush).not.toHaveBeenCalled()
  })

  it('should NOT start rest timer after last set', async () => {
    // Given: Hook is initialized with last set
    const { result } = renderHook(() => useExerciseV2Actions(defaultProps))

    // When: Completing the last set
    await act(async () => {
      await result.current.handleCompleteSet()
    })

    // Then: Should NOT start rest timer
    expect(mockStartRestTimer).not.toHaveBeenCalled()
  })
})
