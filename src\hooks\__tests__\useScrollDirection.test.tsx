import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useScrollDirection } from '../useScrollDirection'

describe('useScrollDirection', () => {
  let originalScrollY: number

  beforeEach(() => {
    // Save original scrollY
    originalScrollY = window.scrollY

    // Mock window.scrollY
    Object.defineProperty(window, 'scrollY', {
      writable: true,
      configurable: true,
      value: 0,
    })
  })

  afterEach(() => {
    // Restore original scrollY
    Object.defineProperty(window, 'scrollY', {
      writable: true,
      configurable: true,
      value: originalScrollY,
    })
  })

  it('should return visible true when at top of page', () => {
    // Given: Page is at top
    window.scrollY = 0

    // When: Hook is rendered
    const { result } = renderHook(() => useScrollDirection())

    // Then: Should be visible
    expect(result.current.isVisible).toBe(true)
    expect(result.current.scrollDirection).toBe('up')
  })

  it('should hide navigation when scrolling down past threshold', async () => {
    // Given: Hook is rendered
    const { result } = renderHook(() => useScrollDirection())

    // When: Scroll down past threshold
    act(() => {
      window.scrollY = 100
      window.dispatchEvent(new Event('scroll'))
    })

    // Wait for debounce
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 20))
    })

    // Then: Should be hidden
    expect(result.current.isVisible).toBe(false)
    expect(result.current.scrollDirection).toBe('down')
  })

  it('should show navigation when scrolling up', () => {
    // Given: Navigation is hidden after scrolling down
    const { result } = renderHook(() => useScrollDirection())

    act(() => {
      window.scrollY = 100
      window.dispatchEvent(new Event('scroll'))
    })

    // When: Scroll up
    act(() => {
      window.scrollY = 80
      window.dispatchEvent(new Event('scroll'))
    })

    // Then: Should be visible
    expect(result.current.isVisible).toBe(true)
    expect(result.current.scrollDirection).toBe('up')
  })

  it('should always show navigation when near top of page', () => {
    // Given: Navigation was hidden
    const { result } = renderHook(() => useScrollDirection())

    act(() => {
      window.scrollY = 100
      window.dispatchEvent(new Event('scroll'))
    })

    // When: Scroll to near top (within threshold)
    act(() => {
      window.scrollY = 30
      window.dispatchEvent(new Event('scroll'))
    })

    // Then: Should be visible regardless of scroll direction
    expect(result.current.isVisible).toBe(true)
  })

  it('should debounce rapid scroll events', () => {
    vi.useFakeTimers()

    // Given: Hook is rendered
    const { result } = renderHook(() => useScrollDirection())

    // When: Multiple rapid scroll events
    act(() => {
      for (let i = 0; i < 10; i++) {
        window.scrollY = 50 + i * 10
        window.dispatchEvent(new Event('scroll'))
      }
    })

    // Then: Should process events with debouncing
    act(() => {
      vi.advanceTimersByTime(10)
    })

    expect(result.current.scrollDirection).toBe('down')

    vi.useRealTimers()
  })

  it('should handle scroll to same position without changing state', () => {
    // Given: Hook is rendered and scrolled down
    const { result } = renderHook(() => useScrollDirection())

    act(() => {
      window.scrollY = 100
      window.dispatchEvent(new Event('scroll'))
    })

    const initialState = { ...result.current }

    // When: Scroll event with same position
    act(() => {
      window.dispatchEvent(new Event('scroll'))
    })

    // Then: State should not change
    expect(result.current).toEqual(initialState)
  })

  it('should clean up event listeners on unmount', () => {
    // Given: Mock removeEventListener
    const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener')

    // When: Hook is mounted and unmounted
    const { unmount } = renderHook(() => useScrollDirection())
    unmount()

    // Then: Should remove scroll listener
    expect(removeEventListenerSpy).toHaveBeenCalledWith(
      'scroll',
      expect.any(Function)
    )
  })

  it('should handle custom scroll threshold', async () => {
    // Given: Hook with custom threshold
    const { result } = renderHook(() => useScrollDirection({ threshold: 100 }))

    // When: Scroll just below threshold
    act(() => {
      window.scrollY = 80
      window.dispatchEvent(new Event('scroll'))
    })

    // Then: Should still be visible (below threshold)
    expect(result.current.isVisible).toBe(true)

    // When: Scroll past custom threshold
    act(() => {
      window.scrollY = 120
      window.dispatchEvent(new Event('scroll'))
    })

    // Wait for debounce
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 20))
    })

    // Then: Navigation can hide
    expect(result.current.isVisible).toBe(false)
  })

  it('should handle touch events for mobile scrolling', async () => {
    // Given: Hook is rendered
    const { result } = renderHook(() => useScrollDirection())

    // Simulate touch scroll down
    act(() => {
      window.scrollY = 0
      const touchStart = new TouchEvent('touchstart', {
        touches: [{ pageY: 100 } as Touch],
      })
      window.dispatchEvent(touchStart)

      window.scrollY = 100
      const touchEnd = new TouchEvent('touchend', {
        changedTouches: [{ pageY: 0 } as Touch],
      })
      window.dispatchEvent(touchEnd)
      window.dispatchEvent(new Event('scroll'))
    })

    // Wait for debounce
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 20))
    })

    // Then: Should detect scroll down
    expect(result.current.scrollDirection).toBe('down')
    expect(result.current.isVisible).toBe(false)
  })
})
