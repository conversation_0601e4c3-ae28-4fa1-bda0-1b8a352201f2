'use client'

import { motion } from 'framer-motion'
import { CheckCircle, ArrowRight } from 'lucide-react'
import type { ExerciseModel } from '@/types'
import { vibrate } from '@/utils/haptics'

// Simple confetti component
function Confetti() {
  // Use a stable array to avoid recreating on each render
  const confettiPieces = Array.from({ length: 20 }, (_, i) => i)

  return (
    <div className="fixed inset-0 pointer-events-none">
      {confettiPieces.map((piece) => (
        <motion.div
          key={`confetti-particle-${piece}`}
          className="absolute w-2 h-2 bg-brand-primary rounded-full"
          initial={{
            x: '50vw',
            y: '50vh',
            scale: 0,
          }}
          animate={{
            x: `${50 + (Math.random() - 0.5) * 80}vw`,
            y: `${50 + (Math.random() - 0.5) * 80}vh`,
            scale: [0, 1, 1, 0],
          }}
          transition={{
            duration: 1.5,
            delay: piece * 0.05,
            ease: 'easeOut',
          }}
        />
      ))}
    </div>
  )
}

interface ExerciseCompleteViewV2Props {
  exercise: ExerciseModel | null
  isLastExercise: boolean
  onContinue: () => void
}

export function ExerciseCompleteViewV2({
  exercise,
  isLastExercise,
  onContinue,
}: ExerciseCompleteViewV2Props) {
  const handleContinue = () => {
    vibrate('success')
    onContinue()
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen px-4 bg-surface-primary">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="text-center max-w-sm"
      >
        {/* Success icon */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
          className="mb-6"
        >
          <CheckCircle className="w-24 h-24 text-brand-primary mx-auto" />
        </motion.div>

        {/* Exercise name */}
        <h2 className="text-2xl font-bold text-text-primary mb-2">
          {exercise?.Label || 'Exercise'} Complete!
        </h2>

        {/* Motivational message */}
        <p className="text-text-secondary mb-8">
          {isLastExercise
            ? "Amazing work! You've crushed your workout!"
            : 'Great job! Ready for the next one?'}
        </p>

        {/* Continue button */}
        <motion.button
          onClick={handleContinue}
          className="w-full py-4 px-6 bg-brand-primary text-text-inverse 
                     rounded-full font-medium text-lg flex items-center 
                     justify-center gap-2 hover:bg-brand-secondary 
                     transition-colors"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {isLastExercise ? 'Finish Workout' : 'Next Exercise'}
          <ArrowRight className="w-5 h-5" />
        </motion.button>

        {/* Skip to summary (if not last) */}
        {!isLastExercise && (
          <button
            onClick={() => {
              vibrate('light')
              // Skip to workout complete
              onContinue()
            }}
            className="mt-4 text-text-secondary text-sm underline"
          >
            Skip remaining exercises
          </button>
        )}
      </motion.div>

      {/* Confetti animation on mount */}
      <Confetti />
    </div>
  )
}
