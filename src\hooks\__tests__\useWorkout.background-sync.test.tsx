import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, act, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useWorkout } from '../useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import { workoutApi } from '@/api/workouts'
import type {
  GetUserProgramInfoResponseModel,
  WorkoutTemplateModel,
  WorkoutTemplateGroupModel,
  RecommendationModel,
} from '@/types'

// Mock the API module
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserProgramInfo: vi.fn(),
    getUserWorkout: vi.fn(),
    getExerciseRecommendation: vi.fn(),
    saveWorkoutSet: vi.fn(),
    completeWorkout: vi.fn(),
  },
}))

// Mock data
const mockUserProgramInfo: GetUserProgramInfoResponseModel = {
  GetUserProgramInfoResponseModel: {
    UserId: 'test-user',
    WeeklyStatus: 'Week 1',
    ProgramLabel: 'Test Program',
    NbDaysInTheWeek: 5,
    NbNonTrainingDays: 2,
    MondayIsFirst: false,
    TimeLogged: '2024-01-01T10:00:00',
    NextWorkoutDayText: 'Today',
    IsInIntroWorkout: false,
    IsInFirstWeek: true,
    TodaysWorkoutId: '1234',
    TodaysWorkoutText: 'Push Day',
    RecommendedProgram: {
      Id: 1,
      Label: 'Beginner Program',
      RemainingToLevelUp: 10,
      IconUrl: 'https://example.com/icon.png',
    },
    NextWorkoutTemplate: {
      Id: 1,
      Label: 'Push Day',
      IsSystemExercise: false,
      Exercises: [
        {
          Id: 1,
          Label: 'Bench Press',
          Path: 'chest/benchpress',
          TargetWeight: { Mass: 100, MassUnit: 'lbs' },
          TargetReps: 8,
          IsWarmup: false,
          HasPastLogs: true,
        },
      ],
    },
    NextNonTrainingDay: '2024-01-03',
    MondayHere: '2024-01-01',
    NextIntensityTechnique: 'Standard',
    ServerTimeUtc: '2024-01-01T10:00:00Z',
    MaxWorkoutSets: 20,
    NbMediumSets: 5,
    NbChallenges: 3,
    WorkoutTemplates: [],
  },
}

const mockWorkout: WorkoutTemplateModel = {
  Id: 1,
  UserId: 'test-user',
  Label: 'Push Day',
  Exercises: [
    {
      Id: 1,
      Label: 'Bench Press',
      Path: 'chest/benchpress',
      TargetWeight: { Mass: 100, MassUnit: 'lbs' },
      TargetReps: 8,
      IsWarmup: false,
      HasPastLogs: true,
    },
  ],
  IsSystemExercise: false,
  WorkoutSettingsModel: {
    Id: 1,
    Pause: 120,
    Equipment: '',
    ChildWorkoutTemplateId: null,
    SetsModel: null,
    WorkoutProgramId: 1,
    IsFirstSet: false,
    IsFail: false,
    NbRepsMinimalInc: null,
    AvgDuration: null,
    IsNotRealData: false,
  },
}

const mockWorkoutGroup: WorkoutTemplateGroupModel = {
  Id: 1,
  Label: 'Beginner Program',
  WorkoutTemplates: [mockWorkout],
  IsFeaturedProgram: false,
  UserId: '',
  IsSystemExercise: true,
  RequiredWorkoutToLevelUp: 10,
  ProgramId: 1,
}

const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 10,
  Weight: { Lb: 100, Kg: 45.36 },
  Increments: { Lb: 2.5, Kg: 1 },
  LastLogDate: '2024-01-01',
  LastReps: 8,
  LastWeight: { Lb: 95, Kg: 43.09 },
  LastSeries: 3,
  IsWaitingForSwap: false,
  RM: 105,
  RIR: 2,
  History: [],
  IsBodyweight: false,
  IsTimeBased: false,
  IsUnilateral: false,
  IsPlate: false,
  RecommendedCountdown: null,
  NegativeWeight: null,
  PartialWeight: null,
  IsNegative: false,
  IsPartial: false,
}

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: Infinity,
      },
    },
  })

  return function ({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }
}

describe('useWorkout - Background Synchronization', () => {
  beforeEach(() => {
    // Reset all stores
    useAuthStore.setState({ isAuthenticated: true })
    useWorkoutStore.setState({
      cachedData: {
        userProgramInfo: null,
        userWorkouts: null,
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: 0,
          userWorkouts: 0,
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
      hasHydrated: false,
      currentWorkout: null,
      exercises: [],
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      workoutSession: null,
      isLoading: false,
      error: null,
    })

    // Clear all mocks
    vi.clearAllMocks()

    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true,
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Background Sync Strategy', () => {
    it('should fetch fresh data when cache is stale', async () => {
      // Given: Stale cached data (25 hours old)
      const staleTimestamp = Date.now() - 25 * 60 * 60 * 1000
      const staleData = {
        ...mockUserProgramInfo.GetUserProgramInfoResponseModel,
        ProgramLabel: 'Stale Program',
      }

      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: staleData,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: staleTimestamp,
            userWorkouts: staleTimestamp,
            todaysWorkout: staleTimestamp,
            exerciseRecommendations: {},
          },
        },
      })

      // Mock fresh API data
      const freshProgramInfo = {
        ...mockUserProgramInfo,
        GetUserProgramInfoResponseModel: {
          ...mockUserProgramInfo.GetUserProgramInfoResponseModel,
          ProgramLabel: 'Fresh Program',
        },
      }

      vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(
        freshProgramInfo
      )
      vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Should have stale cached data immediately
      expect(result.current.todaysWorkout).toBeDefined()

      // Then: Background API call should be started
      await waitFor(() => {
        expect(workoutApi.getUserProgramInfo).toHaveBeenCalled()
      })

      // And: Cache should be updated with fresh data
      await waitFor(() => {
        const state = useWorkoutStore.getState()
        expect(state.cachedData.userProgramInfo?.ProgramLabel).toBe(
          'Fresh Program'
        )
      })
    })

    it('should not fetch when cache is fresh', () => {
      // Given: Fresh cached data (1 hour old)
      const freshTimestamp = Date.now() - 60 * 60 * 1000

      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: freshTimestamp,
            userWorkouts: freshTimestamp,
            todaysWorkout: freshTimestamp,
            exerciseRecommendations: {},
          },
        },
      })

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Should have cached data
      expect(result.current.todaysWorkout).toBeDefined()

      // Then: No API call made
      expect(workoutApi.getUserProgramInfo).not.toHaveBeenCalled()
      expect(workoutApi.getUserWorkout).not.toHaveBeenCalled()
    })

    it('should deduplicate concurrent requests', async () => {
      // Given: Stale cache
      const staleTimestamp = Date.now() - 25 * 60 * 60 * 1000

      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: staleTimestamp,
            userWorkouts: staleTimestamp,
            todaysWorkout: staleTimestamp,
            exerciseRecommendations: {},
          },
        },
      })

      vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(
        mockUserProgramInfo
      )
      vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])

      // When: Multiple instances of the hook are rendered with shared QueryClient
      const wrapper = createWrapper()
      const { result: result1 } = renderHook(() => useWorkout(), { wrapper })
      const { result: result2 } = renderHook(() => useWorkout(), { wrapper })

      // Then: API should only be called once (deduplication by React Query)
      await waitFor(() => {
        expect(workoutApi.getUserProgramInfo).toHaveBeenCalledTimes(1)
        expect(workoutApi.getUserWorkout).toHaveBeenCalledTimes(1)
      })

      // Both hooks should have the same data
      expect(result1.current.todaysWorkout).toEqual(
        result2.current.todaysWorkout
      )
    })

    it('should handle background sync failure gracefully', async () => {
      // Given: Stale cached data
      const staleTimestamp = Date.now() - 25 * 60 * 60 * 1000
      const cachedData = mockUserProgramInfo.GetUserProgramInfoResponseModel

      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: cachedData,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: staleTimestamp,
            userWorkouts: staleTimestamp,
            todaysWorkout: staleTimestamp,
            exerciseRecommendations: {},
          },
        },
      })

      // Mock API failure
      vi.mocked(workoutApi.getUserProgramInfo).mockRejectedValue(
        new Error('Network error')
      )
      vi.mocked(workoutApi.getUserWorkout).mockRejectedValue(
        new Error('Network error')
      )

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Should still have cached data despite API failure
      expect(result.current.todaysWorkout).toBeDefined()

      // Wait for background sync to fail
      await waitFor(() => {
        expect(workoutApi.getUserProgramInfo).toHaveBeenCalled()
      })

      // Cache should remain unchanged (stale data still available)
      const state = useWorkoutStore.getState()
      expect(state.cachedData.userProgramInfo).toEqual(cachedData)
      expect(result.current.todaysWorkout).toBeDefined()
    })

    it('should update cache atomically without disrupting UI', async () => {
      // Given: Stale cached data with user in the middle of workout
      const staleTimestamp = Date.now() - 25 * 60 * 60 * 1000

      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: staleTimestamp,
            userWorkouts: staleTimestamp,
            todaysWorkout: staleTimestamp,
            exerciseRecommendations: {},
          },
        },
        currentWorkout: mockWorkout,
        exercises: mockWorkout.Exercises,
        currentExerciseIndex: 0,
        currentSetIndex: 2, // User is on set 3
      })

      // Mock fresh API data with different exercise order
      const freshWorkout = {
        ...mockWorkout,
        Exercises: [
          {
            Id: 2,
            Label: 'Squat', // Different exercise
            Path: 'legs/squat',
            TargetWeight: { Mass: 200, MassUnit: 'lbs' },
            TargetReps: 5,
            IsWarmup: false,
            HasPastLogs: true,
          },
          ...mockWorkout.Exercises,
        ],
      }

      vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(
        mockUserProgramInfo
      )
      vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([freshWorkout])

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Store initial exercise index
      const initialExerciseIndex = result.current.currentExerciseIndex
      const initialSetIndex = result.current.currentSetIndex

      // Wait for background sync
      await waitFor(() => {
        expect(workoutApi.getUserWorkout).toHaveBeenCalled()
      })

      // User's position should not be disrupted
      expect(result.current.currentExerciseIndex).toBe(initialExerciseIndex)
      expect(result.current.currentSetIndex).toBe(initialSetIndex)
    })

    it('should respect refetch settings and not refetch on mount/focus', () => {
      // Given: Fresh cached data
      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: Date.now(),
            userWorkouts: Date.now(),
            todaysWorkout: Date.now(),
            exerciseRecommendations: {},
          },
        },
      })

      // Render hook
      renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Simulate window focus
      act(() => {
        window.dispatchEvent(new Event('focus'))
      })

      // Should not trigger API calls
      expect(workoutApi.getUserProgramInfo).not.toHaveBeenCalled()
      expect(workoutApi.getUserWorkout).not.toHaveBeenCalled()
    })
  })

  describe('Exercise Recommendation Background Sync', () => {
    it('should fetch fresh recommendation when cache is stale', async () => {
      // Given: Stale recommendation (2 hours old)
      const staleTimestamp = Date.now() - 2 * 60 * 60 * 1000
      const staleRecommendation = {
        ...mockRecommendation,
        Reps: 8, // Old recommendation
      }

      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {
            1: staleRecommendation,
          },
          lastUpdated: {
            userProgramInfo: Date.now(),
            userWorkouts: Date.now(),
            todaysWorkout: Date.now(),
            exerciseRecommendations: {
              1: staleTimestamp,
            },
          },
        },
        currentWorkout: mockWorkout,
        exercises: mockWorkout.Exercises,
        currentExerciseIndex: 0,
      })

      // Mock fresh recommendation
      const freshRecommendation = {
        ...mockRecommendation,
        Reps: 12, // Updated recommendation
      }

      vi.mocked(workoutApi.getExerciseRecommendation).mockResolvedValue(
        freshRecommendation
      )
      vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(
        mockUserProgramInfo
      )
      vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Should have stale recommendation immediately
      expect(result.current.recommendation?.Reps).toBe(8)

      // Background fetch should be triggered
      await waitFor(() => {
        expect(workoutApi.getExerciseRecommendation).toHaveBeenCalledWith(1)
      })

      // Cache should be updated
      await waitFor(() => {
        const state = useWorkoutStore.getState()
        expect(state.cachedData.exerciseRecommendations[1]?.Reps).toBe(12)
      })
    })

    it('should handle offline mode for recommendations', async () => {
      // Given: Offline mode with stale recommendation
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: false,
      })

      const staleTimestamp = Date.now() - 2 * 60 * 60 * 1000

      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {
            1: mockRecommendation,
          },
          lastUpdated: {
            userProgramInfo: Date.now(),
            userWorkouts: Date.now(),
            todaysWorkout: Date.now(),
            exerciseRecommendations: {
              1: staleTimestamp,
            },
          },
        },
        currentWorkout: mockWorkout,
        exercises: mockWorkout.Exercises,
        currentExerciseIndex: 0,
      })

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Should have stale recommendation
      expect(result.current.recommendation).toEqual(mockRecommendation)

      // Should not attempt to fetch when offline
      expect(workoutApi.getExerciseRecommendation).not.toHaveBeenCalled()

      // Call getRecommendation directly
      const recommendation = await result.current.getRecommendation(1)

      // Should return cached data even if stale
      expect(recommendation).toEqual(mockRecommendation)
      expect(workoutApi.getExerciseRecommendation).not.toHaveBeenCalled()
    })
  })
})
