import type { ExerciseModel, RecommendationModel } from '@/types'
import type { WorkoutLogSerieModelRef } from './createWorkoutSets'
import { formatWeight, roundToNearestIncrement } from './workoutSetHelpers'

/**
 * Generates warm-up sets
 */
export function generateWarmupSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel
): WorkoutLogSerieModelRef[] {
  const warmupSets: WorkoutLogSerieModelRef[] = []

  if (recommendation.WarmUpsList && recommendation.WarmUpsList.length > 0) {
    for (let i = 0; i < recommendation.WarmUpsList.length; i++) {
      const warmup = recommendation.WarmUpsList[i]
      if (!warmup) {
        // Skip if warmup is undefined
      } else {
        warmupSets.push({
          ExerciseId: exercise.Id,
          Weight: warmup.WarmUpWeightSet || { Kg: 0, Lb: 0 },
          IsWarmups: true,
          Reps: warmup.WarmUpReps,
          SetNo: 'W',
          IsLastWarmupSet: i === recommendation.WarmUpsList.length - 1,
          IsHeaderCell: i === 0,
          HeaderImage: '',
          HeaderTitle: '',
          ExerciseName: exercise.Label,
          EquipmentId: exercise.EquipmentId,
          SetTitle: (() => {
            if (i === 0) return "Let's warm up:"
            if (i === recommendation.WarmUpsList.length - 1)
              return 'Last warm-up set:'
            return ''
          })(),
          IsFinished: false,
          IsNext: i === 0,
          LastTimeSet: '',
          IsTimeBased: exercise.IsTimeBased,
          IsBodyweight: exercise.IsBodyweight,
          IsNormalset: exercise.IsNormalSets,
          BackColor: 'transparent',
          Increments: recommendation.Increments,
          Min: recommendation.Min,
          Max: recommendation.Max,
          BodypartId: exercise.BodyPartId,
          IsUnilateral: exercise.IsUnilateral,
          IsFlexibility: exercise.IsFlexibility,
        })
      }
    }
  }

  return warmupSets
}

/**
 * Creates base work set with common properties
 */
export function createBaseWorkSet(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setNumber: number,
  existingSetsCount: number,
  isKg: boolean
): WorkoutLogSerieModelRef {
  return {
    ExerciseName: exercise.Label,
    ExerciseId: exercise.Id,
    IsWarmups: false,
    SetNo: `${setNumber + 1}`,
    IsHeaderCell: existingSetsCount === 0 && setNumber === 0,
    IsFirstWorkSet: setNumber === 0,
    IsFinished: false,
    IsNext: false, // Will be calculated based on finished sets
    Weight: recommendation.Weight,
    Reps: recommendation.Reps,
    SetTitle: '',
    LastTimeSet:
      setNumber === 0 &&
      recommendation.FirstWorkSetReps &&
      recommendation.FirstWorkSetWeight
        ? `Last time: ${recommendation.FirstWorkSetReps} x ${formatWeight(
            recommendation.FirstWorkSetWeight,
            isKg
          )}`
        : '',
    IsLastSet: setNumber === recommendation.Series - 1,
  }
}

/**
 * Generates pyramid sets (weight increases, reps decrease)
 */
export function generatePyramidSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  existingSetsCount: number,
  isKg: boolean
): WorkoutLogSerieModelRef[] {
  const pyramidSets: WorkoutLogSerieModelRef[] = []

  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(
      exercise,
      recommendation,
      j,
      existingSetsCount,
      isKg
    )

    if (j === 0) {
      rec.SetTitle = recommendation.Series > 1 ? 'Pyramid set:' : ''
      pyramidSets.push(rec)
    } else {
      const lastSet = pyramidSets[pyramidSets.length - 1]
      if (lastSet?.Weight) {
        const weightIncrease = lastSet.Weight.Kg * 0.1 // 10% increase
        const newWeight = lastSet.Weight.Kg + weightIncrease

        rec.Weight = {
          Kg: roundToNearestIncrement(
            newWeight,
            recommendation.Increments?.Kg || 1
          ),
          Lb: roundToNearestIncrement(
            newWeight * 2.20462,
            recommendation.Increments?.Lb || 5
          ),
        }
        rec.Reps = Math.max(1, (lastSet?.Reps || 0) - 2) // 2 reps less
        rec.SetTitle = ''
        pyramidSets.push(rec)
      }
    }
  }

  return pyramidSets
}

/**
 * Generates reverse pyramid sets (weight decreases, reps increase)
 */
export function generateReversePyramidSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  existingSetsCount: number,
  isKg: boolean
): WorkoutLogSerieModelRef[] {
  const reversePyramidSets: WorkoutLogSerieModelRef[] = []

  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(
      exercise,
      recommendation,
      j,
      existingSetsCount,
      isKg
    )

    if (j === 0) {
      rec.SetTitle = recommendation.Series > 1 ? 'Reverse pyramid:' : ''
      reversePyramidSets.push(rec)
    } else {
      const lastSet = reversePyramidSets[reversePyramidSets.length - 1]
      if (lastSet?.Weight) {
        const weightDecrease = lastSet.Weight.Kg * 0.1 // 10% decrease
        const newWeight = lastSet.Weight.Kg - weightDecrease

        rec.Weight = {
          Kg: roundToNearestIncrement(
            newWeight,
            recommendation.Increments?.Kg || 1
          ),
          Lb: roundToNearestIncrement(
            newWeight * 2.20462,
            recommendation.Increments?.Lb || 5
          ),
        }
        rec.Reps = (lastSet?.Reps || 0) + 2 // 2 reps more
        rec.SetTitle = ''
        reversePyramidSets.push(rec)
      }
    }
  }

  return reversePyramidSets
}

// Functions moved to workoutSetGenerators2 to avoid circular dependency
