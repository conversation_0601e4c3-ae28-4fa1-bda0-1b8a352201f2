#!/usr/bin/env node

/**
 * Production Test Statistics Script
 * Analyzes and reports on production test results
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

const log = {
  error: (msg) => console.error(`${colors.red}❌ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.warn(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️ ${msg}${colors.reset}`),
  header: (msg) => console.log(`${colors.cyan}📊 ${msg}${colors.reset}`)
};

function findTestResults() {
  const possiblePaths = [
    'playwright-report/results.json',
    'test-results/results.json',
    'playwright-report/index.html',
    'test-results'
  ];

  const foundPaths = [];
  
  for (const testPath of possiblePaths) {
    if (fs.existsSync(testPath)) {
      foundPaths.push(testPath);
    }
  }

  return foundPaths;
}

function parsePlaywrightResults(resultsPath) {
  try {
    if (resultsPath.endsWith('.json')) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      return {
        total: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0
      };
    } else if (fs.statSync(resultsPath).isDirectory()) {
      // Count test result files
      const files = fs.readdirSync(resultsPath);
      const testFiles = files.filter(f => f.endsWith('.json') || f.endsWith('.xml'));
      return {
        total: testFiles.length,
        resultFiles: testFiles.length
      };
    }
  } catch (error) {
    log.warning(`Could not parse results from ${resultsPath}: ${error.message}`);
    return null;
  }
}

function generateReport(testResults, resultPaths) {
  log.header('Production Test Statistics Report');
  console.log('='.repeat(50));
  
  if (resultPaths.length === 0) {
    log.warning('No test result files found');
    log.info('Expected locations:');
    console.log('  - playwright-report/results.json');
    console.log('  - test-results/results.json');
    console.log('  - playwright-report/index.html');
    console.log('  - test-results/ directory');
    return;
  }

  log.info(`Found test results in ${resultPaths.length} location(s):`);
  resultPaths.forEach(path => console.log(`  - ${path}`));
  console.log();

  if (testResults) {
    log.header('Test Execution Summary');
    
    if (testResults.total !== undefined) {
      console.log(`Total Tests: ${testResults.total}`);
      console.log(`Passed: ${colors.green}${testResults.passed}${colors.reset}`);
      console.log(`Failed: ${colors.red}${testResults.failed}${colors.reset}`);
      console.log(`Skipped: ${colors.yellow}${testResults.skipped}${colors.reset}`);
      
      if (testResults.duration) {
        const durationMin = Math.round(testResults.duration / 60000);
        console.log(`Duration: ${durationMin} minutes`);
      }
      
      const passRate = testResults.total > 0 ? 
        ((testResults.passed / testResults.total) * 100).toFixed(1) : 0;
      console.log(`Pass Rate: ${passRate}%`);
      
      console.log();
      
      if (testResults.failed > 0) {
        log.error(`${testResults.failed} test(s) failed`);
      } else {
        log.success('All tests passed!');
      }
    } else if (testResults.resultFiles !== undefined) {
      console.log(`Test result files found: ${testResults.resultFiles}`);
    }
  }

  // Check for specific report files
  if (fs.existsSync('playwright-report/index.html')) {
    log.info('HTML report available at: playwright-report/index.html');
  }
  
  if (fs.existsSync('coverage/lcov-report/index.html')) {
    log.info('Coverage report available at: coverage/lcov-report/index.html');
  }

  console.log('='.repeat(50));
}

function main() {
  const resultPaths = findTestResults();
  let testResults = null;

  // Try to parse the first JSON results file found
  for (const path of resultPaths) {
    if (path.endsWith('.json')) {
      testResults = parsePlaywrightResults(path);
      if (testResults) break;
    }
  }

  // If no JSON results, try directory-based analysis
  if (!testResults && resultPaths.length > 0) {
    const dirPath = resultPaths.find(p => fs.statSync(p).isDirectory());
    if (dirPath) {
      testResults = parsePlaywrightResults(dirPath);
    }
  }

  generateReport(testResults, resultPaths);

  // Exit with error code if tests failed
  if (testResults && testResults.failed > 0) {
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { findTestResults, parsePlaywrightResults, generateReport };
