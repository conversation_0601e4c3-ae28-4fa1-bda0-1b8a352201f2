import { renderHook, act } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useWorkoutActions } from '../WorkoutOverviewActions'
import type { WorkoutTemplateGroupModel } from '@/types'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

// Mock debugLog
vi.mock('@/utils/debugLog', () => ({
  debugLog: {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}))

// Mock exercise validation
vi.mock('@/utils/exerciseValidation', () => ({
  isValidExerciseId: vi.fn((id: number) => id > 0 && Number.isInteger(id)),
}))

describe('WorkoutOverviewActions Navigation - Standard vs V2', () => {
  const mockPush = vi.fn()
  const mockRouter = { push: mockPush }
  const mockStartWorkout = vi.fn()
  const mockLoadExerciseRecommendation = vi.fn()
  const mockUpdateExerciseWorkSets = vi.fn()
  const mockFinishWorkout = vi.fn()

  const mockTodaysWorkout: WorkoutTemplateGroupModel[] = [
    {
      Id: 1,
      Name: 'Test Workout',
      CreatedOn: '2024-01-01',
      WorkoutTemplates: [
        {
          Id: 1,
          Label: 'Test Workout',
          Exercises: [
            {
              Id: 123,
              Label: 'Bench Press',
              BodyPartId: 1,
              IsBodyweight: false,
            },
          ],
        },
      ],
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue(mockRouter)
    mockStartWorkout.mockResolvedValue({
      success: true,
      firstExerciseId: 123,
    })
  })

  it('should navigate to standard exercise page when clicking exercise', async () => {
    const { result } = renderHook(() =>
      useWorkoutActions({
        todaysWorkout: mockTodaysWorkout,
        startWorkout: mockStartWorkout,
        exercises: [],
        workoutSession: null,
        loadExerciseRecommendation: mockLoadExerciseRecommendation,
        updateExerciseWorkSets: mockUpdateExerciseWorkSets,
        finishWorkout: mockFinishWorkout,
      })
    )

    await act(async () => {
      await result.current.handleExerciseClick(123)
    })

    // Should navigate to standard exercise page, NOT V2
    expect(mockPush).toHaveBeenCalledWith('/workout/exercise/123')
  })

  it('should navigate to standard exercise page when starting workout', async () => {
    const { result } = renderHook(() =>
      useWorkoutActions({
        todaysWorkout: mockTodaysWorkout,
        startWorkout: mockStartWorkout,
        exercises: [],
        workoutSession: null,
        loadExerciseRecommendation: mockLoadExerciseRecommendation,
        updateExerciseWorkSets: mockUpdateExerciseWorkSets,
        finishWorkout: mockFinishWorkout,
      })
    )

    await act(async () => {
      await result.current.handleStartWorkout()
    })

    // Should navigate to standard exercise page, NOT V2
    expect(mockPush).toHaveBeenCalledWith('/workout/exercise/123')
  })

  it('should handle V2 navigation when explicitly requested', async () => {
    // This test documents future behavior when V2 notification is implemented
    renderHook(() =>
      useWorkoutActions({
        todaysWorkout: mockTodaysWorkout,
        startWorkout: mockStartWorkout,
        exercises: [],
        workoutSession: null,
        loadExerciseRecommendation: mockLoadExerciseRecommendation,
        updateExerciseWorkSets: mockUpdateExerciseWorkSets,
        finishWorkout: mockFinishWorkout,
      })
    )

    // Future: When V2 notification handler is implemented
    // await act(async () => {
    //   await result.current.handleV2Navigation(123)
    // })
    // expect(mockPush).toHaveBeenCalledWith('/workout/exercise-v2/123')
  })
})
