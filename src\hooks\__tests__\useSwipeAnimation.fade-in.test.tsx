import { renderHook, act } from '@testing-library/react'
import { useSwipeAnimation } from '../useSwipeAnimation'
import * as framerMotion from 'framer-motion'
import { vi } from 'vitest'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  useAnimation: vi.fn(),
}))

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

describe('useSwipeAnimation fade-in behavior', () => {
  let mockControls: {
    start: ReturnType<typeof vi.fn>
  }
  let onComplete: ReturnType<typeof vi.fn>
  let onSkip: ReturnType<typeof vi.fn>

  beforeEach(() => {
    mockControls = {
      start: vi.fn().mockResolvedValue(undefined),
    }
    onComplete = vi.fn()
    onSkip = vi.fn()

    vi.mocked(framerMotion.useAnimation).mockReturnValue(mockControls as any)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('right swipe (complete) fade-in animation', () => {
    it('should fade in from center after right swipe, not slide from left', async () => {
      const { result } = renderHook(() =>
        useSwipeAnimation({ onComplete, onSkip })
      )

      // Simulate right swipe
      const mockPanInfo = {
        offset: { x: 150, y: 0 }, // Above threshold (100)
        velocity: { x: 0, y: 0 },
        point: { x: 0, y: 0 },
      }

      await act(async () => {
        await result.current.handleDragEnd({} as any, mockPanInfo)
      })

      // Verify animation sequence
      expect(mockControls.start).toHaveBeenCalledTimes(3)

      // First: exit right
      expect(mockControls.start).toHaveBeenNthCalledWith(1, {
        x: 300,
        opacity: 0,
      })

      // Second: reset to center with opacity 0 (fade preparation)
      expect(mockControls.start).toHaveBeenNthCalledWith(2, {
        x: 0,
        opacity: 0,
      })

      // Third: fade in at center
      expect(mockControls.start).toHaveBeenNthCalledWith(3, {
        x: 0,
        opacity: 1,
        transition: { duration: 0.2 },
      })

      expect(onComplete).toHaveBeenCalled()
    })
  })

  describe('left swipe (skip) fade-in animation', () => {
    it('should fade in from center after left swipe, not slide from right', async () => {
      const { result } = renderHook(() =>
        useSwipeAnimation({ onComplete, onSkip })
      )

      // Simulate left swipe
      const mockPanInfo = {
        offset: { x: -150, y: 0 }, // Below threshold (-100)
        velocity: { x: 0, y: 0 },
        point: { x: 0, y: 0 },
      }

      await act(async () => {
        await result.current.handleDragEnd({} as any, mockPanInfo)
      })

      // Verify animation sequence
      expect(mockControls.start).toHaveBeenCalledTimes(3)

      // First: exit left
      expect(mockControls.start).toHaveBeenNthCalledWith(1, {
        x: -300,
        opacity: 0,
      })

      // Second: reset to center with opacity 0 (fade preparation)
      expect(mockControls.start).toHaveBeenNthCalledWith(2, {
        x: 0,
        opacity: 0,
      })

      // Third: fade in at center
      expect(mockControls.start).toHaveBeenNthCalledWith(3, {
        x: 0,
        opacity: 1,
        transition: { duration: 0.2 },
      })

      expect(onSkip).toHaveBeenCalled()
    })
  })

  describe('no swipe (snap back)', () => {
    it('should just snap back to center when swipe threshold not met', async () => {
      const { result } = renderHook(() =>
        useSwipeAnimation({ onComplete, onSkip })
      )

      // Simulate small swipe (below threshold)
      const mockPanInfo = {
        offset: { x: 50, y: 0 }, // Below threshold (100)
        velocity: { x: 0, y: 0 },
        point: { x: 0, y: 0 },
      }

      await act(async () => {
        await result.current.handleDragEnd({} as any, mockPanInfo)
      })

      // Should only snap back, no complex animation
      expect(mockControls.start).toHaveBeenCalledTimes(1)
      expect(mockControls.start).toHaveBeenCalledWith({ x: 0 })

      expect(onComplete).not.toHaveBeenCalled()
      expect(onSkip).not.toHaveBeenCalled()
    })
  })
})
