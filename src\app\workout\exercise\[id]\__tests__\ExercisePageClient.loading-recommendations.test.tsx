import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { ExercisePageClient } from '../ExercisePageClient'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import type { ExerciseWorkSetsModel } from '@/types'

// Mock dependencies
vi.mock('next/navigation')
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/components/workout/SetScreenLoadingState', () => ({
  SetScreenLoadingState: ({
    exerciseName,
    showDetailedSkeleton,
    isLoadingRecommendations,
  }: any) => (
    <div data-testid="loading-state">
      {exerciseName && <div data-testid="exercise-name">{exerciseName}</div>}
      {showDetailedSkeleton && (
        <div data-testid="skeleton">Skeleton Loader</div>
      )}
      {isLoadingRecommendations && (
        <div data-testid="recommendations-message">
          Preparing your recommendations...
        </div>
      )}
    </div>
  ),
}))
vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: () => ({
    isInitializing: false,
    loadingError: null,
    retryInitialization: vi.fn(),
  }),
}))

describe('ExercisePageClient - Recommendations Loading', () => {
  const mockRouter = {
    replace: vi.fn(),
    push: vi.fn(),
  }

  const mockExercises: ExerciseWorkSetsModel[] = [
    {
      Id: 1001,
      Label: 'Bench Press',
      IsBodyweight: false,
      IsEasy: false,
      IsSystemExercise: true,
      IsSwappable: true,
      IsTimeBased: false,
      IsUnilateral: false,
      VideoUrl: '',
      EquipmentModel: { Id: 1, Label: 'Barbell' },
      LastTimeModifiedBy: new Date(),
      Minutes: 2,
      Reps: 8,
      Sets: 3,
      SetsModel: {
        Series: '3',
        Reps: '8',
        SetStyle: 'Normal',
      },
      BodyPartId: 1,
      SelectedEquipmentModelId: 1,
      UnilateralOption: 0,
      SelectedUnilateralOption: 0,
      WorkSets: [],
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
  })

  it('should pass exercise name and show detailed skeleton when loading recommendations', () => {
    // Mock that workout is loaded but recommendations are loading
    vi.mocked(useWorkout).mockReturnValue({
      isLoadingWorkout: false,
      workoutError: null,
      exercises: mockExercises,
      workoutSession: { id: 'test-session' },
    } as any)

    // Mock that recommendations are loading for this exercise
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadingStates: new Map([[1001, true]]), // Loading recommendations
    } as any)

    render(<ExercisePageClient exerciseId={1001} />)

    // Should render loading state with exercise name and skeleton
    expect(screen.getByTestId('loading-state')).toBeInTheDocument()
    expect(screen.getByTestId('exercise-name')).toHaveTextContent('Bench Press')
    expect(screen.getByTestId('skeleton')).toBeInTheDocument()
    expect(screen.getByTestId('recommendations-message')).toBeInTheDocument()
  })

  it('should show loading state even when exercise is not found in exercises array', () => {
    // Mock that workout is loaded but exercise not in array
    vi.mocked(useWorkout).mockReturnValue({
      isLoadingWorkout: false,
      workoutError: null,
      exercises: [], // No exercises
      workoutSession: { id: 'test-session' },
    } as any)

    // Mock that recommendations are loading
    vi.mocked(useWorkoutStore).mockReturnValue({
      loadingStates: new Map([[9999, true]]), // Loading for non-existent exercise
    } as any)

    render(<ExercisePageClient exerciseId={9999} />)

    // Should still show loading state but without exercise name
    expect(screen.getByTestId('loading-state')).toBeInTheDocument()
    expect(screen.queryByTestId('exercise-name')).not.toBeInTheDocument()
    expect(screen.getByTestId('skeleton')).toBeInTheDocument()
    expect(screen.getByTestId('recommendations-message')).toBeInTheDocument()
  })
})
