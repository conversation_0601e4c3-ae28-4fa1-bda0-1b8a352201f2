import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { CurrentSetCard } from '../CurrentSetCard'
import { vi } from 'vitest'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Mock the animation hook
const mockTriggerFadeAnimation = vi.fn()
vi.mock('@/hooks/useSwipeAnimation', () => ({
  useSwipeAnimation: vi.fn(() => ({
    controls: {},
    isDragging: false,
    handleDragEnd: vi.fn(),
    handleDragStart: vi.fn(),
    triggerFadeAnimation: mockTriggerFadeAnimation,
  })),
}))

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

describe('CurrentSetCard save button animation', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    IsFinished: false,
  } as ExerciseModel

  const mockSet: WorkoutLogSerieModel = {
    Id: 1,
    Reps: 10,
    Weight: { Kg: 50, Lb: 110 },
    IsWarmups: false,
    IsNext: true,
    IsFinished: false,
  } as WorkoutLogSerieModel

  const defaultProps = {
    exercise: mockExercise,
    currentSet: mockSet,
    setData: { reps: 10, weight: 110, duration: 0 },
    onSetDataChange: vi.fn(),
    onComplete: vi.fn(),
    onSkip: vi.fn(),
    isSaving: false,
    completedSets: 0,
    totalSets: 3,
    unit: 'lbs' as const,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should trigger fade animation before calling onComplete when save button is clicked', async () => {
    const onComplete = vi.fn()

    render(<CurrentSetCard {...defaultProps} onComplete={onComplete} />)

    const saveButton = screen.getByRole('button', { name: /save set/i })

    // Click save button
    fireEvent.click(saveButton)

    // Should trigger fade animation first
    await waitFor(() => {
      expect(mockTriggerFadeAnimation).toHaveBeenCalledTimes(1)
    })

    // Then call onComplete after animation
    await waitFor(() => {
      expect(onComplete).toHaveBeenCalledTimes(1)
    })

    // Verify order: animation first, then callback
    expect(mockTriggerFadeAnimation).toHaveBeenCalledBefore(onComplete as any)
  })

  it('should not trigger animation if save button is disabled', () => {
    render(<CurrentSetCard {...defaultProps} isSaving />)

    const saveButton = screen.getByRole('button', { name: /save set/i })

    // Button should be disabled
    expect(saveButton).toBeDisabled()

    // Try to click
    fireEvent.click(saveButton)

    // Should not trigger animation
    expect(mockTriggerFadeAnimation).not.toHaveBeenCalled()
    expect(defaultProps.onComplete).not.toHaveBeenCalled()
  })

  it('should handle rapid save button clicks gracefully', async () => {
    const onComplete = vi.fn()

    render(<CurrentSetCard {...defaultProps} onComplete={onComplete} />)

    const saveButton = screen.getByRole('button', { name: /save set/i })

    // Rapid clicks
    fireEvent.click(saveButton)
    fireEvent.click(saveButton)
    fireEvent.click(saveButton)

    // Should only trigger animation once
    await waitFor(() => {
      expect(mockTriggerFadeAnimation).toHaveBeenCalledTimes(1)
    })

    // And only call onComplete once
    await waitFor(() => {
      expect(onComplete).toHaveBeenCalledTimes(1)
    })
  })

  it('should not interfere with swipe animations', async () => {
    const { rerender } = render(<CurrentSetCard {...defaultProps} />)

    // First save via button
    const saveButton = screen.getByRole('button', { name: /save set/i })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(mockTriggerFadeAnimation).toHaveBeenCalledTimes(1)
    })

    // Clear mocks
    vi.clearAllMocks()

    // Update to next set
    const nextSet: WorkoutLogSerieModel = {
      ...mockSet,
      Id: 2,
      Reps: 8,
    }

    rerender(<CurrentSetCard {...defaultProps} currentSet={nextSet} />)

    // Swipe animations should still work independently
    // (tested in existing swipe animation tests)
  })
})
