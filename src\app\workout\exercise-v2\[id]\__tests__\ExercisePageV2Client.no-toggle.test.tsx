import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExercisePageV2Client } from '../ExercisePageV2Client'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
}))

// Mock dependencies with successful data
vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: () => ({
    currentExercise: { Id: 1, Label: 'Bench Press', IsFinished: false },
    exercises: [{ Id: 1, Label: 'Bench Press' }],
    currentSetIndex: 0,
    isSaving: false,
    saveError: null,
    showComplete: false,
    showExerciseComplete: false,
    recommendation: { WeightIncrement: 5, RepsIncrement: 1 },
    isLoading: false,
    error: null,
    isLastExercise: false,
    isLastSet: false,
    isWarmup: false,
    isFirstWorkSet: true,
    completedSets: [],
    setData: { reps: 10, weight: 80, duration: 0 },
    setSetData: vi.fn(),
    setSaveError: vi.fn(),
    handleSaveSet: vi.fn(),
    refetchRecommendation: vi.fn(),
  }),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    isLoadingWorkout: false,
    workoutError: null,
    workoutSession: { id: '123' },
  }),
}))

vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: () => ({
    isInitializing: false,
    loadingError: null,
    retryInitialization: vi.fn(),
  }),
}))

vi.mock('@/hooks/useExerciseV2Actions', () => ({
  useExerciseV2Actions: () => ({
    handleCompleteSet: vi.fn(),
    handleSkipSet: vi.fn(),
  }),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'lbs' }),
  }),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    loadingStates: new Map(),
    restTimerState: { isActive: false },
  }),
}))

vi.mock('@/utils/generateAllSets', () => ({
  generateAllSets: () => [
    {
      Id: 1,
      Reps: 10,
      Weight: { Kg: 80, Lb: 175 },
      IsWarmups: false,
      IsNext: true,
      IsFinished: false,
    },
    {
      Id: 2,
      Reps: 10,
      Weight: { Kg: 82.5, Lb: 180 },
      IsWarmups: false,
      IsNext: false,
      IsFinished: false,
    },
  ],
}))

describe('ExercisePageV2Client - No Toggle Button', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: { queries: { retry: false } },
    })
  })

  const renderComponent = () =>
    render(
      <QueryClientProvider client={queryClient}>
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      </QueryClientProvider>
    )

  it('should NOT render view toggle button', () => {
    renderComponent()

    // Test will FAIL initially - toggle button currently exists
    expect(screen.queryByText('View all sets')).not.toBeInTheDocument()
    expect(screen.queryByText('Minimal view')).not.toBeInTheDocument()
    expect(screen.queryByText('Hybrid view')).not.toBeInTheDocument()
  })

  it('should render only hybrid view layout by default', () => {
    renderComponent()

    // Test will FAIL initially - conditional rendering still exists
    // Should show CurrentSetCard AND NextSetsPreview (hybrid view components)
    expect(screen.getByText('Set 1')).toBeInTheDocument() // From CurrentSetCard
    expect(screen.getByText('Next sets')).toBeInTheDocument() // From NextSetsPreview
  })

  it('should not render AllSetsViewV2 or minimal view components', () => {
    renderComponent()

    // Test will FAIL initially - other views are still accessible
    // AllSetsViewV2 should never be rendered
    expect(screen.queryByTestId('all-sets-view')).not.toBeInTheDocument()
  })

  it('should optimize layout without toggle button spacing', () => {
    renderComponent()

    // Test will FAIL initially - toggle button padding still exists
    const mainContainer = screen.getByTestId('exercise-page-container')
    expect(mainContainer).toHaveClass('h-screen') // Full height utilization

    // No padding for removed toggle button
    expect(
      screen.queryByTestId('toggle-button-container')
    ).not.toBeInTheDocument()
  })
})
