/**
 * Workout-specific types for the Dr. Muscle X app
 *
 * These types extend the API types to include client-side state
 * and implement the hierarchical loading pattern from the mobile app.
 */

import type { ExerciseModel, WorkoutLogSerieModel } from './api'

/**
 * Represents an exercise with its sets, mirroring the mobile app's ExerciseWorkSetsModel.
 * This combines exercise metadata with its sets collection and loading states.
 *
 * @example
 * const exercise: ExerciseWorkSetsModel = {
 *   Id: 1,
 *   Label: "Bench Press",
 *   BodyPartId: 2,
 *   IsFinished: false,
 *   IsNextExercise: true,
 *   isLoadingSets: false,
 *   setsError: null,
 *   lastSetsUpdate: Date.now(),
 *   sets: [
 *     { ExerciseId: 1, Weight: { Value: 100, Unit: 'lbs' }, Reps: 10, ... }
 *   ]
 * }
 */
export interface ExerciseWorkSetsModel {
  // Exercise properties from ExerciseModel
  Id: number
  Label: string
  BodyPartId: number
  IsFinished: boolean
  IsNextExercise: boolean
  IsInProgress?: boolean // Indicates exercise has some sets saved but not completed

  // Loading state properties
  isLoadingSets: boolean
  setsError: string | null
  lastSetsUpdate: number // Timestamp of last successful update

  // Sets collection
  sets: WorkoutLogSerieModel[]

  // Optional properties from ExerciseModel that might be needed
  IsBodyweight?: boolean
  IsEasy?: boolean
  IsMedium?: boolean
  VideoLink?: string
  LocalVideo?: string
  IsAssisted?: boolean
  IsSystemExercise?: boolean
  IsSwapTarget?: boolean
  IsUnilateral?: boolean
  IsTimeBased?: boolean
  VideoUrl?: string
  IsFlexibility?: boolean
  IsWeighted?: boolean
  SetStyle?: string
  IsPlate?: boolean
  IsPyramid?: boolean
  IsNormalSets?: boolean
  IsBodypartPriority?: boolean
  IsOneHanded?: boolean
}

/**
 * Helper function to create an ExerciseWorkSetsModel from an ExerciseModel
 */
export function createExerciseWorkSetsModel(
  exercise: ExerciseModel,
  sets: WorkoutLogSerieModel[] = []
): ExerciseWorkSetsModel {
  return {
    Id: exercise.Id,
    Label: exercise.Label,
    BodyPartId: exercise.BodyPartId || 0,
    IsFinished: false,
    IsNextExercise: false,
    isLoadingSets: false,
    setsError: null,
    lastSetsUpdate: 0,
    sets,
    IsBodyweight: exercise.IsBodyweight,
    IsEasy: exercise.IsEasy,
    IsMedium: exercise.IsMedium,
    VideoLink: exercise.VideoUrl || undefined,
    VideoUrl: exercise.VideoUrl,
    LocalVideo: exercise.LocalVideo,
    IsAssisted: exercise.IsAssisted,
    IsSystemExercise: exercise.IsSystemExercise,
    IsSwapTarget: exercise.IsSwapTarget,
    IsUnilateral: exercise.IsUnilateral,
    IsTimeBased: exercise.IsTimeBased,
    IsFlexibility: exercise.IsFlexibility,
    IsWeighted: exercise.IsWeighted,
    SetStyle: exercise.SetStyle,
    IsPlate: exercise.IsPlate,
    IsPyramid: exercise.IsPyramid,
    IsNormalSets: exercise.IsNormalSets,
    IsBodypartPriority: exercise.IsBodypartPriority,
    IsOneHanded: exercise.IsOneHanded,
  }
}

/**
 * Helper function to check if an exercise has valid cached sets
 */
export function hasValidCachedSets(
  exercise: ExerciseWorkSetsModel,
  maxAge: number = 5 * 60 * 1000 // 5 minutes default
): boolean {
  // If we have sets, check if they're still valid
  if (exercise.sets && exercise.sets.length > 0) {
    const age = Date.now() - exercise.lastSetsUpdate
    return age < maxAge
  }

  // If we have an error, consider it "valid" for a shorter period to prevent infinite retries
  if (exercise.setsError && exercise.lastSetsUpdate) {
    const errorAge = Date.now() - exercise.lastSetsUpdate
    const errorRetryDelay = 30 * 1000 // 30 seconds before retrying failed requests
    return errorAge < errorRetryDelay
  }

  return false
}

/**
 * Helper function to update sets in an ExerciseWorkSetsModel
 */
export function updateExerciseSets(
  exercise: ExerciseWorkSetsModel,
  sets: WorkoutLogSerieModel[]
): ExerciseWorkSetsModel {
  return {
    ...exercise,
    sets,
    isLoadingSets: false,
    setsError: null,
    lastSetsUpdate: Date.now(),
  }
}

/**
 * Helper function to mark an exercise as loading
 */
export function markExerciseLoading(
  exercise: ExerciseWorkSetsModel
): ExerciseWorkSetsModel {
  return {
    ...exercise,
    isLoadingSets: true,
    setsError: null,
  }
}

/**
 * Helper function to mark an exercise as having an error
 */
export function markExerciseError(
  exercise: ExerciseWorkSetsModel,
  error: string
): ExerciseWorkSetsModel {
  return {
    ...exercise,
    isLoadingSets: false,
    setsError: error,
    lastSetsUpdate: Date.now(), // Mark as updated to prevent infinite retries
  }
}
