/**
 * Haptic feedback utilities for mobile devices
 */

type HapticType = 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error'

export function vibrate(type: HapticType = 'light') {
  if (!('vibrate' in navigator)) return

  const patterns: Record<HapticType, number | number[]> = {
    light: 10,
    medium: 20,
    heavy: 30,
    success: [10, 20, 10],
    warning: [20, 10, 20],
    error: [30, 10, 30, 10, 30],
  }

  try {
    navigator.vibrate(patterns[type])
  } catch (error) {
    // Silently fail if vibration is not supported
    // eslint-disable-next-line no-console
    console.debug('Vibration not supported:', error)
  }
}

export function canVibrate(): boolean {
  return 'vibrate' in navigator
}
