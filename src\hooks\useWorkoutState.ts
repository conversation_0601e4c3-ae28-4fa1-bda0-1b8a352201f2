import { useState, useEffect, useRef } from 'react'
import { WorkoutTemplateGroupModel } from '@/types'
import {
  ExerciseWorkSetsModel,
  createExerciseWorkSetsModel,
} from '@/types/workout'
import { ExerciseModel } from '@/types/api/exercise'
import { WorkoutCache } from '@/utils/workoutCache'
import { PerformanceMonitor, PerformanceMarks } from '@/utils/performance'
import { useWorkoutStore } from '@/stores/workoutStore/index'
import { useAuthStore } from '@/stores/authStore'

export function useWorkoutState() {
  const { isAuthenticated } = useAuthStore()
  const [isOffline, setIsOffline] = useState(!navigator.onLine)
  const [cachedWorkout, setCachedWorkout] = useState<
    WorkoutTemplateGroupModel[] | null
  >(null)

  // Track which exercises have had their sets loaded
  const loadedExerciseSets = useRef(new Set<number>())
  const [exerciseWorkSetsModels, setExerciseWorkSetsModels] = useState<
    ExerciseWorkSetsModel[]
  >([])

  const {
    exercises,
    currentExerciseIndex,
    currentSetIndex,
    currentWorkout,
    workoutSession,
    isLoading,
    error,
  } = useWorkoutStore()

  const currentExercise = exercises[currentExerciseIndex]

  // Load cached data on mount
  useEffect(() => {
    const cached = WorkoutCache.get()
    if (cached) {
      setCachedWorkout(cached)
      PerformanceMonitor.mark(PerformanceMarks.CACHE_HIT)
    } else {
      PerformanceMonitor.mark(PerformanceMarks.CACHE_MISS)
    }
  }, [])

  // Clear cache on logout
  useEffect(() => {
    if (!isAuthenticated) {
      WorkoutCache.clear()
      setCachedWorkout(null)
    }
  }, [isAuthenticated])

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOffline(false)
    const handleOffline = () => setIsOffline(true)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Update exercise work sets models when base exercises change
  useEffect(() => {
    if (!exercises || exercises.length === 0) {
      setExerciseWorkSetsModels([])
      loadedExerciseSets.current.clear()
      return
    }

    // Convert to ExerciseWorkSetsModel[] preserving existing sets data
    const updatedModels = exercises.map((exercise) => {
      // Find existing model to preserve sets data
      const existingModel = exerciseWorkSetsModels.find(
        (m) => m.Id === exercise.Id
      )

      if (existingModel) {
        // Preserve existing sets and loading state
        return {
          ...existingModel,
          // Update exercise metadata that might have changed
          Label: exercise.Label,
          BodyPartId: exercise.BodyPartId || 0,
          IsFinished: exercise.IsFinished,
          IsInProgress: Boolean(
            (exercise as ExerciseModel & { IsInProgress?: boolean })
              .IsInProgress
          ),
        }
      }

      // Create new model for exercises not yet tracked
      const newModel = createExerciseWorkSetsModel(exercise)

      // Set initial status from exercise if available
      newModel.IsFinished = exercise.IsFinished || false
      newModel.IsInProgress = Boolean(
        (exercise as ExerciseModel & { IsInProgress?: boolean }).IsInProgress
      )

      return newModel
    })

    setExerciseWorkSetsModels(updatedModels)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [exercises]) // Only depend on exercises, not exerciseWorkSetsModels to avoid loops

  return {
    isOffline,
    cachedWorkout,
    exerciseWorkSetsModels,
    setExerciseWorkSetsModels,
    loadedExerciseSets,
    currentExercise,
    currentExerciseIndex,
    currentSetIndex,
    currentWorkout,
    workoutSession,
    isLoading,
    error,
    exercises,
  }
}
