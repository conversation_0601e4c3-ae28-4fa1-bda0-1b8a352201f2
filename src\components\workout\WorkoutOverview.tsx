'use client'

import { useEffect, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { ExerciseCard } from '@/components/workout/ExerciseCard'
import { usePullToRefresh } from '@/hooks/usePullToRefresh'
import { PullToRefreshIndicator } from '@/components/PullToRefreshIndicator'
import { ExerciseItemSkeleton } from '@/components/ui/Skeletons'
import { FloatingCTAButton } from '@/components/ui'
import { PerformanceMonitor, PerformanceMarks } from '@/utils/performance'
import { debugLog } from '@/utils/debugLog'
import {
  WorkoutLoadingState,
  WorkoutErrorState,
  NoWorkoutState,
  StatusIndicators,
} from './WorkoutOverviewStates'
import { useWorkoutActions } from './WorkoutOverviewActions'

export function WorkoutOverview() {
  const router = useRouter()
  const {
    todaysWorkout,
    isLoadingWorkout,
    workoutError,
    startWorkout,
    userProgramInfo,
    exercises,
    exerciseWorkSetsModels,
    expectedExerciseCount,
    hasInitialData,
    isOffline,
    refreshWorkout,
    updateExerciseWorkSets,
    workoutSession,
    finishWorkout,
    isLoading,
    loadExerciseRecommendation,
  } = useWorkout()

  const {
    isStartingWorkout,
    handleStartWorkout,
    handleFinishWorkout,
    handleExerciseClick: handleExerciseClickOriginal,
    handleRetryExercise,
    hasCompletedSets,
    getButtonLabel,
    getButtonAriaLabel,
  } = useWorkoutActions({
    todaysWorkout,
    startWorkout,
    exercises,
    workoutSession,
    loadExerciseRecommendation,
    updateExerciseWorkSets,
    finishWorkout,
  })

  // Use the original handleExerciseClick which now navigates to V2
  const handleExerciseClick = handleExerciseClickOriginal

  // Debug component lifecycle
  useEffect(() => {
    debugLog('[WorkoutOverview] Component mounted')

    return () => {
      debugLog('[WorkoutOverview] Component unmounting!')
    }
  }, [])

  // Mark when workout page becomes interactive
  useEffect(() => {
    if (!isLoadingWorkout && todaysWorkout) {
      PerformanceMonitor.mark(PerformanceMarks.WORKOUT_PAGE_INTERACTIVE)

      // Report metrics in development
      if (process.env.NODE_ENV === 'development') {
        PerformanceMonitor.reportKeyMetrics()
      }
    }
  }, [isLoadingWorkout, todaysWorkout])

  // Pull-to-refresh functionality
  const pullToRefresh = usePullToRefresh({
    onRefresh: refreshWorkout,
    threshold: 120,
    enabled: !isLoadingWorkout && hasInitialData,
    deadZone: 50,
    resistance: 3.5,
  })

  // Check if we have valid workout data structure
  const workoutGroup = todaysWorkout?.[0]
  const workout = workoutGroup?.WorkoutTemplates?.[0]

  // Extract exercises from workout data for display
  const previewExercises = useMemo(() => {
    if (!workout?.Exercises) {
      return []
    }
    // Create preview models from workout exercises
    return workout.Exercises.map((exercise) => ({
      Id: exercise.Id,
      Label: exercise.Label,
      BodyPartId: exercise.BodyPartId || 0,
      IsFinished: false,
      IsNextExercise: false,
      isLoadingSets: false,
      setsError: null,
      lastSetsUpdate: 0,
      sets: [], // Empty sets for preview
      IsBodyweight: exercise.IsBodyweight,
      WorkoutSets: [], // For compatibility with ExerciseCard
      isLoading: false,
      error: null,
    }))
  }, [workout])

  // Use exerciseWorkSetsModels when workout is active, otherwise use preview
  // If exerciseWorkSetsModels is empty but we have a session, still show preview
  const displayExercises =
    workoutSession && exerciseWorkSetsModels.length > 0
      ? exerciseWorkSetsModels
      : previewExercises

  // Show skeleton only when no workout data is available at all
  if (isLoadingWorkout && !todaysWorkout) {
    return <WorkoutLoadingState isStartingWorkout={isStartingWorkout} />
  }

  // Error state
  if (workoutError) {
    return <WorkoutErrorState error={workoutError} />
  }

  // No workout state - only show when we're sure there's no workout available
  if (
    !isLoading &&
    !isLoadingWorkout &&
    (!todaysWorkout || todaysWorkout.length === 0 || !workout)
  ) {
    return (
      <NoWorkoutState
        todaysWorkout={todaysWorkout}
        isLoadingWorkout={isLoadingWorkout}
        hasInitialData={hasInitialData}
        userProgramInfo={userProgramInfo}
      />
    )
  }

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    debugLog('[WorkoutOverview] Workout data check:', {
      hasTodaysWorkout: !!todaysWorkout,
      todaysWorkoutLength: todaysWorkout?.length,
      workoutGroup,
      hasWorkoutTemplates: !!workoutGroup?.WorkoutTemplates,
      workoutTemplatesLength: workoutGroup?.WorkoutTemplates?.length,
      workout,
    })
  }

  return (
    <div
      className="h-full flex flex-col bg-bg-primary relative"
      data-testid="workout-overview-container"
    >
      {/* Pull-to-refresh indicator */}
      <PullToRefreshIndicator
        pullDistance={pullToRefresh.pullDistance}
        threshold={120}
        isRefreshing={pullToRefresh.isRefreshing}
        isPulling={pullToRefresh.isPulling}
      />
      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 pb-24">
          {/* Extra bottom padding for fixed button */}
          <div className="mx-auto max-w-lg">
            {/* Status indicators */}
            <StatusIndicators
              isOffline={isOffline}
              isRefreshing={pullToRefresh.isRefreshing}
            />

            {/* Test V2 UI Banner - Remove in production */}
            {displayExercises.length > 0 && (
              <div className="mb-4 p-3 bg-brand-primary/10 border border-brand-primary/20 rounded-lg">
                <p className="text-sm text-brand-primary font-medium mb-2">
                  🚀 Try our new UI
                </p>
                <button
                  onClick={async () => {
                    // Find the first incomplete exercise
                    const firstIncompleteExercise = displayExercises.find(
                      (ex) => !ex.IsFinished
                    )
                    const exerciseToOpen =
                      firstIncompleteExercise || displayExercises[0]

                    if (exerciseToOpen) {
                      // Start workout if not already started
                      if (todaysWorkout && !workoutSession) {
                        const result = await startWorkout(todaysWorkout)
                        if (!result.success) {
                          return
                        }
                      }

                      // Navigate to V2 exercise page with exercise name
                      const exerciseName = exerciseToOpen.Label || ''
                      const v2Url = exerciseName
                        ? `/workout/exercise-v2/${exerciseToOpen.Id}?exerciseName=${encodeURIComponent(exerciseName)}`
                        : `/workout/exercise-v2/${exerciseToOpen.Id}`
                      router.push(v2Url)
                    }
                  }}
                  className="text-sm text-brand-primary underline hover:no-underline"
                >
                  Start with new exercise view →
                </button>
              </div>
            )}

            {/* Exercise List */}
            <div className="mb-8 space-y-3">
              {/* Show loaded exercises using ExerciseCard */}
              {displayExercises?.map((exercise) => (
                <ExerciseCard
                  key={exercise.Id}
                  exercise={exercise}
                  onExerciseClick={handleExerciseClick}
                  onRetry={handleRetryExercise}
                  workout={workout}
                />
              ))}

              {/* Show skeleton loaders for remaining exercises if still loading */}
              {isLoadingWorkout &&
              expectedExerciseCount &&
              displayExercises &&
              expectedExerciseCount > displayExercises.length
                ? Array.from({
                    length: expectedExerciseCount - displayExercises.length,
                  }).map((_, index) => (
                    <ExerciseItemSkeleton
                      key={`skeleton-${displayExercises.length + index}`}
                    />
                  ))
                : null}
            </div>
          </div>
        </div>
      </div>

      {/* Floating CTA Button */}
      <FloatingCTAButton
        onClick={
          workoutSession && hasCompletedSets
            ? handleFinishWorkout
            : handleStartWorkout
        }
        label={getButtonLabel()}
        ariaLabel={getButtonAriaLabel()}
        isLoading={isStartingWorkout}
        disabled={isStartingWorkout}
      />
    </div>
  )
}
