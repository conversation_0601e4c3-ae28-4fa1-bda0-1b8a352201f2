import { test, expect } from '@playwright/test'

test.describe('Navigation Hide on Scroll', () => {
  test.beforeEach(async ({ page }) => {
    // Use real authentication to ensure navigation is shown
    await page.goto('/login')

    // Fill in login form
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')

    // Submit form
    await page.click('button[type="submit"]')

    // Wait for navigation to complete
    await page.waitForURL('**/program', { timeout: 10000 })
  })

  test('should hide navigation when scrolling down', async ({ page }) => {
    // Already on program page from beforeEach

    // Wait for navigation to be visible
    const navBar = page.locator('header[role="banner"]')
    await expect(navBar).toBeVisible()

    // Verify initial state
    const initialTransform = await navBar.evaluate(
      (el) => getComputedStyle(el).transform
    )
    // Initial transform logged for debugging

    // Scroll down significantly
    await page.evaluate(() => window.scrollTo(0, 300))
    await page.waitForTimeout(100) // Wait for debounce and transition

    // Check if navigation is hidden
    const hiddenTransform = await navBar.evaluate(
      (el) => getComputedStyle(el).transform
    )
    // Hidden transform logged for debugging

    // Should contain negative Y value (hidden)
    expect(hiddenTransform).not.toBe(initialTransform)
  })

  test('should show navigation when scrolling up', async ({ page }) => {
    // Already on program page
    const navBar = page.locator('header[role="banner"]')

    // First scroll down to hide nav
    await page.evaluate(() => window.scrollTo(0, 300))
    await page.waitForTimeout(100)

    // Then scroll up slightly
    await page.evaluate(() => window.scrollTo(0, 200))
    await page.waitForTimeout(100)

    // Get transform after scrolling up
    const visibleTransform = await navBar.evaluate(
      (el) => getComputedStyle(el).transform
    )
    // Visible transform logged for debugging

    // Should be visible (no negative Y translation)
    expect(visibleTransform).toBe('none')
  })

  test('should always show navigation at top of page', async ({ page }) => {
    // Already on program page
    const navBar = page.locator('header[role="banner"]')

    // Scroll down then back to top
    await page.evaluate(() => window.scrollTo(0, 300))
    await page.waitForTimeout(100)
    await page.evaluate(() => window.scrollTo(0, 0))
    await page.waitForTimeout(100)

    // Get transform at top
    const topTransform = await navBar.evaluate(
      (el) => getComputedStyle(el).transform
    )
    // Transform at top logged for debugging

    // Should always be visible at top
    expect(topTransform).toBe('none')
  })
})
