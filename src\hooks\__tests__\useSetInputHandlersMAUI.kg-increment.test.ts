import { renderHook } from '@testing-library/react'
import { useSetInputHandlersMAUI } from '../useSetInputHandlersMAUI'

describe('useSetInputHandlersMAUI - kg increment fix', () => {
  const defaultProps = {
    exercise: null,
    recommendation: null,
    isKg: true,
    userBodyWeight: 80,
    onWeightChange: vi.fn(),
    onRepsChange: vi.fn(),
    onOneRMUpdate: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Weight increment/decrement with kg unit', () => {
    it('should increment weight by 1 when isKg is true', () => {
      // GIVEN: Hook initialized with kg unit
      const { result } = renderHook(() =>
        useSetInputHandlersMAUI({ ...defaultProps, isKg: true })
      )

      // WHEN: incrementWeight is called with current weight of 50
      const newWeight = result.current.incrementWeight(50)

      // THEN: Weight should increase by 1
      expect(newWeight).toBe(51) // 50 + 1
    })

    it('should decrement weight by 1 when isKg is true', () => {
      // GIVEN: Hook initialized with kg unit
      const { result } = renderHook(() =>
        useSetInputHandlersMAUI({ ...defaultProps, isKg: true })
      )

      // WHEN: decrementWeight is called with current weight of 50
      const newWeight = result.current.decrementWeight(50)

      // THEN: Weight should decrease by 1
      expect(newWeight).toBe(49) // 50 - 1
    })

    it('should increment weight by 2.5 when isKg is false', () => {
      // GIVEN: Hook initialized with lbs unit
      const { result } = renderHook(() =>
        useSetInputHandlersMAUI({ ...defaultProps, isKg: false })
      )

      // WHEN: incrementWeight is called with current weight of 100
      const newWeight = result.current.incrementWeight(100)

      // THEN: Weight should increase by 2.5
      expect(newWeight).toBe(102.5) // 100 + 2.5
    })

    it('should decrement weight by 2.5 when isKg is false', () => {
      // GIVEN: Hook initialized with lbs unit
      const { result } = renderHook(() =>
        useSetInputHandlersMAUI({ ...defaultProps, isKg: false })
      )

      // WHEN: decrementWeight is called with current weight of 100
      const newWeight = result.current.decrementWeight(100)

      // THEN: Weight should decrease by 2.5
      expect(newWeight).toBe(97.5) // 100 - 2.5
    })

    it('should not allow weight to go below 0 when decrementing', () => {
      // GIVEN: Hook initialized with kg unit
      const { result } = renderHook(() =>
        useSetInputHandlersMAUI({ ...defaultProps, isKg: true })
      )

      // WHEN: decrementWeight is called with weight less than increment
      const newWeight = result.current.decrementWeight(0.5)

      // THEN: Weight should be 0, not negative
      expect(newWeight).toBe(0) // Math.max(0, 0.5 - 1) = 0
    })

    it('should handle decimal truncation properly', () => {
      // GIVEN: Hook initialized with kg unit
      const { result } = renderHook(() =>
        useSetInputHandlersMAUI({ ...defaultProps, isKg: true })
      )

      // WHEN: incrementWeight is called with decimal weight
      const newWeight = result.current.incrementWeight(50.123456)

      // THEN: Result should be truncated to 2 decimal places
      expect(newWeight).toBe(51.12) // 50.123456 + 1 = 51.123456, truncated to 51.12
    })
  })

  describe('Reps increment/decrement', () => {
    it('should increment reps by 1', () => {
      // GIVEN: Hook initialized
      const { result } = renderHook(() => useSetInputHandlersMAUI(defaultProps))

      // WHEN: incrementReps is called
      const newReps = result.current.incrementReps(10)

      // THEN: Reps should increase by 1
      expect(newReps).toBe(11)
    })

    it('should not allow reps to exceed 100', () => {
      // GIVEN: Hook initialized
      const { result } = renderHook(() => useSetInputHandlersMAUI(defaultProps))

      // WHEN: incrementReps is called at max
      const newReps = result.current.incrementReps(100)

      // THEN: Reps should stay at 100
      expect(newReps).toBe(100)
    })

    it('should not allow reps to go below 1', () => {
      // GIVEN: Hook initialized
      const { result } = renderHook(() => useSetInputHandlersMAUI(defaultProps))

      // WHEN: decrementReps is called at min
      const newReps = result.current.decrementReps(1)

      // THEN: Reps should stay at 1
      expect(newReps).toBe(1)
    })
  })
})
