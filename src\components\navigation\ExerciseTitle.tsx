'use client'

import React, { useRef, useEffect, useState } from 'react'

interface ExerciseTitleProps {
  title: string
  titleDisplayMode:
    | 'auto-scroll'
    | 'multiline'
    | 'swipe-scroll'
    | 'smart-truncate'
  isExercisePage: boolean
}

export function ExerciseTitle({
  title,
  titleDisplayMode,
  isExercisePage,
}: ExerciseTitleProps) {
  const titleRef = useRef<HTMLHeadingElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [needsScroll, setNeedsScroll] = useState(false)
  const [isPaused, setIsPaused] = useState(false)

  useEffect(() => {
    if (
      titleRef.current &&
      containerRef.current &&
      isExercisePage &&
      titleDisplayMode === 'auto-scroll'
    ) {
      const titleWidth = titleRef.current.scrollWidth
      const containerWidth = containerRef.current.clientWidth
      setNeedsScroll(titleWidth > containerWidth)
    }
  }, [title, isExercisePage, titleDisplayMode])

  if (titleDisplayMode === 'multiline') {
    return (
      <h1
        ref={titleRef}
        className="text-4xl font-bold bg-gradient-to-r from-brand-gold-start to-brand-gold-end bg-clip-text text-transparent line-clamp-2 whitespace-normal leading-tight"
      >
        {title}
      </h1>
    )
  }

  if (titleDisplayMode === 'swipe-scroll') {
    return (
      <div className="overflow-x-auto scrollbar-hide" data-swipe-enabled="true">
        <h1
          ref={titleRef}
          className="text-4xl font-bold bg-gradient-to-r from-brand-gold-start to-brand-gold-end bg-clip-text text-transparent whitespace-nowrap"
        >
          {title}
        </h1>
      </div>
    )
  }

  if (titleDisplayMode === 'smart-truncate') {
    return (
      <h1
        ref={titleRef}
        className="text-4xl font-bold bg-gradient-to-r from-brand-gold-start to-brand-gold-end bg-clip-text text-transparent truncate"
        data-tooltip-content={title}
        data-expandable="true"
      >
        {title}
      </h1>
    )
  }

  // Default: enhanced auto-scroll
  return (
    <div className="relative overflow-hidden" ref={containerRef}>
      <h1
        ref={titleRef}
        className={`text-4xl font-bold bg-gradient-to-r from-brand-gold-start to-brand-gold-end bg-clip-text text-transparent ${
          needsScroll
            ? `animate-scroll-enhanced whitespace-nowrap ${
                isPaused ? 'animation-play-state-paused' : ''
              }`
            : 'truncate'
        }`}
        style={
          needsScroll
            ? ({
                '--scroll-width': `${titleRef.current?.scrollWidth || 0}px`,
              } as React.CSSProperties)
            : {}
        }
        data-scroll-pausable="true"
        onTouchStart={() => setIsPaused(true)}
        onTouchEnd={() => setIsPaused(false)}
        onMouseEnter={() => setIsPaused(true)}
        onMouseLeave={() => setIsPaused(false)}
      >
        {title}
      </h1>
    </div>
  )
}
