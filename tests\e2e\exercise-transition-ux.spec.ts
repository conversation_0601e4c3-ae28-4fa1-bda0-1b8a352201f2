import { test, expect } from '@playwright/test'
import { login } from './helpers'

test.describe('Exercise Transition UX', () => {
  test.beforeEach(async ({ page }) => {
    // Mock workout program response
    await page.route('**/api/trainee/program', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Workouts: [
            {
              WorkoutTemplates: [
                {
                  Exercises: [
                    { Id: 1, Label: 'Bench Press', BodyPartId: 1 },
                    { Id: 2, Label: 'Squat', BodyPartId: 2 },
                  ],
                },
              ],
            },
          ],
        }),
      })
    })

    // Mock exercise recommendation API to delay response
    await page.route('**/api/exercise/recommendation/*', async (route) => {
      // Delay to simulate loading
      await new Promise((resolve) => setTimeout(resolve, 1000))
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 1,
          Reps: 10,
          Weight: { Kg: 45, Lb: 100 },
          WeightIncrement: 5,
        }),
      })
    })

    // Mock login
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')
    await page.goto('/workout')
  })

  test('should show smooth transition when clicking exercise without loaded recommendation', async ({
    page,
  }) => {
    // Click on the first exercise
    await page.click('[data-testid="exercise-card-1"]')

    // Should show transition screen with checkmark
    await expect(page.getByTestId('exercise-transition-screen')).toBeVisible()
    await expect(page.getByTestId('success-icon')).toBeVisible()

    // After 400ms, should show exercise name
    await page.waitForTimeout(400)
    await expect(page.getByTestId('success-icon')).not.toBeVisible()
    await expect(page.getByText('Bench Press')).toBeVisible()

    // After transition completes, should show exercise page
    await expect(page.getByTestId('exercise-page-content')).toBeVisible({
      timeout: 2000,
    })
  })

  test('should skip transition when recommendation is already loaded', async ({
    page,
  }) => {
    // Pre-load recommendation by mocking immediate response
    await page.route('**/api/exercise/recommendation/*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 1,
          Reps: 10,
          Weight: { Kg: 45, Lb: 100 },
          WeightIncrement: 5,
        }),
      })
    })

    // Click exercise after recommendation is loaded
    await page.click('[data-testid="exercise-card-1"]')

    // Should NOT show transition screen
    await expect(
      page.getByTestId('exercise-transition-screen')
    ).not.toBeVisible()

    // Should go directly to exercise page
    await expect(page.getByTestId('exercise-page-content')).toBeVisible()
  })

  test('should work for both v1 and v2 exercise pages', async ({ page }) => {
    // Test V2 page
    await page.click('[data-testid="try-new-ui-button"]')

    // Should show transition for V2
    await expect(page.getByTestId('exercise-transition-screen')).toBeVisible()
    await expect(page.getByTestId('success-icon')).toBeVisible()

    // Navigate back and test V1
    await page.goBack()
    await page.waitForTimeout(500)
    await page.click('[data-testid="exercise-card-2"]') // Different exercise

    // Should show transition for V1
    await expect(page.getByTestId('exercise-transition-screen')).toBeVisible()
    await expect(page.getByText('Squat')).toBeVisible({ timeout: 1000 })
  })

  test('should handle navigation with special characters in exercise name', async ({
    page,
  }) => {
    // Mock workout with special characters
    await page.route('**/api/trainee/program', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Workouts: [
            {
              WorkoutTemplates: [
                {
                  Exercises: [
                    {
                      Id: 3,
                      Label: 'Cable Fly (High to Low)',
                      BodyPartId: 1,
                    },
                  ],
                },
              ],
            },
          ],
        }),
      })
    })

    await page.reload()
    await page.click('[data-testid="exercise-card-3"]')

    // Should show transition with properly encoded name
    await expect(page.getByTestId('exercise-transition-screen')).toBeVisible()
    await page.waitForTimeout(400)
    await expect(page.getByText('Cable Fly (High to Low)')).toBeVisible()
  })

  test('should handle rapid navigation between exercises', async ({ page }) => {
    // Click first exercise
    await page.click('[data-testid="exercise-card-1"]')

    // Immediately go back before transition completes
    await page.waitForTimeout(200)
    await page.goBack()

    // Click second exercise
    await page.click('[data-testid="exercise-card-2"]')

    // Should show correct transition for second exercise
    await page.waitForTimeout(400)
    await expect(page.getByText('Squat')).toBeVisible()

    // Should eventually show correct exercise page
    await expect(page.url()).toContain('/exercise/2')
  })
})
