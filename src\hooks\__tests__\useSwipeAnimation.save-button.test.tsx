import { renderHook, act } from '@testing-library/react'
import { useSwipeAnimation } from '../useSwipeAnimation'
import * as framerMotion from 'framer-motion'
import { vi } from 'vitest'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  useAnimation: vi.fn(),
}))

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

describe('useSwipeAnimation save button animation', () => {
  let mockControls: {
    start: ReturnType<typeof vi.fn>
  }
  let onComplete: ReturnType<typeof vi.fn>
  let onSkip: ReturnType<typeof vi.fn>

  beforeEach(() => {
    mockControls = {
      start: vi.fn().mockResolvedValue(undefined),
    }
    onComplete = vi.fn()
    onSkip = vi.fn()

    vi.mocked(framerMotion.useAnimation).mockReturnValue(mockControls as any)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('save button fade animation', () => {
    it('should expose triggerFadeAnimation function', () => {
      const { result } = renderHook(() =>
        useSwipeAnimation({ onComplete, onSkip })
      )

      // Test that triggerFadeAnimation is exposed
      expect(result.current.triggerFadeAnimation).toBeDefined()
      expect(typeof result.current.triggerFadeAnimation).toBe('function')
    })

    it('should trigger fade animation sequence when triggerFadeAnimation is called', async () => {
      const { result } = renderHook(() =>
        useSwipeAnimation({ onComplete, onSkip })
      )

      // Call the fade animation trigger
      await act(async () => {
        await result.current.triggerFadeAnimation()
      })

      // Verify animation sequence matches swipe animation
      expect(mockControls.start).toHaveBeenCalledTimes(3)

      // First: exit with opacity 0
      expect(mockControls.start).toHaveBeenNthCalledWith(1, {
        x: 0,
        opacity: 0,
        transition: { duration: 0.2 },
      })

      // Second: reset (already at center, just ensure opacity 0)
      expect(mockControls.start).toHaveBeenNthCalledWith(2, {
        x: 0,
        opacity: 0,
      })

      // Third: fade in at center (33% faster: 0.2s instead of 0.3s)
      expect(mockControls.start).toHaveBeenNthCalledWith(3, {
        x: 0,
        opacity: 1,
        transition: { duration: 0.2 },
      })
    })

    it('should not call onComplete or onSkip when triggerFadeAnimation is used', async () => {
      const { result } = renderHook(() =>
        useSwipeAnimation({ onComplete, onSkip })
      )

      await act(async () => {
        await result.current.triggerFadeAnimation()
      })

      // Should not call callbacks - those are handled by the component
      expect(onComplete).not.toHaveBeenCalled()
      expect(onSkip).not.toHaveBeenCalled()
    })

    it('should handle animation errors gracefully', async () => {
      mockControls.start.mockRejectedValueOnce(new Error('Animation failed'))

      const { result } = renderHook(() =>
        useSwipeAnimation({ onComplete, onSkip })
      )

      // Should not throw
      await act(async () => {
        await expect(
          result.current.triggerFadeAnimation()
        ).resolves.not.toThrow()
      })
    })
  })
})
