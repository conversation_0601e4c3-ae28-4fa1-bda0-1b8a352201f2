'use client'

import { usePathname, useRouter } from 'next/navigation'
import { IOSNavigationBar } from './navigation'
import { NavigationProvider, useNavigation } from '@/contexts/NavigationContext'
import { getNavigationConfig } from '@/config/navigationConfig'
import { useAuth } from '@/hooks/useAuth'
import { useState, useEffect, useRef } from 'react'
import { UserMenu } from './UserMenu'
import { GradientKebabMenuIcon } from './icons/GradientKebabMenuIcon'
import { useWorkoutStore } from '@/stores/workoutStore'
import { isValidExerciseId } from '@/utils/exerciseValidation'
import { logger } from '@/utils/logger'

interface NavigationWrapperProps {
  children: React.ReactNode
}

// Pages where navigation should not be shown
const EXCLUDED_PATHS = ['/login', '/signup', '/reset-password']

function NavigationContent({ children }: NavigationWrapperProps) {
  const pathname = usePathname()
  const router = useRouter()
  const {
    goBack,
    title: dynamicTitle,
    setTitle,
    setInfo,
    setProgress,
  } = useNavigation()
  const { user } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [showBackConfirm, setShowBackConfirm] = useState(false)
  const getCurrentExercise = useWorkoutStore(
    (state) => state.getCurrentExercise
  )

  // Track previous pathname to prevent infinite loops
  const previousPathnameRef = useRef<string>('')

  // Clear dynamic title when navigating away from pages that set it
  useEffect(() => {
    // Only proceed if pathname actually changed
    if (previousPathnameRef.current === pathname) return

    const wasOnDynamicTitlePage =
      previousPathnameRef.current.startsWith('/workout/exercise/')
    const isNowOnDynamicTitlePage = pathname.startsWith('/workout/exercise/')

    // Clear title only when leaving a dynamic title page
    if (wasOnDynamicTitlePage && !isNowOnDynamicTitlePage && dynamicTitle) {
      setTitle('')
    }

    // Update the ref for next comparison
    previousPathnameRef.current = pathname
  }, [pathname, dynamicTitle, setTitle])

  // Don't show navigation on excluded pages
  const shouldShowNavigation = !EXCLUDED_PATHS.some((path) =>
    pathname.startsWith(path)
  )

  if (!shouldShowNavigation) {
    return children as React.ReactElement
  }

  // Get navigation config for current route
  const navConfig = getNavigationConfig(pathname)

  // Check if we're on an exercise page (but not v2)
  // REMOVED: isExercisePage flag to ensure consistent navigation styling

  if (!navConfig) {
    // Fallback to default navigation if no config found
    return children as React.ReactElement
  }

  // Handle left element
  let leftElement = null
  if (navConfig.leftElement && typeof navConfig.leftElement !== 'string') {
    const LeftComponent = navConfig.leftElement
    leftElement = <LeftComponent />
  }

  // Handle right element
  let rightElement = null
  if (navConfig.rightElement) {
    if (navConfig.rightElement === 'UserAvatar' && user) {
      rightElement = (
        <button
          onClick={() => setIsMenuOpen(true)}
          className="flex items-center p-2 -mr-2 rounded-lg hover:bg-bg-secondary transition-colors"
          aria-label="Open user menu"
        >
          <GradientKebabMenuIcon size={24} />
        </button>
      )
    } else if (typeof navConfig.rightElement !== 'string') {
      const RightComponent = navConfig.rightElement
      rightElement = <RightComponent />
    }
  }

  // Special handling for rest timer back navigation
  const handleBackClick = () => {
    if (navConfig.confirmBeforeBack) {
      setShowBackConfirm(true)
    } else if (navConfig.backRoute) {
      router.push(navConfig.backRoute)
    } else if (pathname === '/workout/rest-timer') {
      // For rest timer, check if we're between sets
      const searchParams = new URLSearchParams(window.location.search)
      const isBetweenSets = searchParams.get('between-sets') === 'true'
      if (isBetweenSets) {
        // Navigate back to the current exercise page
        const currentExercise = getCurrentExercise()
        if (currentExercise?.Id && isValidExerciseId(currentExercise.Id)) {
          router.push(`/workout/exercise/${currentExercise.Id}`)
        } else {
          // Fallback to workout page if no current exercise or invalid ID
          logger.warn(
            'Invalid exercise ID in NavigationWrapper back navigation:',
            currentExercise?.Id
          )
          router.push('/workout')
        }
      } else {
        // For rest between exercises, go to workout overview
        router.push('/workout')
      }
    } else {
      goBack()
    }
  }

  return (
    <>
      <IOSNavigationBar
        title={dynamicTitle || navConfig.title}
        showBackButton={navConfig.showBackButton}
        onBackClick={handleBackClick}
        leftElement={leftElement}
        rightElement={rightElement}
        isExercisePage={false}
        setInfo={setInfo}
        progressValue={setProgress.progressValue}
        totalSets={setProgress.totalSets}
        completedSets={setProgress.completedSets}
      />
      {children}

      {/* User Menu */}
      {user && (
        <UserMenu
          isOpen={isMenuOpen}
          onClose={() => setIsMenuOpen(false)}
          user={user}
        />
      )}

      {/* Back Confirmation Dialog */}
      {showBackConfirm && navConfig.confirmBeforeBack && (
        <div className="fixed inset-0 bg-bg-overlay flex items-center justify-center p-4 z-50">
          <div
            className="bg-bg-secondary rounded-theme p-6 max-w-sm w-full shadow-theme-xl"
            role="dialog"
            aria-modal="true"
          >
            <h3 className="text-lg font-semibold mb-2 text-text-primary">
              Are you sure you want to go back?
            </h3>
            <p className="text-text-secondary mb-6">
              {navConfig.backConfirmMessage ||
                'Any unsaved changes will be lost.'}
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowBackConfirm(false)}
                className="flex-1 py-2 px-4 border border-brand-primary/20 rounded-theme hover:bg-bg-tertiary text-text-primary transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  setShowBackConfirm(false)
                  if (navConfig.backRoute) {
                    router.push(navConfig.backRoute)
                  } else {
                    goBack()
                  }
                }}
                className="flex-1 py-2 px-4 bg-red-600 text-white rounded-theme hover:bg-red-700 shadow-theme-md transition-all"
              >
                Yes, go back
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export function NavigationWrapper({ children }: NavigationWrapperProps) {
  return (
    <NavigationProvider>
      <NavigationContent>{children}</NavigationContent>
    </NavigationProvider>
  )
}
