import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useWorkoutActions } from '../WorkoutOverviewActions'

// Mock next/navigation
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

describe('WorkoutOverviewActions - Exercise Name Navigation', () => {
  const mockProps = {
    todaysWorkout: [
      {
        WorkoutTemplates: [
          {
            Exercises: [
              { Id: 1, Label: 'Bench Press' },
              { Id: 2, Label: 'Squat' },
            ],
          },
        ],
      },
    ],
    startWorkout: vi
      .fn()
      .mockResolvedValue({ success: true, firstExerciseId: 1 }),
    exercises: [
      { Id: 1, Label: 'Bench Press', sets: [] },
      { Id: 2, Label: 'Squat', sets: [] },
    ],
    workoutSession: null,
    loadExerciseRecommendation: vi.fn(),
    updateExerciseWorkSets: vi.fn(),
    finishWorkout: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should navigate with exercise name in search params', async () => {
    const { result } = renderHook(() => useWorkoutActions(mockProps))

    await act(async () => {
      await result.current.handleExerciseClick(1)
    })

    // Should navigate with exercise name in search params
    expect(mockPush).toHaveBeenCalledWith(
      '/workout/exercise/1?exerciseName=Bench%20Press'
    )
  })

  it('should handle exercise click with URL encoding for special characters', async () => {
    const propsWithSpecialChars = {
      ...mockProps,
      exercises: [{ Id: 3, Label: 'Cable Fly (High to Low)', sets: [] }],
    }

    const { result } = renderHook(() =>
      useWorkoutActions(propsWithSpecialChars)
    )

    await act(async () => {
      await result.current.handleExerciseClick(3)
    })

    // Should properly encode special characters
    // Note: encodeURIComponent encodes spaces but not parentheses in this context
    expect(mockPush).toHaveBeenCalledWith(
      '/workout/exercise/3?exerciseName=Cable%20Fly%20(High%20to%20Low)'
    )
  })

  it('should navigate without exercise name if not found', async () => {
    const { result } = renderHook(() => useWorkoutActions(mockProps))

    await act(async () => {
      await result.current.handleExerciseClick(999) // Non-existent exercise
    })

    // Should still navigate but without exercise name
    expect(mockPush).toHaveBeenCalledWith('/workout/exercise/999')
  })

  it('should start workout before navigation if not started', async () => {
    const { result } = renderHook(() => useWorkoutActions(mockProps))

    await act(async () => {
      await result.current.handleExerciseClick(1)
    })

    // Should start workout first
    expect(mockProps.startWorkout).toHaveBeenCalledWith(mockProps.todaysWorkout)
    // Then navigate with exercise name
    expect(mockPush).toHaveBeenCalledWith(
      '/workout/exercise/1?exerciseName=Bench%20Press'
    )
  })

  it('should not start workout if already in session', async () => {
    const propsWithSession = {
      ...mockProps,
      workoutSession: { id: 1 },
    }

    const { result } = renderHook(() => useWorkoutActions(propsWithSession))

    await act(async () => {
      await result.current.handleExerciseClick(1)
    })

    // Should not start workout again
    expect(mockProps.startWorkout).not.toHaveBeenCalled()
    // But should still navigate with exercise name
    expect(mockPush).toHaveBeenCalledWith(
      '/workout/exercise/1?exerciseName=Bench%20Press'
    )
  })

  it('should handle empty exercise label gracefully', async () => {
    const propsWithEmptyLabel = {
      ...mockProps,
      exercises: [{ Id: 4, Label: '', sets: [] }],
    }

    const { result } = renderHook(() => useWorkoutActions(propsWithEmptyLabel))

    await act(async () => {
      await result.current.handleExerciseClick(4)
    })

    // Should navigate without exercise name parameter
    expect(mockPush).toHaveBeenCalledWith('/workout/exercise/4')
  })
})
