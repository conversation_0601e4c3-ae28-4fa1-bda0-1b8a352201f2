import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useWorkoutSync } from '../useWorkoutSync'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'

// Mock stores
vi.mock('@/stores/workoutStore')
vi.mock('@/stores/authStore')

describe('useWorkoutSync', () => {
  let mockSaveSetStore: ReturnType<typeof vi.fn>

  beforeEach(() => {
    vi.clearAllMocks()

    mockSaveSetStore = vi.fn()

    // Mock useWorkoutStore
    vi.mocked(useWorkoutStore).mockReturnValue({
      workoutSession: { id: 'test-session-123' },
      saveSet: mockSaveSetStore,
    } as any)

    // Mock useAuthStore
    vi.mocked(useAuthStore).mockReturnValue({
      getCachedUserInfo: () => ({ MassUnit: 'kg' }),
    } as any)
  })

  it('should mark saved sets as finished', async () => {
    // Given: A workout sync hook
    const { result } = renderHook(() => useWorkoutSync())

    // When: Saving a completed set
    await act(async () => {
      await result.current.saveSet({
        exerciseId: 1,
        weight: 80,
        reps: 10,
        isWarmup: false,
        setNumber: 1,
      })
    })

    // Then: The set should be marked as IsFinished: true
    expect(mockSaveSetStore).toHaveBeenCalledWith(
      expect.objectContaining({
        ExerciseId: 1,
        Weight: { Kg: 80, Lb: 80 * 2.20462 },
        Reps: 10,
        IsWarmups: false,
        IsNext: false,
        IsFinished: true, // This is the key assertion
      })
    )
  })

  it('should mark warmup sets as finished when saved', async () => {
    // Given: A workout sync hook
    const { result } = renderHook(() => useWorkoutSync())

    // When: Saving a warmup set
    await act(async () => {
      await result.current.saveSet({
        exerciseId: 1,
        weight: 40,
        reps: 5,
        isWarmup: true,
        setNumber: 1,
      })
    })

    // Then: The warmup set should be marked as IsFinished: true
    expect(mockSaveSetStore).toHaveBeenCalledWith(
      expect.objectContaining({
        IsWarmups: true,
        IsFinished: true,
      })
    )
  })

  it('should handle imperial units correctly', async () => {
    // Given: User prefers imperial units
    vi.mocked(useAuthStore).mockReturnValue({
      getCachedUserInfo: () => ({ MassUnit: 'lbs' }),
    } as any)

    const { result } = renderHook(() => useWorkoutSync())

    // When: Saving a set with weight in lbs
    await act(async () => {
      await result.current.saveSet({
        exerciseId: 1,
        weight: 175,
        reps: 10,
        isWarmup: false,
      })
    })

    // Then: Weight should be converted correctly and set marked as finished
    expect(mockSaveSetStore).toHaveBeenCalledWith(
      expect.objectContaining({
        Weight: { Lb: 175, Kg: 175 * 0.453592 },
        IsFinished: true,
      })
    )
  })
})
