import { describe, it, expect, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useWorkoutStore } from '@/stores/workoutStore'
import type {
  WorkoutTemplateModel,
  ExerciseModel,
  WorkoutLogSerieModel,
} from '@/types'

// Mock data
const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  TargetReps: 8,
  TargetWeight: { Mass: 100, MassUnit: 'lbs' },
  Path: 'chest/benchpress',
  IsWarmup: false,
  IsMedium: false,
  IsBodyweight: false,
  IsNormalSet: true,
  IsDeload: false,
  IsFinished: false,
  IsMediumFinished: false,
  IsEasy: false,
  IsFirstMedium: false,
  IsFirstEasy: false,
  BodyWeight: null,
  EquipmentId: 1,
  TimeSinceLastSet: '00:00:00',
  PreviousExercise: null,
  SwapExerciseTargetPath: null,
  RecommendationInKgRange: null,
  FirstWorkSet: null,
  From1RM: null,
  OneRM: null,
  IsMultiUnity: false,
  IsRecommended: true,
  HasPastLogs: true,
  IsTimeBased: false,
  IsPlate: false,
}

const mockWorkout: WorkoutTemplateModel = {
  Id: 1,
  UserId: 'test-user',
  Label: 'Push Day',
  Exercises: [mockExercise],
  IsSystemExercise: false,
  WorkoutSettingsModel: {
    Id: 1,
    Pause: 120,
    Equipment: '',
    ChildWorkoutTemplateId: null,
    SetsModel: null,
    WorkoutProgramId: 1,
    IsFirstSet: false,
    IsFail: false,
    NbRepsMinimalInc: null,
    AvgDuration: null,
    IsNotRealData: false,
  },
}

// const mockWorkoutGroup: WorkoutTemplateGroupModel = {
//   Name: 'Week 1',
//   Workouts: [mockWorkout],
//   Id: 1,
// }

describe('Workout Store', () => {
  beforeEach(() => {
    // Reset store state
    useWorkoutStore.setState({
      currentWorkout: null,
      exercises: [],
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      workoutSession: null,
      isLoading: false,
      error: null,
    })
  })

  describe('Initial State', () => {
    it('should initialize with default values', () => {
      const { result } = renderHook(() => useWorkoutStore())

      expect(result.current.currentWorkout).toBeNull()
      expect(result.current.exercises).toEqual([])
      expect(result.current.currentExerciseIndex).toBe(0)
      expect(result.current.currentSetIndex).toBe(0)
      expect(result.current.workoutSession).toBeNull()
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()
    })
  })

  describe('Workout Loading', () => {
    it('should set workout data when loaded', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setWorkout(mockWorkout)
      })

      expect(result.current.currentWorkout).toEqual(mockWorkout)
      expect(result.current.exercises).toEqual(mockWorkout.Exercises)
      expect(result.current.currentExerciseIndex).toBe(0)
      expect(result.current.currentSetIndex).toBe(0)
    })

    it('should handle empty workout', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const emptyWorkout: WorkoutTemplateModel = {
        ...mockWorkout,
        Exercises: [],
      }

      act(() => {
        result.current.setWorkout(emptyWorkout)
      })

      expect(result.current.currentWorkout).toEqual(emptyWorkout)
      expect(result.current.exercises).toEqual([])
    })

    it('should set loading state', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setLoading(true)
      })

      expect(result.current.isLoading).toBe(true)

      act(() => {
        result.current.setLoading(false)
      })

      expect(result.current.isLoading).toBe(false)
    })

    it('should set error state', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const errorMessage = 'Failed to load workout'

      act(() => {
        result.current.setError(errorMessage)
      })

      expect(result.current.error).toBe(errorMessage)
      expect(result.current.isLoading).toBe(false)
    })
  })

  describe('Workout Session Management', () => {
    it('should start a workout session', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setWorkout(mockWorkout)
        result.current.startWorkout()
      })

      expect(result.current.workoutSession).not.toBeNull()
      expect(result.current.workoutSession?.id).toBeDefined()
      expect(result.current.workoutSession?.startTime).toBeInstanceOf(Date)
      expect(result.current.workoutSession?.exercises).toEqual([])
    })

    it('should not start workout without workout data', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.startWorkout()
      })

      expect(result.current.workoutSession).toBeNull()
    })
  })

  describe('Exercise Progression', () => {
    beforeEach(() => {
      const multiExerciseWorkout: WorkoutTemplateModel = {
        ...mockWorkout,
        Exercises: [
          mockExercise,
          { ...mockExercise, Id: 2, Label: 'Shoulder Press' },
          { ...mockExercise, Id: 3, Label: 'Tricep Extension' },
        ],
      }
      useWorkoutStore.setState({
        currentWorkout: multiExerciseWorkout,
        exercises: multiExerciseWorkout.Exercises,
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
      })
    })

    it('should progress to next exercise', () => {
      const { result } = renderHook(() => useWorkoutStore())

      expect(result.current.currentExerciseIndex).toBe(0)

      act(() => {
        result.current.nextExercise()
      })

      expect(result.current.currentExerciseIndex).toBe(1)
      expect(result.current.currentSetIndex).toBe(0)
    })

    it('should not progress beyond last exercise', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Move to last exercise
      act(() => {
        result.current.nextExercise()
        result.current.nextExercise()
      })

      expect(result.current.currentExerciseIndex).toBe(2)

      // Try to go beyond
      act(() => {
        result.current.nextExercise()
      })

      // Should stay at last exercise
      expect(result.current.currentExerciseIndex).toBe(2)
    })

    it('should get current exercise correctly', () => {
      const { result } = renderHook(() => useWorkoutStore())

      const currentExercise = result.current.getCurrentExercise()
      expect(currentExercise?.Label).toBe('Bench Press')

      act(() => {
        result.current.nextExercise()
      })

      const nextExercise = result.current.getCurrentExercise()
      expect(nextExercise?.Label).toBe('Shoulder Press')
    })
  })

  describe('Set Progression', () => {
    beforeEach(() => {
      useWorkoutStore.setState({
        currentWorkout: mockWorkout,
        exercises: mockWorkout.Exercises,
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
      })
    })

    it('should progress to next set', () => {
      const { result } = renderHook(() => useWorkoutStore())

      expect(result.current.currentSetIndex).toBe(0)

      act(() => {
        result.current.nextSet()
      })

      expect(result.current.currentSetIndex).toBe(1)
    })

    it('should save set data', () => {
      const { result } = renderHook(() => useWorkoutStore())

      const setData: WorkoutLogSerieModel = {
        Id: 0,
        ExerciseId: mockExercise.Id,
        Reps: 8,
        Weight: { Lb: 100, Kg: 45.36 },
        RIR: 2,
        IsWarmups: false,
        IsNext: false,
        IsFinished: false,
      }

      act(() => {
        result.current.saveSet(setData)
      })

      const session = result.current.workoutSession
      expect(session?.exercises).toHaveLength(1)
      if (session?.exercises[0]) {
        expect(session.exercises[0].exerciseId).toBe(mockExercise.Id)
        expect(session.exercises[0].sets).toHaveLength(1)
        if (session.exercises[0].sets[0]) {
          expect(session.exercises[0].sets[0]).toMatchObject({
            reps: setData.Reps,
            weight: setData.Weight,
            rir: setData.RIR,
          })
        }
      }
    })

    it('should update current set data', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.updateCurrentSet({ reps: 10, weight: 105 })
      })

      const currentSet = result.current.getCurrentSet()
      expect(currentSet).toEqual({ reps: 10, weight: 105 })
    })
  })

  describe('Workout Completion', () => {
    beforeEach(() => {
      const completedSession = {
        id: 'session-1',
        startTime: new Date(Date.now() - 3600000), // 1 hour ago
        exercises: [
          {
            exerciseId: 1,
            name: 'Exercise',
            sets: [
              {
                setNumber: 1,
                reps: 8,
                weight: { Lb: 100, Kg: 45.36 },
                rir: 2,
                isWarmup: false,
                timestamp: new Date(),
              },
            ],
          },
        ],
      }

      useWorkoutStore.setState({
        currentWorkout: mockWorkout,
        exercises: mockWorkout.Exercises,
        workoutSession: completedSession,
        currentExerciseIndex: 0,
        currentSetIndex: 1,
      })
    })

    it('should complete workout', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.completeWorkout()
      })

      expect(result.current.workoutSession?.endTime).toBeInstanceOf(Date)
      expect(result.current.isWorkoutComplete()).toBe(true)
    })

    it('should calculate workout duration', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.completeWorkout()
      })

      const duration = result.current.getWorkoutDuration()
      expect(duration).toBeGreaterThan(0)
      expect(duration).toBeLessThanOrEqual(3610000) // ~1 hour in ms with some buffer
    })

    it('should reset workout after completion', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.completeWorkout()
        result.current.resetWorkout()
      })

      expect(result.current.currentWorkout).toBeNull()
      expect(result.current.exercises).toEqual([])
      expect(result.current.currentExerciseIndex).toBe(0)
      expect(result.current.currentSetIndex).toBe(0)
      expect(result.current.workoutSession).toBeNull()
    })
  })

  describe('Status Indicators', () => {
    it('should update exercise status to in progress after saving first set', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Start workout with exercises
      act(() => {
        result.current.setWorkout(mockWorkout)
        result.current.startWorkout()
      })

      const setData: WorkoutLogSerieModel = {
        Id: 0,
        ExerciseId: mockExercise.Id,
        Reps: 10,
        Weight: { Mass: 100, MassUnit: 'lbs' },
        IsWarmup: false,
        TimeStarted: new Date(),
        TimeCompleted: new Date(),
      }

      act(() => {
        result.current.saveSet(setData)
      })

      // Exercise should now be marked as in progress
      const updatedExercise = result.current.exercises.find(
        (e) => e.Id === mockExercise.Id
      )
      expect(updatedExercise?.IsInProgress).toBe(true)
      expect(updatedExercise?.IsFinished).toBe(false)
    })

    it('should update exercise status to finished when all recommended sets are completed', () => {
      const { result } = renderHook(() => useWorkoutStore())

      const exerciseWith3Sets = {
        ...mockExercise,
        RecommendedSets: 3,
      }

      const workoutWith3Sets = {
        ...mockWorkout,
        Exercises: [exerciseWith3Sets],
      }

      act(() => {
        result.current.setWorkout(workoutWith3Sets)
        result.current.startWorkout()
      })

      // Save 3 sets
      for (let i = 0; i < 3; i++) {
        const setData: WorkoutLogSerieModel = {
          Id: i,
          ExerciseId: mockExercise.Id,
          Reps: 10 - i,
          Weight: { Mass: 100 - i * 5, MassUnit: 'lbs' },
          IsWarmup: false,
          TimeStarted: new Date(),
          TimeCompleted: new Date(),
        }

        act(() => {
          result.current.saveSet(setData)
        })
      }

      // Exercise should now be marked as finished
      const updatedExercise = result.current.exercises.find(
        (e) => e.Id === mockExercise.Id
      )
      expect(updatedExercise?.IsFinished).toBe(true)
      expect(updatedExercise?.IsInProgress).toBe(false)
    })

    it('should populate exercise with set data for UI display', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setWorkout(mockWorkout)
        result.current.startWorkout()
      })

      const setData: WorkoutLogSerieModel = {
        Id: 0,
        ExerciseId: mockExercise.Id,
        Reps: 12,
        Weight: { Mass: 90, MassUnit: 'lbs' },
        IsWarmup: false,
        TimeStarted: new Date(),
        TimeCompleted: new Date(),
      }

      act(() => {
        result.current.saveSet(setData)
      })

      // Exercise should have sets array populated
      const updatedExercise = result.current.exercises.find(
        (e) => e.Id === mockExercise.Id
      )
      expect(updatedExercise?.sets).toBeDefined()
      expect(updatedExercise?.sets).toHaveLength(1)
      expect(updatedExercise?.sets?.[0]).toMatchObject({
        reps: 12,
        weight: { Mass: 90, MassUnit: 'lbs' },
      })
    })
  })

  describe('Edge Cases', () => {
    it('should handle saving set without active session', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Clear workout session
      act(() => {
        result.current.resetWorkout()
      })

      const setData: WorkoutLogSerieModel = {
        Id: 0,
        ExerciseId: 1,
        Reps: 8,
        Weight: { Lb: 100, Kg: 45.36 },
        RIR: 2,
        IsWarmups: false,
        IsNext: false,
        IsFinished: false,
      }

      // Should not throw error
      expect(() => {
        act(() => {
          result.current.saveSet(setData)
        })
      }).not.toThrow()
    })

    it('should handle concurrent updates', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setWorkout(mockWorkout)
        result.current.startWorkout()
        result.current.setError('Test error')
        result.current.setLoading(true) // Set loading after error to test it can be set independently
      })

      expect(result.current.currentWorkout).toEqual(mockWorkout)
      expect(result.current.workoutSession).not.toBeNull()
      expect(result.current.isLoading).toBe(true)
      expect(result.current.error).toBe('Test error')
    })

    it('should preserve exercises array when setWorkout called with same workout ID', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Set initial workout
      act(() => {
        result.current.setWorkout(mockWorkout)
        result.current.startWorkout()
      })

      const initialExercises = result.current.exercises

      // Call setWorkout again with the same workout
      act(() => {
        result.current.setWorkout(mockWorkout)
      })

      // Exercises array should be preserved
      expect(result.current.exercises).toEqual(initialExercises)
      expect(result.current.exercises.length).toBe(1)
      expect(result.current.exercises[0].Id).toBe(mockExercise.Id)
    })

    it('should not reset exercises during recommendation loading', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Set workout and start session
      act(() => {
        result.current.setWorkout(mockWorkout)
        result.current.startWorkout()
      })

      const exercisesBeforeLoad = [...result.current.exercises]

      // Simulate recommendation loading
      act(() => {
        // This simulates what happens during loadAllExerciseRecommendations
        result.current.loadingStates.set(1, true)
      })

      // Exercises should remain unchanged
      expect(result.current.exercises).toEqual(exercisesBeforeLoad)

      // Simulate re-render that might call setWorkout again
      act(() => {
        result.current.setWorkout(mockWorkout)
      })

      // Exercises should still be preserved
      expect(result.current.exercises).toEqual(exercisesBeforeLoad)
      expect(result.current.exercises.length).toBe(1)
    })

    it('should handle race condition when workout is set without exercises during loading', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Create a workout with exercises
      const workoutWithExercises: WorkoutTemplateModel = {
        ...mockWorkout,
        Exercises: [mockExercise],
      }

      // Set initial workout with exercises
      act(() => {
        result.current.setWorkout(workoutWithExercises)
        result.current.startWorkout()
      })

      expect(result.current.exercises.length).toBe(1)
      const initialExerciseId = result.current.exercises[0].Id

      // Simulate a scenario where workout is set again but Exercises might be empty/undefined
      // This can happen if the workout object is recreated without exercises during async loading
      const workoutWithoutExercises: WorkoutTemplateModel = {
        ...mockWorkout,
        Exercises: [], // Empty array simulating incomplete data
      }

      act(() => {
        result.current.setWorkout(workoutWithoutExercises)
      })

      // Exercises should be preserved when workout ID is the same
      // even if the new workout object doesn't have exercises populated
      expect(result.current.exercises.length).toBe(1)
      expect(result.current.exercises[0].Id).toBe(initialExerciseId)
    })
  })
})
