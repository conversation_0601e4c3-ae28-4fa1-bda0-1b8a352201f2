/**
 * Test race condition scenarios for useExercisePageInitialization
 * Specifically testing the "Exercise Not Available" error that occurs when
 * exercise validation happens before exercises array is loaded
 */

import { renderHook, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useExercisePageInitialization } from '../useExercisePageInitialization'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useRouter } from 'next/navigation'

// Mock the dependencies
vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: vi.fn(),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(),
}))

vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(vi.fn(), {
    log: vi.fn(),
    error: vi.fn(),
  }),
}))

describe('useExercisePageInitialization - Race Condition Prevention', () => {
  const mockRouter = {
    replace: vi.fn(),
  }

  const mockWorkoutStore = {
    setCurrentExerciseById: vi.fn(),
    loadingStates: new Map(),
    getCachedExerciseRecommendation: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue(mockRouter)
    ;(useWorkoutStore as any).mockReturnValue(mockWorkoutStore)
  })

  it('should prevent "Exercise Not Available" error when exercises array is empty', async () => {
    // Mock useWorkout to return empty exercises array (race condition scenario)
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: [], // Empty array - this is the race condition
      workoutSession: { id: 1 },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Wait for the hook to complete initialization
    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should NOT have an error - the guard clause should prevent validation
    expect(result.current.loadingError).toBeNull()

    // Should NOT attempt to set current exercise when exercises are empty
    expect(mockWorkoutStore.setCurrentExerciseById).not.toHaveBeenCalled()
  })

  it('should prevent "Exercise Not Available" error when exercises is null', async () => {
    // Mock useWorkout to return null exercises (race condition scenario)
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: null, // Null - this is another race condition scenario
      workoutSession: { id: 1 },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Wait for the hook to complete initialization
    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should NOT have an error - the guard clause should prevent validation
    expect(result.current.loadingError).toBeNull()

    // Should NOT attempt to set current exercise when exercises are null
    expect(mockWorkoutStore.setCurrentExerciseById).not.toHaveBeenCalled()
  })

  it('should show "Exercise Not Available" error when exercise truly does not exist in workout', async () => {
    // Mock useWorkout to return exercises array WITHOUT the requested exercise
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: [
        { Id: 456, Label: 'Different Exercise' }, // Exercise 123 is NOT in the workout
      ],
      workoutSession: { id: 1 },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Wait for the hook to process
    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should have an error since exercise 123 is not in the workout
    expect(result.current.loadingError).toBeTruthy()
    expect(result.current.loadingError?.message).toContain(
      'Exercise 123 not found in workout'
    )
  })

  it('should successfully initialize when exercise exists in workout', async () => {
    // Mock useWorkout to return exercises array WITH the requested exercise
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: [
        { Id: 123, Label: 'Target Exercise', sets: [] }, // Exercise 123 IS in the workout
      ],
      workoutSession: { id: 1 },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    mockWorkoutStore.getCachedExerciseRecommendation.mockReturnValue(null)

    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Wait for the hook to complete initialization
    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should NOT have an error
    expect(result.current.loadingError).toBeNull()

    // Should set the current exercise
    expect(mockWorkoutStore.setCurrentExerciseById).toHaveBeenCalledWith(123)
  })

  it('should prevent validation when workout is still loading', async () => {
    // Mock useWorkout to return loading state
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
            },
          ],
        },
      ],
      isLoadingWorkout: true, // Still loading
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: [{ Id: 123, Label: 'Target Exercise' }], // Exercises exist but still loading
      workoutSession: { id: 1 },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Wait for the hook to complete initialization
    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should NOT have an error - should wait for loading to complete
    expect(result.current.loadingError).toBeNull()

    // Should NOT attempt to set current exercise while still loading
    expect(mockWorkoutStore.setCurrentExerciseById).not.toHaveBeenCalled()
  })

  it('should handle race condition when navigating from login while workout is being set', async () => {
    // Simulate the exact scenario from login:
    // 1. workoutSession exists (just created)
    // 2. currentWorkout exists
    // 3. But exercises array hasn't been populated yet
    const mockUseWorkout = vi.fn()
    ;(useWorkout as any).mockImplementation(mockUseWorkout)

    // Initial state - workout being set but exercises not populated yet
    mockUseWorkout.mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
              Exercises: [{ Id: 123, Label: 'Target Exercise' }],
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: [], // Empty initially - this is the race condition!
      workoutSession: { id: 'session-123' },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    const { result, rerender } = renderHook(() =>
      useExercisePageInitialization(123)
    )

    // Initial render - should not error with empty exercises
    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    expect(result.current.loadingError).toBeNull()
    expect(mockWorkoutStore.setCurrentExerciseById).not.toHaveBeenCalled()

    // Now simulate exercises being populated (what happens after setWorkout completes)
    mockUseWorkout.mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
              Exercises: [{ Id: 123, Label: 'Target Exercise' }],
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: [{ Id: 123, Label: 'Target Exercise', sets: [] }], // Now populated
      workoutSession: { id: 'session-123' },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    // Force re-render to simulate the store update
    rerender()

    // After exercises are populated, should successfully set current exercise
    await waitFor(() => {
      expect(mockWorkoutStore.setCurrentExerciseById).toHaveBeenCalledWith(123)
    })

    // Should still have no error
    expect(result.current.loadingError).toBeNull()
  })

  it('should handle the specific login to exercise navigation scenario', async () => {
    // This tests the exact scenario where:
    // 1. User logs in and workout data is being prefetched
    // 2. setWorkout is called from useLoginPrefetch
    // 3. User navigates to exercise page before setWorkout completes

    const mockUseWorkout = vi.fn()
    ;(useWorkout as any).mockImplementation(mockUseWorkout)

    // Simulate the state right after login when setWorkout has been called
    // but hasn't finished populating the exercises array yet
    mockUseWorkout.mockReturnValue({
      todaysWorkout: null, // Not loaded from cache yet
      isLoadingWorkout: false, // Not loading because we have a workoutSession
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: undefined, // This might be undefined instead of empty array
      workoutSession: { id: 'session-from-login' }, // Session exists from login
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    const { result } = renderHook(() => useExercisePageInitialization(123))

    // Should handle undefined exercises gracefully
    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should not error when exercises is undefined
    expect(result.current.loadingError).toBeNull()
    expect(mockWorkoutStore.setCurrentExerciseById).not.toHaveBeenCalled()
  })

  it('should validate when workout currentWorkout exists but exercises is being populated', async () => {
    // Another edge case - currentWorkout is set but exercises array is being populated

    ;(useWorkoutStore as any).mockReturnValue({
      ...mockWorkoutStore,
      currentWorkout: {
        Id: 1,
        Exercises: [{ Id: 123, Label: 'Target Exercise' }],
      },
      exercises: [], // Being populated
    })
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: [
        {
          WorkoutTemplates: [
            {
              Id: 1,
              Label: 'Test Workout',
              Exercises: [{ Id: 123, Label: 'Target Exercise' }],
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      startWorkout: vi.fn().mockResolvedValue({ success: true }),
      exercises: [], // Empty during population
      workoutSession: { id: 'session-123' },
      loadRecommendation: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    })

    const { result } = renderHook(() => useExercisePageInitialization(123))

    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should not error even with this edge case
    expect(result.current.loadingError).toBeNull()
  })
})
