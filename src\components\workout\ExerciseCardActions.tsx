'use client'

import React from 'react'
import type { ExerciseModel } from '@/types'
import { ExerciseOverflowMenu } from './ExerciseOverflowMenu'

interface ExerciseCardActionsProps {
  exercise: ExerciseModel
  isExpanded: boolean
  hasSets: boolean
  onSwapClick: (e: React.MouseEvent) => void
  onToggleExpand: (e: React.MouseEvent) => void
  onSkipExercise: (exerciseId: number) => void
  onViewHistory: (exerciseId: number) => void
  onAddNote: (exerciseId: number) => void
}

export function ExerciseCardActions({
  exercise,
  isExpanded,
  hasSets,
  onSwapClick,
  onToggleExpand,
  onSkipExercise,
  onViewHistory,
  onAddNote,
}: ExerciseCardActionsProps) {
  return (
    <div className="flex items-center gap-1">
      {/* Overflow Menu */}
      <ExerciseOverflowMenu
        exercise={exercise}
        onSkip={onSkipExercise}
        onViewHistory={onViewHistory}
        onAddNote={onAddNote}
      />

      {/* Swap Button */}
      <button
        onClick={onSwapClick}
        className="p-2 text-text-tertiary hover:text-brand-primary transition-colors"
        aria-label="Swap exercise"
        title="Swap for another exercise"
      >
        <svg
          className="h-4 w-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
          />
        </svg>
      </button>

      {/* Expand/Collapse Button */}
      {hasSets && (
        <button
          onClick={onToggleExpand}
          className="p-2 text-text-tertiary hover:text-text-secondary transition-colors"
          aria-label={isExpanded ? 'Collapse sets' : 'Expand sets'}
        >
          <svg
            className={`h-5 w-5 transform transition-transform ${
              isExpanded ? 'rotate-180' : ''
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>
      )}
    </div>
  )
}
