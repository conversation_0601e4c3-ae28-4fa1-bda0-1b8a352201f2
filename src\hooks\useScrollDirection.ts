import { useState, useEffect, useRef, useCallback } from 'react'

interface UseScrollDirectionOptions {
  threshold?: number
  debounceMs?: number
}

interface ScrollDirectionState {
  isVisible: boolean
  scrollDirection: 'up' | 'down'
}

export function useScrollDirection(
  options: UseScrollDirectionOptions = {}
): ScrollDirectionState {
  const { threshold = 50, debounceMs = 10 } = options

  const [state, setState] = useState<ScrollDirectionState>({
    isVisible: true,
    scrollDirection: 'up',
  })

  const lastScrollY = useRef(0)
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null)

  const handleScroll = useCallback(() => {
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current)
    }

    debounceTimeout.current = setTimeout(() => {
      const currentScrollY = window.scrollY

      // Always show when near top of page
      if (currentScrollY < threshold) {
        setState({
          isVisible: true,
          scrollDirection: 'up',
        })
        lastScrollY.current = currentScrollY
        return
      }

      // Determine scroll direction
      const scrollingDown = currentScrollY > lastScrollY.current
      const scrollingUp = currentScrollY < lastScrollY.current

      // Only update if there's actual movement
      if (scrollingDown || scrollingUp) {
        setState({
          isVisible: scrollingUp,
          scrollDirection: scrollingDown ? 'down' : 'up',
        })
      }

      lastScrollY.current = currentScrollY
    }, debounceMs)
  }, [threshold, debounceMs])

  useEffect(() => {
    // Set initial scroll position
    lastScrollY.current = window.scrollY

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true })

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll)
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current)
      }
    }
  }, [handleScroll])

  return state
}
