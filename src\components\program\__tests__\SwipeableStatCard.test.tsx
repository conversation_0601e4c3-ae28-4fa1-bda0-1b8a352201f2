import { render, screen } from '@testing-library/react'
import { userEvent } from '@testing-library/user-event'
import { vi } from 'vitest'
import { SwipeableStatCard } from '../SwipeableStatCard'
import type { UserStats } from '@/types'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, drag, onDragEnd, animate, ...props }: any) => (
      <div {...props}>{children}</div>
    ),
  },
  useAnimation: () => ({
    start: vi.fn(),
    set: vi.fn(),
  }),
}))

// Mock haptic utils
vi.mock('@/utils/haptic', () => ({
  useHaptic: () => ({
    trigger: vi.fn(),
    withHandler: (fn: () => void) => fn,
  }),
}))

describe('SwipeableStatCard', () => {
  const mockStats: UserStats = {
    weekStreak: 5,
    workoutsCompleted: 42,
    lbsLifted: 12500,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('shows first stat (week streak) in focused view initially', () => {
    render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

    // Should show week streak value
    expect(screen.getByText('5')).toBeInTheDocument()
    expect(screen.getByText('Week Streak')).toBeInTheDocument()

    // Should not show other stats initially
    expect(screen.queryByText('42')).not.toBeInTheDocument()
    expect(screen.queryByText('12500')).not.toBeInTheDocument()
  })

  it('shows loading state when isLoading is true', () => {
    render(<SwipeableStatCard stats={null} isLoading />)

    // Should show animated counter starting at 0
    expect(screen.getByText('0')).toBeInTheDocument()
    expect(screen.getByText('Week Streak')).toBeInTheDocument()

    // Should show loading message
    expect(screen.getByText('Loading your workout...')).toBeInTheDocument()
  })

  it('shows stat indicators for navigation', () => {
    render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

    // Should show 3 dots for 3 stats
    const indicators = screen.getAllByTestId('stat-indicator')
    expect(indicators).toHaveLength(3)

    // First indicator should be active
    expect(indicators[0]).toHaveClass('bg-brand-primary')
    expect(indicators[1]).toHaveClass('bg-bg-tertiary')
    expect(indicators[2]).toHaveClass('bg-bg-tertiary')
  })

  it('shows next stat when swiping left', async () => {
    const user = userEvent.setup()
    render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

    // Simulate swipe left by clicking next indicator
    const indicators = screen.getAllByTestId('stat-indicator')
    await user.click(indicators[1])

    // Should now show workouts completed
    expect(screen.getByText('42')).toBeInTheDocument()
    expect(screen.getByText('Workouts')).toBeInTheDocument()

    // Should not show other stats
    expect(screen.queryByText('5')).not.toBeInTheDocument()
    expect(screen.queryByText('12500')).not.toBeInTheDocument()
  })

  it('cycles through all stats and back to first', async () => {
    const user = userEvent.setup()
    render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

    const indicators = screen.getAllByTestId('stat-indicator')

    // Go to second stat
    await user.click(indicators[1])
    expect(screen.getByText('42')).toBeInTheDocument()

    // Go to third stat
    await user.click(indicators[2])
    expect(screen.getByText('12,500')).toBeInTheDocument() // Formatted number
    expect(screen.getByText('Lbs Lifted')).toBeInTheDocument()

    // Go back to first stat
    await user.click(indicators[0])
    expect(screen.getByText('5')).toBeInTheDocument()
    expect(screen.getByText('Week Streak')).toBeInTheDocument()
  })

  it('handles null stats gracefully', () => {
    render(<SwipeableStatCard stats={null} isLoading={false} />)

    // Should show zero values
    expect(screen.getByText('0')).toBeInTheDocument()
    expect(screen.getByText('Week Streak')).toBeInTheDocument()
  })

  it('handles undefined values in stats', () => {
    const partialStats: Partial<UserStats> = {
      weekStreak: 5,
      // workoutsCompleted and lbsLifted are undefined
    }
    render(
      <SwipeableStatCard stats={partialStats as UserStats} isLoading={false} />
    )

    // Should show first stat normally
    expect(screen.getByText('5')).toBeInTheDocument()
  })

  it('shows swipe hint text', () => {
    render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

    expect(screen.getByText('Swipe to view more')).toBeInTheDocument()
  })

  it('applies V2 styling with no hover effects', () => {
    render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

    const card = screen.getByTestId('swipeable-stat-card')

    // Should have V2 styling
    expect(card).toHaveClass('bg-surface-secondary')
    expect(card).toHaveClass('rounded-2xl')
    expect(card).toHaveClass('shadow-lg')

    // Should NOT have hover effects
    expect(card).not.toHaveClass('hover:scale-[1.02]')
    expect(card).not.toHaveClass('hover:shadow-theme-lg')
  })

  describe('Counter Animation', () => {
    it('shows counter display when loading', () => {
      render(<SwipeableStatCard stats={null} isLoading />)

      // Should show initial value of 0
      expect(screen.getByText('0')).toBeInTheDocument()
      expect(screen.getByText('Week Streak')).toBeInTheDocument()

      // Should show loading message
      expect(screen.getByText('Loading your workout...')).toBeInTheDocument()
    })

    it('uses animated values when loading', () => {
      // This test verifies the component structure is correct for animation
      // Actual animation behavior is tested via E2E tests due to RAF limitations in unit tests
      render(<SwipeableStatCard stats={null} isLoading />)

      // Should show initial value
      expect(screen.getByTestId('stat-value')).toHaveTextContent('0')

      // Should have loading message
      expect(screen.getByText('Loading your workout...')).toBeInTheDocument()

      // Should have correct stat label
      expect(screen.getByText('Week Streak')).toBeInTheDocument()
    })

    it('transitions to real value when data arrives', () => {
      const { rerender } = render(<SwipeableStatCard stats={null} isLoading />)

      // Should start with 0
      expect(screen.getByText('0')).toBeInTheDocument()

      // Provide real data
      rerender(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      // Should immediately show real value
      expect(screen.getByText('5')).toBeInTheDocument()

      // Loading message should disappear
      expect(
        screen.queryByText('Loading your workout...')
      ).not.toBeInTheDocument()
    })

    it('respects reduced motion preference', () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation((query) => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      })

      render(<SwipeableStatCard stats={null} isLoading />)

      // Should show 0 immediately without animation
      expect(screen.getByText('0')).toBeInTheDocument()
    })
  })

  describe('Loading Message', () => {
    it('shows loading message below card when loading', () => {
      render(<SwipeableStatCard stats={null} isLoading />)

      expect(screen.getByText('Loading your workout...')).toBeInTheDocument()
    })

    it('hides loading message when data is loaded', () => {
      render(<SwipeableStatCard stats={mockStats} isLoading={false} />)

      expect(
        screen.queryByText('Loading your workout...')
      ).not.toBeInTheDocument()
    })
  })
})
