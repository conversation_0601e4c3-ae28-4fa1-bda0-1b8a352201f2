import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { RepsInput } from '../inputs/RepsInput'
import { WeightInput } from '../inputs/WeightInput'

/**
 * Test suite for PR #334 premium font size implementation in reps/weights inputs
 */
describe('Premium Font Sizes for Reps and Weight Inputs', () => {
  describe('RepsInput premium typography', () => {
    const mockProps = {
      reps: 8,
      onChange: vi.fn(),
      onIncrement: vi.fn(),
      onDecrement: vi.fn(),
    }

    it('should display reps value with text-8xl font size (96px)', () => {
      render(<RepsInput {...mockProps} />)

      const input = screen.getByRole('spinbutton', { name: /reps/i })

      // Test will fail initially - need to implement text-8xl for main value
      expect(input).toHaveClass('text-8xl')
      expect(input).toHaveClass('font-bold')
    })

    it('should maintain premium minimal layout with larger fonts', () => {
      render(<RepsInput {...mockProps} />)

      const input = screen.getByRole('spinbutton', { name: /reps/i })

      // Should center the value display
      expect(input).toHaveClass('text-center')

      // Should have proper spacing for 96px font
      expect(input).toHaveClass('py-8') // Increased padding for larger font
    })

    it('should display label with text-2xl and 60% opacity', () => {
      render(<RepsInput {...mockProps} />)

      const label = screen.getByText('Reps')

      // Premium minimal guidelines: 24px label with 60% opacity
      expect(label).toHaveClass('text-2xl')
      expect(label).toHaveClass('text-white/60')
    })

    it('should apply transition animations for value changes', () => {
      render(<RepsInput {...mockProps} />)

      const input = screen.getByRole('spinbutton', { name: /reps/i })

      // Premium minimal guidelines: 300ms ease-out transitions
      expect(input).toHaveClass('transition-all')
      expect(input).toHaveClass('duration-300')
      expect(input).toHaveClass('ease-out')
    })
  })

  describe('WeightInput premium typography', () => {
    const mockProps = {
      weight: 185,
      unit: 'lbs' as const,
      onChange: vi.fn(),
      onIncrement: vi.fn(),
      onDecrement: vi.fn(),
    }

    it('should display weight value with text-8xl font size (96px)', () => {
      render(<WeightInput {...mockProps} />)

      const input = screen.getByRole('spinbutton', { name: /weight/i })

      // Test will fail initially - need to implement text-8xl for main value
      expect(input).toHaveClass('text-8xl')
      expect(input).toHaveClass('font-bold')
    })

    it('should display unit label with text-2xl and 60% opacity', () => {
      render(<WeightInput {...mockProps} />)

      const unitElement = screen.getByText('lbs')

      // Premium minimal guidelines: 24px label with 60% opacity
      expect(unitElement).toHaveClass('text-2xl')
      expect(unitElement).toHaveClass('text-white/60')
      // Unit is now in a centered container below the input
      expect(unitElement.parentElement).toHaveClass('mt-2')
    })

    it('should handle kg units with premium styling', () => {
      render(<WeightInput {...mockProps} weight={84} unit="kg" />)

      const unitElement = screen.getByText('kg')

      expect(unitElement).toHaveClass('text-2xl')
      expect(unitElement).toHaveClass('text-white/60')
    })

    it('should maintain center alignment for premium layout', () => {
      render(<WeightInput {...mockProps} />)

      const input = screen.getByRole('spinbutton', { name: /weight/i })

      // Should center the value display
      expect(input).toHaveClass('text-center')

      // Should have proper spacing for 96px font
      expect(input).toHaveClass('py-8') // Increased padding for larger font
    })
  })

  describe('Progress ring style consistency', () => {
    it('should match premium minimal progress ring value styling', () => {
      const mockRepsProps = {
        reps: 8,
        onChange: vi.fn(),
        onIncrement: vi.fn(),
        onDecrement: vi.fn(),
      }

      render(<RepsInput {...mockRepsProps} />)

      const input = screen.getByRole('spinbutton', { name: /reps/i })

      // Should match progress ring specifications:
      // Main Value: text-8xl font-bold text-white (96px)
      expect(input).toHaveClass('text-8xl')
      expect(input).toHaveClass('font-bold')
      expect(input).toHaveClass('text-white')

      // Transition: transition-all duration-300 ease-out
      expect(input).toHaveClass('transition-all')
      expect(input).toHaveClass('duration-300')
      expect(input).toHaveClass('ease-out')
    })
  })

  describe('Mobile viewport compatibility', () => {
    it('should maintain touch targets with larger fonts', () => {
      const mockProps = {
        reps: 8,
        onChange: vi.fn(),
        onIncrement: vi.fn(),
        onDecrement: vi.fn(),
      }

      render(<RepsInput {...mockProps} />)

      const decrementButton = screen.getByRole('button', {
        name: /decrease reps/i,
      })
      const incrementButton = screen.getByRole('button', {
        name: /increase reps/i,
      })

      // Should maintain minimum 44px touch targets
      expect(decrementButton).toHaveClass('p-3') // 12px * 2 + content >= 44px
      expect(incrementButton).toHaveClass('p-3')
    })

    it('should adapt layout for 320-430px viewport', () => {
      const mockProps = {
        weight: 185,
        unit: 'lbs' as const,
        onChange: vi.fn(),
        onIncrement: vi.fn(),
        onDecrement: vi.fn(),
      }

      render(<WeightInput {...mockProps} />)

      const container = screen.getByRole('spinbutton', {
        name: /weight/i,
      }).parentElement

      // Should use responsive layout classes
      expect(container).toHaveClass('flex-1')
      expect(container).toHaveClass('relative')
    })
  })

  describe('Accessibility with premium fonts', () => {
    it('should maintain ARIA labels with larger text', () => {
      const mockProps = {
        reps: 8,
        onChange: vi.fn(),
        onIncrement: vi.fn(),
        onDecrement: vi.fn(),
      }

      render(<RepsInput {...mockProps} />)

      const input = screen.getByRole('spinbutton', { name: /reps/i })
      const decrementButton = screen.getByRole('button', {
        name: /decrease reps/i,
      })
      const incrementButton = screen.getByRole('button', {
        name: /increase reps/i,
      })

      // Should maintain accessibility attributes
      expect(input).toHaveAttribute('aria-label', 'Reps')
      expect(decrementButton).toHaveAttribute('aria-label', 'Decrease reps')
      expect(incrementButton).toHaveAttribute('aria-label', 'Increase reps')
    })

    it('should provide proper contrast on black background', () => {
      const mockProps = {
        weight: 185,
        unit: 'lbs' as const,
        onChange: vi.fn(),
        onIncrement: vi.fn(),
        onDecrement: vi.fn(),
      }

      render(<WeightInput {...mockProps} />)

      const input = screen.getByRole('spinbutton', { name: /weight/i })

      // Should use white text on black background for proper contrast
      expect(input).toHaveClass('text-white')
    })
  })
})
