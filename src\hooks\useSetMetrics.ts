import type { RecommendationModel } from '@/types'

interface UseSetMetricsParams {
  recommendation: RecommendationModel | null
  currentSetIndex: number
  isWarmup: boolean
  unit: 'kg' | 'lbs'
  isFirstWorkSet: boolean
  currentReps?: number
  currentWeight?: number
}

interface UseSetMetricsResult {
  lastTimeReps: number | null
  lastTimeWeight: number | null
  oneRMProgress: number | null
}

/**
 * Custom hook that extracts set metrics logic from ExplainerBox
 * Calculates "Last time" values and 1RM progress for the current set
 */
export function useSetMetrics({
  recommendation,
  currentSetIndex,
  isWarmup,
  unit,
  isFirstWorkSet,
  currentReps,
  currentWeight,
}: UseSetMetricsParams): UseSetMetricsResult {
  if (!recommendation) {
    return {
      lastTimeReps: null,
      lastTimeWeight: null,
      oneRMProgress: null,
    }
  }

  const warmupCount = recommendation.WarmupsCount || 0

  // Get the corresponding history set based on mobile app logic
  let historySet = null
  let lastTimeReps = null
  let lastTimeWeight = null

  if (isWarmup) {
    // For warmups: Direct index matching from HistorySet array
    historySet = recommendation.HistorySet?.[currentSetIndex]
  } else {
    // For work sets
    const workSetIndex = currentSetIndex - warmupCount

    if (workSetIndex === 0 && isFirstWorkSet) {
      // First work set: Use FirstWorkSetReps/FirstWorkSetWeight
      lastTimeReps = recommendation.FirstWorkSetReps
      lastTimeWeight = recommendation.FirstWorkSetWeight
    } else {
      // Subsequent work sets: Filter HistorySet to get only work sets
      const workSets =
        recommendation.HistorySet?.filter((set) => !set.IsWarmups) || []
      historySet = workSets[workSetIndex]
    }
  }

  // Extract last time values from historySet if available
  if (historySet) {
    lastTimeReps = historySet.Reps || null
    lastTimeWeight =
      unit === 'kg'
        ? historySet.Weight?.Kg || null
        : historySet.Weight?.Lb || null
  } else if (lastTimeWeight) {
    // For FirstWorkSetWeight, extract based on unit
    lastTimeWeight =
      unit === 'kg' ? lastTimeWeight.Kg || null : lastTimeWeight.Lb || null
  }

  // Calculate 1RM using Epley formula
  const calculate1RM = (weight: number, reps: number): number => {
    if (reps === 1) return weight
    return weight * (1 + reps / 30)
  }

  // Calculate 1RM progress for first work set
  let oneRMProgress = null
  if (
    isFirstWorkSet &&
    currentWeight &&
    currentReps &&
    recommendation.FirstWorkSet1RM
  ) {
    const current1RM = calculate1RM(currentWeight, currentReps)
    const previous1RM =
      unit === 'kg'
        ? recommendation.FirstWorkSet1RM.Kg
        : recommendation.FirstWorkSet1RM.Lb

    if (previous1RM && previous1RM > 0) {
      const percentageChange = ((current1RM - previous1RM) / previous1RM) * 100
      oneRMProgress = percentageChange
    }
  }

  return {
    lastTimeReps: lastTimeReps ?? null,
    lastTimeWeight: lastTimeWeight ?? null,
    oneRMProgress: oneRMProgress ?? null,
  }
}
