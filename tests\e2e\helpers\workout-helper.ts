import { Page } from '@playwright/test'

/**
 * Start a workout session by navigating to workout page and clicking start
 */
export async function startWorkout(page: Page) {
  // Navigate to workout page
  await page.goto('/workout')

  // Wait for the page to load and exercises to be visible
  await page.waitForSelector('[data-testid="exercise-card"], .exercise-card', {
    timeout: 10000,
  })

  // Click the start workout button
  const startButton = page.locator(
    '[data-testid="start-workout-button"], button:has-text("Start Workout")'
  )
  await startButton.waitFor({ state: 'visible', timeout: 10000 })
  await startButton.click()

  // Wait for workout to start (exercises should still be visible)
  await page.waitForTimeout(1000) // Give time for state to update
}

/**
 * Navigate to a specific exercise by ID
 */
export async function navigateToExercise(
  page: Page,
  exerciseId: string | number
) {
  await page.goto(`/workout/exercise/${exerciseId}`)
  await page.waitForLoadState('networkidle')
}

/**
 * Navigate to a specific exercise V2 page by ID
 */
export async function navigateToExerciseV2(
  page: Page,
  exerciseId: string | number
) {
  await page.goto(`/workout/exercise-v2/${exerciseId}`)
  await page.waitForLoadState('networkidle')
}

/**
 * Complete a set with given reps and weight
 */
export async function completeSet(page: Page, reps: number, weight: number) {
  // Fill in reps
  const repsInput = page.locator(
    'input[name="reps"], input[placeholder="Reps"]'
  )
  await repsInput.waitFor({ state: 'visible', timeout: 5000 })
  await repsInput.fill(reps.toString())

  // Fill in weight
  const weightInput = page.locator(
    'input[name="weight"], input[placeholder*="Weight"]'
  )
  await weightInput.waitFor({ state: 'visible', timeout: 5000 })
  await weightInput.fill(weight.toString())

  // Save the set
  const saveButton = page.locator(
    '[data-testid="save-set-button"], button:has-text("Save set")'
  )
  await saveButton.waitFor({ state: 'visible', timeout: 5000 })
  await saveButton.click()

  // Wait for save to complete
  await page.waitForTimeout(500)
}

/**
 * Get the first exercise card and extract its ID
 */
export async function getFirstExerciseId(page: Page): Promise<string | null> {
  const firstExerciseCard = page
    .locator('.exercise-card, [data-testid="exercise-card"]')
    .first()
  await firstExerciseCard.waitFor({ state: 'visible', timeout: 10000 })

  const exerciseLink = await firstExerciseCard.getAttribute('href')
  if (!exerciseLink) {
    return null
  }

  return exerciseLink.split('/').pop() || null
}

/**
 * Wait for workout session to be active
 */
export async function waitForWorkoutSession(page: Page) {
  // Wait for workout session indicators
  await page.waitForFunction(
    () => {
      const sessionData = localStorage.getItem('workout-session')
      return sessionData !== null
    },
    { timeout: 10000 }
  )
}

/**
 * Mock workout API responses for testing
 */
export async function mockWorkoutResponses(page: Page) {
  // Mock workout template groups
  await page.route(
    '**/api/Workout/GetUserWorkoutTemplateGroup*',
    async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            Id: 1,
            Label: 'Test Workout',
            IsFeaturedProgram: false,
            UserId: 'test-user-123',
            IsSystemExercise: false,
            RequiredWorkoutToLevelUp: 4,
            ProgramId: 1,
            WorkoutTemplates: [
              {
                Id: 101,
                Label: 'Test Day',
                UserId: 'test-user-123',
                IsSystemExercise: false,
                WorkoutSettingsModel: {},
                Exercices: [
                  {
                    Id: 1,
                    Label: 'Bench Press',
                    BodyPartId: 1,
                    IsFinished: false,
                    IsNextExercise: true,
                    IsSystemExercise: true,
                    IsSwapTarget: false,
                    IsUnilateral: false,
                    IsTimeBased: false,
                    IsEasy: false,
                    IsMedium: true,
                    IsBodyweight: false,
                    VideoUrl: '',
                    IsPlate: false,
                    IsWeighted: true,
                    IsPyramid: false,
                    IsNormalSets: true,
                    IsBodypartPriority: false,
                    IsFlexibility: false,
                    IsOneHanded: false,
                    LocalVideo: '',
                    IsAssisted: false,
                  },
                  {
                    Id: 2,
                    Label: 'Incline Dumbbell Press',
                    BodyPartId: 1,
                    IsFinished: false,
                    IsNextExercise: false,
                    IsSystemExercise: true,
                    IsSwapTarget: false,
                    IsUnilateral: false,
                    IsTimeBased: false,
                    IsEasy: false,
                    IsMedium: true,
                    IsBodyweight: false,
                    VideoUrl: '',
                    IsPlate: false,
                    IsWeighted: true,
                    IsPyramid: false,
                    IsNormalSets: true,
                    IsBodypartPriority: false,
                    IsFlexibility: false,
                    IsOneHanded: false,
                    LocalVideo: '',
                    IsAssisted: false,
                  },
                ],
              },
            ],
          },
        ]),
      })
    }
  )

  // Mock start workout session
  await page.route(
    '**/api/WorkoutSession/StartWorkoutSession*',
    async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 1,
          WorkoutTemplateId: 101,
          StartTime: new Date().toISOString(),
          UserId: 'test-user-123',
        }),
      })
    }
  )

  // Mock start workout
  await page.route('**/api/Workout/StartWorkout*', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        Id: 1,
        WorkoutTemplateId: 101,
        StartTime: new Date().toISOString(),
      }),
    })
  })
}
