import { describe, it, expect, vi, beforeEach } from 'vitest'
import { fetchCompleteUserStats } from '../userStatsComplete'
import { apiClient } from '../client'
import type { UserStats } from '@/types/userStats'

vi.mock('../client')

describe('fetchCompleteUserStats', () => {
  const mockApiClient = apiClient as unknown as {
    post: ReturnType<typeof vi.fn>
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should fetch complete stats using two-step approach', async () => {
    // Mock Step 1 response - SKIPPED
    // mockApiClient.post.mockResolvedValueOnce({
    //   data: {
    //     StatusCode: 200,
    //     Result: {
    //       // Program info response
    //     },
    //   },
    // })

    // Mock Step 2 response with complete data
    mockApiClient.post.mockResolvedValueOnce({
      data: {
        StatusCode: 200,
        Result: {
          HistoryExerciseModel: {
            TotalWorkoutCompleted: 79,
            TotalWeight: {
              Lb: 848977.7817950944,
              Kg: 385089.53,
            },
          },
          ConsecutiveWeeks: [
            { ConsecutiveWeeks: 3, MinWeek: 202510, MaxWeek: 202512 },
            { ConsecutiveWeeks: 7, MinWeek: 202522, MaxWeek: 202574 }, // Extended MaxWeek to include current week
          ],
        },
      },
    })

    const result = await fetchCompleteUserStats()

    expect(result).toEqual<UserStats>({
      weekStreak: 7, // Current streak
      workoutsCompleted: 79,
      lbsLifted: 848978, // Rounded
    })

    // Verify only one API call was made (Step 2)
    expect(mockApiClient.post).toHaveBeenCalledTimes(1)

    // Verify Step 1 call - SKIPPED
    // expect(mockApiClient.post).toHaveBeenNthCalledWith(
    //   1,
    //   '/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
    //   expect.objectContaining({
    //     standardName: expect.any(String),
    //   })
    // )

    // Verify Step 2 call
    expect(mockApiClient.post).toHaveBeenCalledWith(
      '/api/WorkoutLog/GetLogAverageWithSetsV2',
      {}
    )
  })

  it('should handle missing HistoryExerciseModel', async () => {
    // Mock response without HistoryExerciseModel
    mockApiClient.post.mockResolvedValueOnce({
      data: {
        StatusCode: 200,
        Result: {
          ConsecutiveWeeks: 5,
          WorkoutCount: 10,
        },
      },
    })

    const result = await fetchCompleteUserStats()

    expect(result).toEqual<UserStats>({
      weekStreak: 5,
      workoutsCompleted: 0,
      lbsLifted: 0,
    })
  })

  it('should handle API errors', async () => {
    mockApiClient.post.mockRejectedValueOnce(new Error('Network error'))

    await expect(fetchCompleteUserStats()).rejects.toThrow()
  })
})
