import { test, expect } from '@playwright/test'
import { login } from './helpers/auth'

test.describe('Workout Navigation Flow', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
  })

  test('should navigate through workout flow with proper navigation context', async ({
    page,
  }) => {
    // Navigate to workout overview
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Check navigation title
    await expect(
      page.locator('[data-testid="navigation-title"]')
    ).toContainText("Today's Workout")

    // Start workout
    const startButton = page.getByRole('button', { name: /start workout/i })
    await expect(startButton).toBeVisible()
    await startButton.click()

    // Wait for navigation to exercise page
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Exercise page should show exercise name in navigation
    const navTitle = page.locator('[data-testid="navigation-title"]')
    await expect(navTitle).toBeVisible()

    // Should have back button available
    const backButton = page.locator('[data-testid="navigation-back-button"]')
    await expect(backButton).toBeVisible()
  })

  test('should handle back navigation correctly', async ({ page }) => {
    // Start at workout overview
    await page.goto('/workout')

    // Navigate to an exercise directly
    await page.goto('/workout/exercise/123')
    await page.waitForLoadState('networkidle')

    // Should show back button
    const backButton = page.locator('[data-testid="navigation-back-button"]')
    await expect(backButton).toBeVisible()

    // Click back should return to workout overview
    await backButton.click()
    await expect(page).toHaveURL('/workout')
  })

  test('should track workout progress during navigation', async ({ page }) => {
    // Navigate to workout
    await page.goto('/workout')

    // Start workout
    const startButton = page.getByRole('button', { name: /start workout/i })
    await startButton.click()

    // Wait for exercise page
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Complete a set (this would trigger navigation to rest timer)
    const saveButton = page.getByRole('button', { name: /save/i }).first()
    if (await saveButton.isVisible()) {
      await saveButton.click()

      // Should navigate to rest timer
      await page.waitForURL('/workout/rest-timer', { timeout: 5000 })

      // Rest timer should have skip option
      const skipButton = page.getByRole('button', { name: /skip/i })
      await expect(skipButton).toBeVisible()
    }
  })
})
