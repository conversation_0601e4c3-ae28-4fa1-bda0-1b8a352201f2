import { test, expect, Page } from '@playwright/test'
import { login } from './helpers'

// Helper function to login and navigate to workout
async function loginAndNavigateToWorkout(page: Page) {
  await page.goto('/login')
  await login(page, '<EMAIL>', 'Dr123456')
  await page.goto('/workout')
}

test.describe('Save Button Fade Animation', () => {
  test.beforeEach(async ({ page }) => {
    await loginAndNavigateToWorkout(page)
  })

  test('should trigger fade animation when save button is clicked', async ({
    page,
  }) => {
    // Navigate to exercise page v2
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="exercise-card"]', {
      timeout: 30000,
    })

    // Click first exercise
    const firstExercise = page.locator('[data-testid="exercise-card"]').first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)
    await page.waitForSelector('.bg-surface-secondary.rounded-2xl', {
      timeout: 30000,
    })

    // Get the set card
    const setCard = page.locator('.bg-surface-secondary.rounded-2xl').first()

    // Record initial opacity
    const initialOpacity = await setCard.evaluate(
      (el) => getComputedStyle(el).opacity
    )
    expect(parseFloat(initialOpacity)).toBe(1)

    // Click save button
    const saveButton = page.locator('button:has-text("Save set")')
    await saveButton.click()

    // Card should fade out (opacity goes to 0)
    await page.waitForFunction(
      (selector) => {
        const element = document.querySelector(selector)
        return element && parseFloat(getComputedStyle(element).opacity) < 0.5
      },
      '.bg-surface-secondary.rounded-2xl',
      { timeout: 1000 }
    )

    // Then fade back in (opacity returns to 1)
    await page.waitForFunction(
      (selector) => {
        const element = document.querySelector(selector)
        return element && parseFloat(getComputedStyle(element).opacity) === 1
      },
      '.bg-surface-secondary.rounded-2xl',
      { timeout: 1000 }
    )

    // Verify we're on the next set
    const setLabel = await setCard.locator('h2').textContent()
    expect(setLabel).toMatch(/Set \d+|Warmup/)
  })

  test('should have same animation timing as swipe gesture', async ({
    page,
  }) => {
    // Navigate to exercise page v2
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="exercise-card"]', {
      timeout: 30000,
    })

    // Click first exercise
    const firstExercise = page.locator('[data-testid="exercise-card"]').first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)
    await page.waitForSelector('.bg-surface-secondary.rounded-2xl', {
      timeout: 30000,
    })

    // Time the save button animation
    const saveButton = page.locator('button:has-text("Save set")')
    const saveAnimationStart = Date.now()

    await saveButton.click()

    // Wait for fade animation to complete
    await page.waitForFunction(
      (selector) => {
        const element = document.querySelector(selector)
        return element && parseFloat(getComputedStyle(element).opacity) === 1
      },
      '.bg-surface-secondary.rounded-2xl',
      { timeout: 1000 }
    )

    const saveAnimationDuration = Date.now() - saveAnimationStart

    // Navigate back to test swipe animation
    await page.reload()
    await page.waitForSelector('.bg-surface-secondary.rounded-2xl', {
      timeout: 30000,
    })

    // Time the swipe animation
    const setCard = page.locator('.bg-surface-secondary.rounded-2xl').first()
    const box = await setCard.boundingBox()

    const swipeAnimationStart = Date.now()

    // Perform swipe
    await setCard.hover()
    await page.mouse.down()
    await page.mouse.move(box!.x + 200, box!.y, { steps: 10 })
    await page.mouse.up()

    // Wait for fade animation to complete
    await page.waitForFunction(
      (selector) => {
        const element = document.querySelector(selector)
        return element && parseFloat(getComputedStyle(element).opacity) === 1
      },
      '.bg-surface-secondary.rounded-2xl',
      { timeout: 1000 }
    )

    const swipeAnimationDuration = Date.now() - swipeAnimationStart

    // Animation durations should be similar (within 100ms)
    expect(
      Math.abs(saveAnimationDuration - swipeAnimationDuration)
    ).toBeLessThan(100)
  })

  test('should not trigger multiple animations on rapid clicks', async ({
    page,
  }) => {
    // Navigate to exercise page v2
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="exercise-card"]', {
      timeout: 30000,
    })

    // Click first exercise
    const firstExercise = page.locator('[data-testid="exercise-card"]').first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)
    await page.waitForSelector('.bg-surface-secondary.rounded-2xl', {
      timeout: 30000,
    })

    // Get initial set label
    const setCard = page.locator('.bg-surface-secondary.rounded-2xl').first()
    const initialSetLabel = await setCard.locator('h2').textContent()

    // Rapidly click save button multiple times
    const saveButton = page.locator('button:has-text("Save set")')
    await saveButton.click()
    await saveButton.click()
    await saveButton.click()

    // Wait a moment
    await page.waitForTimeout(600)

    // Should only advance one set
    const finalSetLabel = await setCard.locator('h2').textContent()
    expect(finalSetLabel).not.toBe(initialSetLabel)

    // Verify we only moved one set forward
    if (initialSetLabel === 'Warmup') {
      expect(finalSetLabel).toMatch(/Set 1|Warmup/)
    } else {
      const initialSetNumber = parseInt(initialSetLabel!.match(/\d+/)![0])
      const finalSetNumber = parseInt(finalSetLabel!.match(/\d+/)![0])
      expect(finalSetNumber).toBe(initialSetNumber + 1)
    }
  })
})
