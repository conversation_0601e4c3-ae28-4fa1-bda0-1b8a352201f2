import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { IOSNavigationBar } from '../IOSNavigationBar'
import { KebabMenuIcon } from '@/components/icons/KebabMenuIcon'

describe('IOSNavigationBar Icon Proportions', () => {
  describe('Exercise Page Icon Scaling', () => {
    it('should scale back button icon to 36px to match text-4xl title on exercise pages', () => {
      const { container } = render(
        <IOSNavigationBar title="Test Exercise" showBackButton isExercisePage />
      )

      const svg = container.querySelector('svg')
      expect(svg).toBeInTheDocument()
      expect(svg?.getAttribute('width')).toBe('36')
      expect(svg?.getAttribute('height')).toBe('36')
    })

    it('should scale right element icons to 36px on exercise pages for consistency', () => {
      // Use KebabMenuIcon component to test the actual integration
      const rightElement = (
        <button>
          <KebabMenuIcon size={36} className="text-text-primary" />
        </button>
      )

      const { container } = render(
        <IOSNavigationBar
          title="Test Exercise"
          isExercisePage
          rightElement={rightElement}
        />
      )

      // Find the SVG in the right element
      const svgs = container.querySelectorAll('svg')
      const rightSvg = svgs[svgs.length - 1] // Last SVG should be the right element
      expect(rightSvg.getAttribute('width')).toBe('36')
      expect(rightSvg.getAttribute('height')).toBe('36')
    })

    it('should maintain original icon sizes on non-exercise pages', () => {
      const { container } = render(
        <IOSNavigationBar
          title="Regular Page"
          showBackButton
          isExercisePage={false}
        />
      )

      const svg = container.querySelector('svg')
      expect(svg?.getAttribute('width')).toBe('28')
      expect(svg?.getAttribute('height')).toBe('28')
    })

    it('should preserve minimum 44px touch targets even with larger icons', () => {
      render(
        <IOSNavigationBar title="Test Exercise" showBackButton isExercisePage />
      )

      const backButton = screen.getByLabelText('Go back')

      // Button should have padding to ensure 44px touch target
      expect(backButton.classList.contains('px-2')).toBe(true)
      expect(backButton.classList.contains('py-2')).toBe(true)
    })

    it('should ensure visual proportion consistency between title and icons', () => {
      render(
        <IOSNavigationBar title="Test Exercise" showBackButton isExercisePage />
      )

      const titleElement = screen.getByText('Test Exercise')
      expect(titleElement.classList.contains('text-4xl')).toBe(true)

      // Icon size (36px) should be proportional to text-4xl (also ~36px)
      const { container } = render(
        <IOSNavigationBar title="Test Exercise" showBackButton isExercisePage />
      )
      const svg = container.querySelector('svg')
      expect(svg?.getAttribute('width')).toBe('36')
    })

    it('should ensure kebab menu is always visible on exercise pages when user is present', () => {
      // This test verifies the integration between navigation config and icon scaling
      const rightElement = (
        <button data-testid="kebab-menu">
          <KebabMenuIcon size={36} className="text-text-primary" />
        </button>
      )

      render(
        <IOSNavigationBar
          title="Test Exercise"
          isExercisePage
          rightElement={rightElement}
        />
      )

      const kebabMenu = screen.getByTestId('kebab-menu')
      expect(kebabMenu).toBeInTheDocument()
      expect(kebabMenu).toBeVisible()
    })
  })

  describe('Edge Cases', () => {
    it('should handle exercise pages with both back button and right element', () => {
      const rightElement = <button data-testid="right-button">Menu</button>

      const { container } = render(
        <IOSNavigationBar
          title="Test Exercise"
          showBackButton
          isExercisePage
          rightElement={rightElement}
        />
      )

      // Both elements should be properly sized
      const svg = container.querySelector('svg')
      expect(svg?.getAttribute('width')).toBe('36')

      const rightButton = screen.getByTestId('right-button')
      expect(rightButton).toBeInTheDocument()
    })

    it('should handle exercise pages with auto-scroll and scaled icons', () => {
      const longTitle =
        'This is a very long exercise name that triggers auto-scroll behavior'

      const { container } = render(
        <IOSNavigationBar title={longTitle} showBackButton isExercisePage />
      )

      const titleElement = screen.getByText(longTitle)
      const svg = container.querySelector('svg')

      // Both auto-scroll and icon scaling should work together
      expect(titleElement.classList.contains('text-4xl')).toBe(true)
      expect(svg?.getAttribute('width')).toBe('36')
    })

    it('should ensure kebab menu remains visible on mobile with long exercise names', () => {
      const longTitle =
        'Romanian Deadlift with Deficit and Pause at Bottom Position for Maximum Glute Activation'
      const rightElement = (
        <button data-testid="kebab-menu">
          <KebabMenuIcon size={36} className="text-text-primary" />
        </button>
      )

      render(
        <IOSNavigationBar
          title={longTitle}
          showBackButton
          isExercisePage
          rightElement={rightElement}
        />
      )

      // Verify kebab menu is in the DOM and visible
      const kebabMenu = screen.getByTestId('kebab-menu')
      expect(kebabMenu).toBeInTheDocument()
      expect(kebabMenu).toBeVisible()

      // Verify the right section has flex-shrink-0 to prevent it from being pushed off-screen
      const rightSection = kebabMenu.closest('div')
      expect(rightSection?.classList.contains('flex-shrink-0')).toBe(true)

      // Verify the center section has min-w-0 to allow proper overflow handling
      const titleElement = screen.getByText(longTitle)
      const centerSection =
        titleElement.closest('div')?.parentElement?.parentElement
      expect(centerSection?.classList.contains('min-w-0')).toBe(true)
    })
  })
})
