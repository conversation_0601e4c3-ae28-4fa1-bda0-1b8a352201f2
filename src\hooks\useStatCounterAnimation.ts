import { useEffect, useRef, useState } from 'react'

interface UseStatCounterAnimationProps {
  isLoading: boolean
  hasRealData: boolean
  estimatedMaxValues: number[]
  duration?: number
}

export function useStatCounterAnimation({
  isLoading,
  hasRealData,
  estimatedMaxValues,
  duration = 3000,
}: UseStatCounterAnimationProps) {
  const [animatedValues, setAnimatedValues] = useState<number[]>(
    estimatedMaxValues.map(() => 0)
  )
  const animationRef = useRef<number | null>(null)
  const startTimeRef = useRef<number>(Date.now())

  const prefersReducedMotion =
    typeof window !== 'undefined' &&
    window.matchMedia &&
    window.matchMedia('(prefers-reduced-motion: reduce)').matches

  useEffect(() => {
    if (!isLoading || hasRealData || prefersReducedMotion) {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
        animationRef.current = null
      }
      return
    }

    startTimeRef.current = Date.now()

    const animate = () => {
      const elapsed = Date.now() - startTimeRef.current
      const progress = Math.min(elapsed / duration, 1)

      const easeOut = 1 - Math.pow(1 - progress, 3)

      const newValues = estimatedMaxValues.map((max) =>
        Math.round(max * easeOut)
      )

      setAnimatedValues(newValues)

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate)
      }
    }

    animationRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
        animationRef.current = null
      }
    }
  }, [
    isLoading,
    hasRealData,
    prefersReducedMotion,
    duration,
    estimatedMaxValues,
  ])

  const resetAnimation = () => {
    startTimeRef.current = Date.now()
  }

  return {
    animatedValues,
    resetAnimation,
  }
}
