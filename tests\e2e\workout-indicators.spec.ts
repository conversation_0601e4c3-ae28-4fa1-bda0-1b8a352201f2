import { test, expect } from '@playwright/test'
import { login } from './helpers'

test.describe('Workout Exercise Status Indicators', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
  })

  test('should update exercise status indicators after saving sets', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview"]')

    // Start workout
    await page.click('button:has-text("Start workout")')
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Save first set - exercise should show "in progress"
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '100')
    await page.click('button:has-text("Save set")')

    // Wait for save to complete
    await page.waitForTimeout(500)

    // Go back to workout overview
    await page.click('[aria-label="Back"]')
    await page.waitForSelector('[data-testid="workout-overview"]')

    // First exercise should now show as in progress (not finished)
    const firstExerciseCard = page
      .locator('[data-testid="exercise-card"]')
      .first()

    // Check that the exercise is NOT showing as finished (no checkmark)
    await expect(
      firstExerciseCard.locator('.text-brand-primary:has-text("✓")')
    ).toBeHidden()

    // The card should not have reduced opacity (which indicates finished state)
    await expect(firstExerciseCard).not.toHaveClass(/opacity-60/)

    // Go back to the exercise and complete all sets
    await firstExerciseCard.click()
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Save remaining sets (assuming 3 total)
    // eslint-disable-next-line no-await-in-loop
    for (let i = 0; i < 2; i++) {
      // eslint-disable-next-line no-await-in-loop
      await page.fill('input[placeholder="Reps"]', '10')
      // eslint-disable-next-line no-await-in-loop
      await page.fill('input[placeholder*="Weight"]', '100')
      // eslint-disable-next-line no-await-in-loop
      await page.click('button:has-text("Save set")')
      // eslint-disable-next-line no-await-in-loop
      await page.waitForTimeout(500)
    }

    // Go back to workout overview
    await page.click('[aria-label="Back"]')
    await page.waitForSelector('[data-testid="workout-overview"]')

    // First exercise should now show as finished
    const completedExerciseCard = page
      .locator('[data-testid="exercise-card"]')
      .first()

    // Check for the checkmark
    await expect(
      completedExerciseCard.locator('.text-brand-primary:has-text("✓")')
    ).toBeVisible()

    // The card should have reduced opacity
    await expect(completedExerciseCard).toHaveClass(/opacity-60/)
  })

  test('should persist status indicators when navigating between exercises', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview"]')

    // Start workout
    await page.click('button:has-text("Start workout")')
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Save a set on first exercise
    await page.fill('input[placeholder="Reps"]', '12')
    await page.fill('input[placeholder*="Weight"]', '50')
    await page.click('button:has-text("Save set")')
    await page.waitForTimeout(500)

    // Navigate to next exercise using the menu
    await page.click('[aria-label="Back"]')
    await page.waitForSelector('[data-testid="workout-overview"]')

    // Click on second exercise
    const secondExercise = page.locator('[data-testid="exercise-card"]').nth(1)
    await secondExercise.click()
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Save a set on second exercise
    await page.fill('input[placeholder="Reps"]', '15')
    await page.fill('input[placeholder*="Weight"]', '30')
    await page.click('button:has-text("Save set")')
    await page.waitForTimeout(500)

    // Go back to overview
    await page.click('[aria-label="Back"]')
    await page.waitForSelector('[data-testid="workout-overview"]')

    // Both exercises should maintain their status
    const exerciseCards = page.locator('[data-testid="exercise-card"]')

    // First exercise - in progress (has sets but not finished)
    const firstCard = exerciseCards.nth(0)
    await expect(
      firstCard.locator('.text-brand-primary:has-text("✓")')
    ).toBeHidden()
    await expect(firstCard).not.toHaveClass(/opacity-60/)

    // Second exercise - also in progress
    const secondCard = exerciseCards.nth(1)
    await expect(
      secondCard.locator('.text-brand-primary:has-text("✓")')
    ).toBeHidden()
    await expect(secondCard).not.toHaveClass(/opacity-60/)
  })
})
