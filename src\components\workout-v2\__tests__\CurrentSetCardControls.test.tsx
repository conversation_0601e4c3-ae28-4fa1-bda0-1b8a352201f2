import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { CurrentSetCardControls } from '../CurrentSetCardControls'
import type { RecommendationModel } from '@/types'

describe('CurrentSetCardControls', () => {
  const defaultProps = {
    setData: { reps: 10, weight: 100, duration: 0 },
    onSetDataChange: vi.fn(),
    unit: 'kg' as const,
    isSaving: false,
    animationClass: '',
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Fade-in animation', () => {
    it('should apply 200ms fade-in animation class when animationClass is provided', () => {
      render(
        <CurrentSetCardControls {...defaultProps} animationClass="fade-in" />
      )

      const container = screen.getByTestId('input-controls-container')
      expect(container).toHaveClass('fade-in')
    })

    it('should render without animation class when not provided', () => {
      render(<CurrentSetCardControls {...defaultProps} />)

      const container = screen.getByTestId('input-controls-container')
      expect(container).not.toHaveClass('fade-in')
    })
  })

  it('renders reps and weight sections', () => {
    render(<CurrentSetCardControls {...defaultProps} />)

    expect(screen.getByTestId('reps-section')).toBeInTheDocument()
    expect(screen.getByTestId('weight-section')).toBeInTheDocument()
  })

  describe('Weight increment behavior', () => {
    describe('Default increments (no recommendation)', () => {
      it('should increment weight by 1 for kg unit', () => {
        const onSetDataChange = vi.fn()
        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="kg"
            setData={{ reps: 10, weight: 50, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight increment button
        const incrementButton = screen.getByLabelText('Increase weight')
        fireEvent.click(incrementButton)

        // Should increment by 1 for kg
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 51, // 50 + 1
          duration: 0,
        })
      })

      it('should increment weight by 2.5 for lbs unit', () => {
        const onSetDataChange = vi.fn()
        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="lbs"
            setData={{ reps: 10, weight: 100, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight increment button
        const incrementButton = screen.getByLabelText('Increase weight')
        fireEvent.click(incrementButton)

        // Should increment by 2.5 for lbs
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 102.5, // 100 + 2.5
          duration: 0,
        })
      })

      it('should decrement weight by 1 for kg unit', () => {
        const onSetDataChange = vi.fn()
        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="kg"
            setData={{ reps: 10, weight: 50, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight decrement button
        const decrementButton = screen.getByLabelText('Decrease weight')
        fireEvent.click(decrementButton)

        // Should decrement by 1 for kg
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 49, // 50 - 1
          duration: 0,
        })
      })

      it('should decrement weight by 2.5 for lbs unit', () => {
        const onSetDataChange = vi.fn()
        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="lbs"
            setData={{ reps: 10, weight: 100, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight decrement button
        const decrementButton = screen.getByLabelText('Decrease weight')
        fireEvent.click(decrementButton)

        // Should decrement by 2.5 for lbs
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 97.5, // 100 - 2.5
          duration: 0,
        })
      })

      it('should not allow weight to go below 0', () => {
        const onSetDataChange = vi.fn()
        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="kg"
            setData={{ reps: 10, weight: 0.5, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight decrement button
        const decrementButton = screen.getByLabelText('Decrease weight')
        fireEvent.click(decrementButton)

        // Should set to 0, not negative
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 0,
          duration: 0,
        })
      })
    })

    describe('Recommendation-based increments', () => {
      it('should use recommendation increment for kg when available', () => {
        const onSetDataChange = vi.fn()
        const recommendation: Partial<RecommendationModel> = {
          Increments: { Kg: 1, Lb: 2.5 },
        }

        // Note: This test will fail until we add recommendation prop
        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="kg"
            setData={{ reps: 10, weight: 50, duration: 0 }}
            onSetDataChange={onSetDataChange}
            recommendation={recommendation as RecommendationModel}
          />
        )

        // Find and click the weight increment button
        const incrementButton = screen.getByLabelText('Increase weight')
        fireEvent.click(incrementButton)

        // Should use recommendation increment of 2.5 for kg
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 52.5, // 50 + 2.5
          duration: 0,
        })
      })

      it('should use recommendation increment for lbs when available', () => {
        const onSetDataChange = vi.fn()
        const recommendation: Partial<RecommendationModel> = {
          Increments: { Kg: 1, Lb: 2.5 },
        }

        // Note: This test will fail until we add recommendation prop
        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="lbs"
            setData={{ reps: 10, weight: 100, duration: 0 }}
            onSetDataChange={onSetDataChange}
            recommendation={recommendation as RecommendationModel}
          />
        )

        // Find and click the weight increment button
        const incrementButton = screen.getByLabelText('Increase weight')
        fireEvent.click(incrementButton)

        // Should use recommendation increment of 5 for lbs
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 105, // 100 + 5
          duration: 0,
        })
      })

      it('should fall back to defaults when recommendation has no increments', () => {
        const onSetDataChange = vi.fn()
        const recommendation: Partial<RecommendationModel> = {
          ExerciseId: 123,
          // No Increments property
        }

        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="kg"
            setData={{ reps: 10, weight: 50, duration: 0 }}
            onSetDataChange={onSetDataChange}
            recommendation={recommendation as RecommendationModel}
          />
        )

        // Find and click the weight increment button
        const incrementButton = screen.getByLabelText('Increase weight')
        fireEvent.click(incrementButton)

        // Should fall back to default increment of 1 for kg
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 51, // 50 + 1
          duration: 0,
        })
      })
    })
  })

  describe('Reps increment behavior', () => {
    it('should increment reps by 1', () => {
      const onSetDataChange = vi.fn()
      render(
        <CurrentSetCardControls
          {...defaultProps}
          setData={{ reps: 10, weight: 50, duration: 0 }}
          onSetDataChange={onSetDataChange}
        />
      )

      // Find and click the reps increment button
      const incrementButton = screen.getByLabelText('Increase reps')
      fireEvent.click(incrementButton)

      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 11,
        weight: 50,
        duration: 0,
      })
    })

    it('should decrement reps by 1 but not below 1', () => {
      const onSetDataChange = vi.fn()
      render(
        <CurrentSetCardControls
          {...defaultProps}
          setData={{ reps: 2, weight: 50, duration: 0 }}
          onSetDataChange={onSetDataChange}
        />
      )

      // Find and click the reps decrement button
      const decrementButton = screen.getByLabelText('Decrease reps')
      fireEvent.click(decrementButton)

      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 1,
        weight: 50,
        duration: 0,
      })
    })

    it('should not decrement reps below 1', () => {
      const onSetDataChange = vi.fn()
      render(
        <CurrentSetCardControls
          {...defaultProps}
          setData={{ reps: 1, weight: 50, duration: 0 }}
          onSetDataChange={onSetDataChange}
        />
      )

      // Find and click the reps decrement button
      const decrementButton = screen.getByLabelText('Decrease reps')
      fireEvent.click(decrementButton)

      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 1, // Should stay at 1
        weight: 50,
        duration: 0,
      })
    })
  })
})
