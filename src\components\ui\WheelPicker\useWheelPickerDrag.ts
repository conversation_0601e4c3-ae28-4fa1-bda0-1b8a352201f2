import { useState, useRef } from 'react'
import { vibrate } from '@/utils/haptics'

export function useWheelPickerDrag(
  value: number,
  onChange: (value: number) => void,
  visibleValues: number[],
  disabled: boolean
) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [scrollLeft, setScrollLeft] = useState(0)
  const lastScrollTimeRef = useRef(0)

  const handleMouseDown = (e: React.MouseEvent) => {
    if (disabled) return
    setIsDragging(true)
    setStartX(e.pageX - (containerRef.current?.offsetLeft || 0))
    setScrollLeft(containerRef.current?.scrollLeft || 0)
  }

  const handleTouchStart = (e: React.TouchEvent) => {
    if (disabled) return
    const touch = e.touches[0]
    if (!touch) return
    setIsDragging(true)
    setStartX(touch.pageX - (containerRef.current?.offsetLeft || 0))
    setScrollLeft(containerRef.current?.scrollLeft || 0)
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !containerRef.current) return
    e.preventDefault()
    const x = e.pageX - (containerRef.current.offsetLeft || 0)
    const walk = (x - startX) * 2
    containerRef.current.scrollLeft = scrollLeft - walk
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging || !containerRef.current) return
    const touch = e.touches[0]
    if (!touch) return
    const x = touch.pageX - (containerRef.current.offsetLeft || 0)
    const walk = (x - startX) * 2
    containerRef.current.scrollLeft = scrollLeft - walk
  }

  const handleEnd = () => {
    if (!isDragging) return
    setIsDragging(false)

    // Snap to nearest value based on scroll position
    if (containerRef.current) {
      const scrollPos = containerRef.current.scrollLeft
      const itemWidth = containerRef.current.scrollWidth / visibleValues.length
      const index = Math.round(scrollPos / itemWidth)

      if (index >= 0 && index < visibleValues.length) {
        const newValue = visibleValues[index]
        if (newValue !== undefined && newValue !== value) {
          vibrate('light')
          onChange(newValue)
        }
      }
    }
  }

  const handleScroll = () => {
    if (isDragging || !containerRef.current) return

    // Add debounce to prevent rapid-fire changes (50ms minimum between changes)
    const now = Date.now()
    if (now - lastScrollTimeRef.current < 50) return
    lastScrollTimeRef.current = now

    const container = containerRef.current
    const scrollPos = container.scrollLeft + container.clientWidth / 2
    const itemWidth = container.scrollWidth / visibleValues.length
    const currentIndex = visibleValues.indexOf(value)
    const targetIndex = Math.round(scrollPos / itemWidth)

    // Only change by 1 increment at a time to reduce sensitivity
    let newIndex = currentIndex
    if (targetIndex > currentIndex) {
      newIndex = currentIndex + 1
    } else if (targetIndex < currentIndex) {
      newIndex = currentIndex - 1
    }

    if (
      newIndex >= 0 &&
      newIndex < visibleValues.length &&
      newIndex !== currentIndex
    ) {
      const newValue = visibleValues[newIndex]
      if (newValue !== undefined && newValue !== value) {
        vibrate('light')
        onChange(newValue)
      }
    }
  }

  return {
    containerRef,
    isDragging,
    handleMouseDown,
    handleTouchStart,
    handleMouseMove,
    handleTouchMove,
    handleEnd,
    handleScroll,
  }
}
