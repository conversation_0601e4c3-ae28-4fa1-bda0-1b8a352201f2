import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from './helpers/authHelper'

test.describe('Workout Swipe Fade Animation', () => {
  test.beforeEach(async ({ page }) => {
    await setupAuthenticatedUser(page)

    // Start a workout to access exercise page
    await page.goto('/workout')
    await page.click('button:has-text("Start workout")')

    // Wait for exercise page to load
    await page.waitForSelector('[data-testid="current-set-card"]', {
      state: 'visible',
    })
  })

  test('should fade in from center after right swipe (complete)', async ({
    page,
  }) => {
    const setCard = page.locator('[data-testid="current-set-card"]')

    // Record initial position
    const initialBox = await setCard.boundingBox()

    // Perform right swipe gesture
    await setCard.hover()
    await page.mouse.down()
    await page.mouse.move(initialBox!.x + 200, initialBox!.y, { steps: 10 })
    await page.mouse.up()

    // Wait for animation to complete
    await page.waitForTimeout(500)

    // Verify card is back at center position (not sliding from left)
    const finalBox = await setCard.boundingBox()
    expect(Math.abs(finalBox!.x - initialBox!.x)).toBeLessThan(10) // Should be centered

    // Verify opacity is back to 1 (fully visible)
    const opacity = await setCard.evaluate((el) => getComputedStyle(el).opacity)
    expect(parseFloat(opacity)).toBeCloseTo(1, 1)
  })

  test('should fade in from center after left swipe (skip)', async ({
    page,
  }) => {
    const setCard = page.locator('[data-testid="current-set-card"]')

    // Record initial position
    const initialBox = await setCard.boundingBox()

    // Perform left swipe gesture
    await setCard.hover()
    await page.mouse.down()
    await page.mouse.move(initialBox!.x - 200, initialBox!.y, { steps: 10 })
    await page.mouse.up()

    // Wait for animation to complete
    await page.waitForTimeout(500)

    // Verify card is back at center position (not sliding from right)
    const finalBox = await setCard.boundingBox()
    expect(Math.abs(finalBox!.x - initialBox!.x)).toBeLessThan(10) // Should be centered

    // Verify opacity is back to 1 (fully visible)
    const opacity = await setCard.evaluate((el) => getComputedStyle(el).opacity)
    expect(parseFloat(opacity)).toBeCloseTo(1, 1)
  })

  test('should maintain smooth animation timing', async ({ page }) => {
    const setCard = page.locator('[data-testid="current-set-card"]')
    const initialBox = await setCard.boundingBox()

    // Start monitoring animation
    const animationStart = Date.now()

    // Perform swipe
    await setCard.hover()
    await page.mouse.down()
    await page.mouse.move(initialBox!.x + 200, initialBox!.y, { steps: 10 })
    await page.mouse.up()

    // Wait for fade-in to complete
    await page.waitForFunction(
      () => {
        const element = document.querySelector(
          '[data-testid="current-set-card"]'
        )
        return element && getComputedStyle(element).opacity === '1'
      },
      { timeout: 1000 }
    )

    const animationEnd = Date.now()
    const duration = animationEnd - animationStart

    // Animation should complete within reasonable time (under 1 second)
    expect(duration).toBeLessThan(1000)
    expect(duration).toBeGreaterThan(200) // But not too fast
  })
})
