import { test, expect } from '@playwright/test'

test.describe('OAuth Authentication Header', () => {
  test('should add Authorization header to API requests after Google OAuth login', async ({
    page,
    context,
  }) => {
    // Set up request interception to monitor API calls
    const apiCalls: { url: string; headers: Record<string, string> }[] = []

    await context.route(
      '**/drmuscle.azurewebsites.net/**',
      async (route, request) => {
        const headers = await request.allHeaders()
        apiCalls.push({
          url: request.url(),
          headers,
        })

        // Continue with the request
        await route.continue()
      }
    )

    // Navigate to login page
    await page.goto('/login')

    // Mock Google OAuth success
    await page.evaluate(() => {
      // Mock the Google OAuth response
      window.google = {
        accounts: {
          id: {
            initialize: () => {},
            prompt: (callback: any) => {
              // Simulate successful OAuth with a mock credential
              callback({
                credential: 'mock-google-id-token',
                select_by: 'user',
              })
            },
          },
        },
      } as any
    })

    // Click Google sign-in button
    await page.click('button:has-text("Continue with Google")')

    // Wait for navigation after successful login
    await page.waitForURL('**/program', { timeout: 10000 })

    // Check that API calls include Authorization header
    const authApiCalls = apiCalls.filter(
      (call) => call.url.includes('/api/') && !call.url.includes('/token') // Exclude the OAuth token endpoint
    )

    // Verify that all API calls have Authorization header
    authApiCalls.forEach((call) => {
      expect(call.headers.authorization).toBeTruthy()
      expect(call.headers.authorization).toMatch(/^Bearer .+/)
    })

    // Verify at least some API calls were made
    expect(authApiCalls.length).toBeGreaterThan(0)
  })

  test('should handle 401 errors gracefully when token is invalid', async ({
    page,
  }) => {
    // Navigate to login and set an invalid token
    await page.goto('/login')

    // Set invalid auth state directly
    await page.evaluate(() => {
      localStorage.setItem(
        'drmuscle-auth',
        JSON.stringify({
          state: {
            token: 'invalid-token',
            user: { email: '<EMAIL>' },
            isAuthenticated: true,
          },
          version: 0,
        })
      )
    })

    // Navigate to a protected page
    await page.goto('/program')

    // Should redirect back to login when API calls fail
    await page.waitForURL('**/login', { timeout: 10000 })

    // Verify auth state is cleared
    const authState = await page.evaluate(() => {
      const stored = localStorage.getItem('drmuscle-auth')
      return stored ? JSON.parse(stored) : null
    })

    expect(authState?.state?.isAuthenticated).toBeFalsy()
    expect(authState?.state?.token).toBeNull()
  })
})
