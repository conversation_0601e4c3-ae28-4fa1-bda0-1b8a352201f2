import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useRestTimer } from '@/components/workout-v2/RestTimer'
import { useAuthStore } from '@/stores/authStore'
import { debugLog } from '@/utils/debugLog'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Extended type to match generateAllSets output
type ExtendedWorkoutLogSerieModel = WorkoutLogSerieModel & {
  WarmUpReps?: number
  WarmUpWeightSet?: { Lb: number; Kg: number }
}

interface UseExerciseV2ActionsProps {
  currentExercise: ExerciseModel | null
  workoutSession: unknown
  setData: { reps: number; weight: number; duration: number }
  currentSetIndex: number
  allSets: ExtendedWorkoutLogSerieModel[]
  isWarmup: boolean
  isLastSet: boolean
  isFirstWorkSet: boolean
  currentSet: WorkoutLogSerieModel | null
  setSaveError: (error: string) => void
  isLastExercise?: boolean
  exercises?: ExerciseModel[]
  onExerciseComplete?: () => void
}

export function useExerciseV2Actions({
  currentExercise,
  workoutSession,
  setData,
  currentSetIndex,
  allSets,
  isWarmup,
  isLastSet,
  isFirstWorkSet,
  currentSet,
  setSaveError,
  isLastExercise = false,
  exercises = [],
  onExerciseComplete,
}: UseExerciseV2ActionsProps) {
  const router = useRouter()
  const { saveSet } = useWorkout()
  const { nextSet } = useWorkoutStore()
  const { startRestTimer } = useRestTimer()
  const { getCachedUserInfo } = useAuthStore()
  const userInfo = getCachedUserInfo()
  const unit: 'kg' | 'lbs' = userInfo?.MassUnit === 'kg' ? 'kg' : 'lbs'

  const handleCompleteSet = useCallback(async () => {
    // eslint-disable-next-line no-console
    console.log('[ExerciseV2] Save button clicked - checking required data:', {
      hasCurrentExercise: !!currentExercise,
      hasWorkoutSession: !!workoutSession,
      currentExerciseId: currentExercise?.Id,
      currentExerciseLabel: currentExercise?.Label,
      workoutSessionId:
        workoutSession &&
        typeof workoutSession === 'object' &&
        'id' in workoutSession
          ? String(workoutSession.id)
          : 'null',
      setDataReps: setData.reps,
      setDataWeight: setData.weight,
    })

    if (!currentExercise || !workoutSession) {
      console.warn('[ExerciseV2] Cannot save set - missing required data:', {
        hasCurrentExercise: !!currentExercise,
        hasWorkoutSession: !!workoutSession,
        currentExerciseId: currentExercise?.Id,
        currentExerciseLabel: currentExercise?.Label,
      })
      return
    }

    debugLog('[ExerciseV2] Completing set')

    try {
      // Save the set directly to avoid navigation
      await saveSet({
        exerciseId: currentExercise.Id,
        reps: currentExercise.IsTimeBased ? undefined : setData.reps,
        weight: setData.weight,
        isWarmup,
        setNumber: currentSetIndex + 1,
        duration: currentExercise.IsTimeBased ? setData.duration : undefined,
        RIR: undefined,
      })

      // Handle RIR if it's the first work set
      if (isFirstWorkSet && !currentExercise.IsTimeBased) {
        // For v2, we'll skip RIR picker for now
        // TODO: Implement inline RIR selection
      }

      // Check if this is the last set
      const currentSetIdx = allSets.findIndex((s) => s.IsNext)
      const hasMoreSets = currentSetIdx < allSets.length - 1

      if (isLastSet) {
        // Stay in V2 flow - handle last set differently
        if (isLastExercise) {
          // Show exercise complete view for last exercise
          if (onExerciseComplete) {
            onExerciseComplete()
          }
        } else {
          // Navigate to next exercise in V2 flow
          const currentIndex = exercises.findIndex(
            (ex) => ex.Id === currentExercise.Id
          )
          if (currentIndex !== -1 && currentIndex < exercises.length - 1) {
            const nextExercise = exercises[currentIndex + 1]
            if (nextExercise) {
              router.push(`/workout/exercise-v2/${nextExercise.Id}`)
            }
          }
        }
      } else if (hasMoreSets) {
        // Move to next set without navigation
        nextSet()

        // Find the next set info
        const nextSetData = allSets[currentSetIdx + 1]
        const nextSetInfo = nextSetData
          ? {
              reps:
                nextSetData.IsWarmups && nextSetData.WarmUpReps
                  ? nextSetData.WarmUpReps
                  : nextSetData.Reps || 0,
              weight:
                nextSetData.IsWarmups && nextSetData.WarmUpWeightSet
                  ? nextSetData.WarmUpWeightSet[unit === 'kg' ? 'Kg' : 'Lb']
                  : nextSetData.Weight?.[unit === 'kg' ? 'Kg' : 'Lb'] || 0,
              unit,
            }
          : undefined

        // Calculate rest duration based on set type
        const restDuration = currentSet?.IsWarmups ? 30 : 90 // 30s for warmups, 90s for work sets
        startRestTimer(restDuration, nextSetInfo)
      }
    } catch (error) {
      console.error('Failed to save set:', error)
      setSaveError(
        error instanceof Error ? error.message : 'Failed to save set'
      )
    }
  }, [
    currentExercise,
    workoutSession,
    setData,
    isWarmup,
    currentSetIndex,
    isFirstWorkSet,
    allSets,
    isLastSet,
    currentSet,
    saveSet,
    nextSet,
    startRestTimer,
    setSaveError,
    isLastExercise,
    exercises,
    onExerciseComplete,
    router,
    unit,
  ])

  const handleSkipSet = useCallback(async () => {
    if (!currentExercise || !workoutSession) return

    debugLog('[ExerciseV2] Skipping set')

    try {
      // Save the set with 0 reps to mark as skipped
      await saveSet({
        exerciseId: currentExercise.Id,
        reps: 0,
        weight: 0,
        isWarmup,
        setNumber: currentSetIndex + 1,
        duration: 0,
        RIR: undefined,
      })

      // Check if this is the last set
      const currentSetIdx = allSets.findIndex((s) => s.IsNext)
      const hasMoreSets = currentSetIdx < allSets.length - 1

      if (isLastSet) {
        // Stay in V2 flow - handle last set differently (same as complete)
        if (isLastExercise) {
          // Show exercise complete view for last exercise
          if (onExerciseComplete) {
            onExerciseComplete()
          }
        } else {
          // Navigate to next exercise in V2 flow
          const currentIndex = exercises.findIndex(
            (ex) => ex.Id === currentExercise.Id
          )
          if (currentIndex !== -1 && currentIndex < exercises.length - 1) {
            const nextExercise = exercises[currentIndex + 1]
            if (nextExercise) {
              router.push(`/workout/exercise-v2/${nextExercise.Id}`)
            }
          }
        }
      } else if (hasMoreSets) {
        // Move to next set without navigation
        nextSet()

        // Find the next set info
        const nextSetData = allSets[currentSetIdx + 1]
        const nextSetInfo = nextSetData
          ? {
              reps:
                nextSetData.IsWarmups && nextSetData.WarmUpReps
                  ? nextSetData.WarmUpReps
                  : nextSetData.Reps || 0,
              weight:
                nextSetData.IsWarmups && nextSetData.WarmUpWeightSet
                  ? nextSetData.WarmUpWeightSet[unit === 'kg' ? 'Kg' : 'Lb']
                  : nextSetData.Weight?.[unit === 'kg' ? 'Kg' : 'Lb'] || 0,
              unit,
            }
          : undefined

        // Calculate rest duration based on set type (shorter for skipped sets)
        const restDuration = currentSet?.IsWarmups ? 15 : 30 // Shorter rest for skipped sets
        startRestTimer(restDuration, nextSetInfo)
      }
    } catch (error) {
      console.error('Failed to skip set:', error)
      setSaveError(
        error instanceof Error ? error.message : 'Failed to skip set'
      )
    }
  }, [
    currentExercise,
    workoutSession,
    isWarmup,
    currentSetIndex,
    allSets,
    isLastSet,
    currentSet,
    saveSet,
    nextSet,
    startRestTimer,
    setSaveError,
    isLastExercise,
    exercises,
    onExerciseComplete,
    router,
    unit,
  ])

  return {
    handleCompleteSet,
    handleSkipSet,
  }
}
