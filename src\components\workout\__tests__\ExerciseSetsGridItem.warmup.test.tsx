import React from 'react'
import { render, screen } from '@testing-library/react'
import { ExerciseSetsGridItem } from '../ExerciseSetsGridItem'
import type { ExtendedWorkoutLogSerieModel } from '../ExerciseSetsGridItem'
import type { ExerciseModel, RecommendationModel } from '@/types'

// Mock SetCell to capture its props
let mockOnWeightChange: ((weight: number) => void) | undefined
let mockOnRepsChange: ((reps: number) => void) | undefined

vi.mock('../SetCell', () => ({
  SetCell: ({ onWeightChange, onRepsChange, weight = 0, reps = 0 }: any) => {
    // Capture the handlers for testing
    mockOnWeightChange = onWeightChange
    mockOnRepsChange = onRepsChange

    return (
      <div data-testid="mocked-set-cell">
        <input type="number" value={weight} data-testid="weight-input" />
        <input type="number" value={reps} data-testid="reps-input" />
      </div>
    )
  },
}))

describe('ExerciseSetsGridItem - Warmup Weight Persistence', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
  }

  const mockRecommendation: RecommendationModel = {
    ExerciseId: 1,
    Weight: { Lb: 135, Kg: 61.2 },
    Reps: 10,
    Series: 3,
    WarmupsCount: 2,
    WarmUpsList: [
      {
        WarmUpReps: 5,
        WarmUpWeightSet: { Lb: 95, Kg: 43.1 },
      },
      {
        WarmUpReps: 3,
        WarmUpWeightSet: { Lb: 115, Kg: 52.2 },
      },
    ],
  } as RecommendationModel

  const mockOnSetUpdate = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should display recommendation weight for finished warmup with 0 weight', () => {
    // Test: When a warmup is saved (IsFinished=true) and API returns weight=0,
    // it should fallback to display the recommendation weight
    const warmupSet: ExtendedWorkoutLogSerieModel = {
      Id: -1001,
      SetNo: '1',
      IsWarmups: true,
      IsFinished: true,
      IsNext: false,
      WarmUpReps: 5,
      WarmUpWeightSet: { Lb: 0, Kg: 0 }, // API returned 0
      Reps: 0,
      Weight: { Lb: 0, Kg: 0 },
    }

    render(
      <ExerciseSetsGridItem
        set={warmupSet}
        index={0}
        sets={[warmupSet]}
        exercise={mockExercise}
        recommendation={mockRecommendation}
        onSetUpdate={mockOnSetUpdate}
        unit="lbs"
        currentSetIndex={1}
        allSetsFinished={false}
      />
    )

    // Should display the recommendation weight (95 lbs) not 0
    const weightInput = screen.getByDisplayValue('95')
    expect(weightInput).toBeInTheDocument()
  })

  it('should display recommendation weight in kg when unit is kg', () => {
    const warmupSet: ExtendedWorkoutLogSerieModel = {
      Id: -1001,
      SetNo: '1',
      IsWarmups: true,
      IsFinished: true,
      IsNext: false,
      WarmUpReps: 5,
      WarmUpWeightSet: { Lb: 0, Kg: 0 },
      Reps: 0,
      Weight: { Lb: 0, Kg: 0 },
    }

    render(
      <ExerciseSetsGridItem
        set={warmupSet}
        index={0}
        sets={[warmupSet]}
        exercise={mockExercise}
        recommendation={mockRecommendation}
        onSetUpdate={mockOnSetUpdate}
        unit="kg"
        currentSetIndex={1}
        allSetsFinished={false}
      />
    )

    // Should display 43.1 kg from recommendation
    const weightInput = screen.getByDisplayValue('43.1')
    expect(weightInput).toBeInTheDocument()
  })

  it('should display actual weight when warmup has non-zero weight', () => {
    const warmupSet: ExtendedWorkoutLogSerieModel = {
      Id: -1001,
      SetNo: '1',
      IsWarmups: true,
      IsFinished: true,
      IsNext: false,
      WarmUpReps: 5,
      WarmUpWeightSet: { Lb: 100, Kg: 45.4 }, // Has actual weight
      Reps: 0,
      Weight: { Lb: 0, Kg: 0 },
    }

    render(
      <ExerciseSetsGridItem
        set={warmupSet}
        index={0}
        sets={[warmupSet]}
        exercise={mockExercise}
        recommendation={mockRecommendation}
        onSetUpdate={mockOnSetUpdate}
        unit="lbs"
        currentSetIndex={1}
        allSetsFinished={false}
      />
    )

    // Should display actual weight (100 lbs) not recommendation
    const weightInput = screen.getByDisplayValue('100')
    expect(weightInput).toBeInTheDocument()
  })

  it('should not apply fallback for work sets', () => {
    const workSet: ExtendedWorkoutLogSerieModel = {
      Id: -2001,
      SetNo: '3',
      IsWarmups: false,
      IsFinished: true,
      IsNext: false,
      Reps: 10,
      Weight: { Lb: 0, Kg: 0 }, // Work set with 0 weight
    }

    render(
      <ExerciseSetsGridItem
        set={workSet}
        index={2}
        sets={[workSet]}
        exercise={mockExercise}
        recommendation={mockRecommendation}
        onSetUpdate={mockOnSetUpdate}
        unit="lbs"
        currentSetIndex={3}
        allSetsFinished={false}
      />
    )

    // Should display 0 for work sets (no fallback)
    const weightInput = screen.getByDisplayValue('0')
    expect(weightInput).toBeInTheDocument()
  })

  it('should use unified reps/weight properties in onSetUpdate', () => {
    // Reset the captured handlers
    mockOnWeightChange = undefined
    mockOnRepsChange = undefined

    const warmupSet: ExtendedWorkoutLogSerieModel = {
      Id: -1001,
      SetNo: '1',
      IsWarmups: true,
      IsFinished: false,
      IsNext: true,
      WarmUpReps: 5,
      WarmUpWeightSet: { Lb: 95, Kg: 43.1 },
      Reps: 0,
      Weight: { Lb: 0, Kg: 0 },
    }

    render(
      <ExerciseSetsGridItem
        set={warmupSet}
        index={0}
        sets={[warmupSet]}
        exercise={mockExercise}
        recommendation={mockRecommendation}
        onSetUpdate={mockOnSetUpdate}
        unit="lbs"
        currentSetIndex={0}
        allSetsFinished={false}
      />
    )

    // Verify handlers were captured
    expect(mockOnWeightChange).toBeDefined()
    expect(mockOnRepsChange).toBeDefined()

    // Test weight change - handler should call onSetUpdate with unified 'weight' property
    mockOnWeightChange!(100)
    expect(mockOnSetUpdate).toHaveBeenCalledWith(-1001, { weight: 100 })

    // Test reps change - handler should call onSetUpdate with unified 'reps' property
    mockOnRepsChange!(8)
    expect(mockOnSetUpdate).toHaveBeenCalledWith(-1001, { reps: 8 })
  })
})
