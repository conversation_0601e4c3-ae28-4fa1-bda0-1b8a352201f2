import { Page } from '@playwright/test'

export async function mockApiResponses(page: Page) {
  // Common API response mocking utility
  await page.route('**/api/**', (route) => {
    const url = route.request().url()

    // Mock common responses based on endpoint
    if (url.includes('/GetLogAverageWithSetsV2')) {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Result: true,
          Data: [],
        }),
      })
    } else if (url.includes('/GetUserWorkoutProgramTimeZoneInfo')) {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          GetUserProgramInfoResponseModel: {
            UserId: 'test-user',
            WeeklyStatus: 'Week 1',
            ProgramLabel: 'Test Program',
          },
        }),
      })
    } else {
      route.continue()
    }
  })
}
