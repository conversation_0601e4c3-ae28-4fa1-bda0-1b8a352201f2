import { test, expect } from '@playwright/test'

test.describe('Workout V2 - Save Button Fade Animation', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login and authenticate
    await page.goto('/login')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'Dr123456')
    await page.click('[data-testid="login-button"]')

    // Wait for dashboard
    await expect(page.locator('[data-testid="dashboard"]')).toBeVisible()

    // Start workout
    await page.click('[data-testid="start-workout-button"]')
    await expect(page.locator('[data-testid="exercise-page"]')).toBeVisible()
  })

  test('should show 200ms fade animation on save button click', async ({
    page,
  }) => {
    const inputContainer = page.locator(
      '[data-testid="input-controls-container"]'
    )
    const saveButton = page.locator('text=Save set')

    // Ensure elements are visible
    await expect(inputContainer).toBeVisible()
    await expect(saveButton).toBeVisible()

    // Get initial opacity
    const initialOpacity = await inputContainer.evaluate(
      (el) => window.getComputedStyle(el).opacity
    )
    expect(initialOpacity).toBe('1')

    // Click save button
    await saveButton.click()

    // Immediately check for fade-out effect
    await expect(inputContainer).toHaveClass(/fade-out/)

    // Wait for animation to progress (100ms into 200ms animation)
    await page.waitForTimeout(100)

    // Check opacity is transitioning
    const midOpacity = await inputContainer.evaluate(
      (el) => window.getComputedStyle(el).opacity
    )
    expect(parseFloat(midOpacity)).toBeLessThan(1)

    // Wait for fade-in to start (after 200ms)
    await page.waitForTimeout(150)

    // Should now have fade-in class
    await expect(inputContainer).toHaveClass(/fade-in/)

    // Wait for complete animation (total 400ms)
    await page.waitForTimeout(100)

    // Should be back to full opacity
    const finalOpacity = await inputContainer.evaluate(
      (el) => window.getComputedStyle(el).opacity
    )
    expect(finalOpacity).toBe('1')
  })

  test('should handle rapid button clicks gracefully', async ({ page }) => {
    const saveButton = page.locator('text=Save set')
    const inputContainer = page.locator(
      '[data-testid="input-controls-container"]'
    )

    // Click button multiple times rapidly
    await saveButton.click()
    await saveButton.click()
    await saveButton.click()

    // Should still only have one animation running
    await expect(inputContainer).toHaveClass(/fade-out/)

    // Wait for animation to complete
    await page.waitForTimeout(400)

    // Should end up in normal state
    const finalOpacity = await inputContainer.evaluate(
      (el) => window.getComputedStyle(el).opacity
    )
    expect(finalOpacity).toBe('1')
  })

  test('should work correctly on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    const inputContainer = page.locator(
      '[data-testid="input-controls-container"]'
    )
    const saveButton = page.locator('text=Save set')

    // Ensure elements are still visible on mobile
    await expect(inputContainer).toBeVisible()
    await expect(saveButton).toBeVisible()

    // Test animation on mobile
    await saveButton.click()

    await expect(inputContainer).toHaveClass(/fade-out/)

    await page.waitForTimeout(200)

    await expect(inputContainer).toHaveClass(/fade-in/)
  })

  test('should maintain animation performance under load', async ({ page }) => {
    const saveButton = page.locator('text=Save set')

    // Measure animation timing
    const startTime = Date.now()

    await saveButton.click()

    // Wait for complete animation cycle
    await page.waitForTimeout(400)

    const endTime = Date.now()
    const duration = endTime - startTime

    // Animation should complete within reasonable time (allowing for test overhead)
    expect(duration).toBeLessThan(600) // 400ms animation + 200ms buffer
    expect(duration).toBeGreaterThan(350) // Minimum expected duration
  })
})
