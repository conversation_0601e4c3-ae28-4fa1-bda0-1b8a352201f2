import { test, expect } from '@playwright/test'

// Mobile-first configuration
test.use({
  viewport: { width: 375, height: 667 }, // iPhone SE
  hasTouch: true,
  isMobile: true,
})

test.describe('Warmup Sets Integration', () => {
  test('Exercise page loads without errors with warmup sets', async ({
    page,
  }) => {
    // Mock auth state to bypass login
    await page.addInitScript(() => {
      window.localStorage.setItem(
        'auth-storage',
        JSON.stringify({
          state: {
            token: 'test-token',
            refreshToken: 'test-refresh-token',
            user: { email: '<EMAIL>', name: 'Test User' },
            isAuthenticated: true,
            hasHydrated: true,
          },
          version: 0,
        })
      )
    })

    // Mock API responses
    await page.route('**/api/workout/today*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            Id: 1,
            Label: 'Test Workout',
            WorkoutTemplates: [
              {
                Id: 1,
                Label: 'Test Template',
                Exercices: [
                  {
                    Id: 123,
                    Label: 'Bench Press',
                    IsBodyweight: false,
                    IsTimeBased: false,
                  },
                ],
              },
            ],
          },
        ]),
      })
    })

    await page.route('**/GetRecommendationForExercise*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Series: 3,
          Reps: 10,
          Weight: { Lb: 135, Kg: 61.23 },
          WarmupsCount: 2,
          WarmUpsList: [],
          RecommendationInKg: 61.23,
          OneRMPercentage: 75,
          IsBodyweight: false,
          Increments: { Lb: 2.5, Kg: 1 },
          Min: { Lb: 0, Kg: 0 },
          Max: { Lb: 300, Kg: 136.08 },
        }),
      })
    })

    // Navigate directly to exercise page
    await page.goto('/workout/exercise/123')

    // Wait for page to load
    await page.waitForLoadState('networkidle')

    // Verify warmup sets are displayed without errors
    await expect(page.getByText('Warm-up Sets')).toBeVisible()
    await expect(page.getByTestId('warmup-sets-container')).toBeVisible()

    // Verify no console errors
    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // Wait a bit to catch any delayed errors
    await page.waitForTimeout(1000)

    // Should have no console errors
    expect(consoleErrors).toHaveLength(0)
  })
})
