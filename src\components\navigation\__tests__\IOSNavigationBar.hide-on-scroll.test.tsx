import React from 'react'
import { render, screen } from '@testing-library/react'
import { IOSNavigationBar } from '../IOSNavigationBar'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import '@testing-library/jest-dom'

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  usePathname: () => '/test-page',
}))

// Mock the useScrollDirection hook
const mockUseScrollDirection = vi.fn()
vi.mock('@/hooks/useScrollDirection', () => ({
  useScrollDirection: () => mockUseScrollDirection(),
}))

describe('IOSNavigationBar - Hide on Scroll', () => {
  beforeEach(() => {
    // Default mock return value
    mockUseScrollDirection.mockReturnValue({
      isVisible: true,
      scrollDirection: 'up',
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should be visible by default', () => {
    // Given: Default scroll state
    // When: Component renders
    render(<IOSNavigationBar title="Test Page" />)

    // Then: Navigation should be visible
    const header = screen.getByRole('banner')
    // This will fail until we implement the feature
    expect(header).toBeDefined()
  })

  it('should hide when scrolling down', () => {
    // Given: Scroll direction is down
    mockUseScrollDirection.mockReturnValue({
      isVisible: false,
      scrollDirection: 'down',
    })

    // When: Component renders
    render(<IOSNavigationBar title="Test Page" />)

    // Then: Navigation should be hidden
    const header = screen.getByRole('banner')
    expect(header).toHaveStyle({ transform: 'translateY(-100%)' })
  })

  it('should show when scrolling up', () => {
    // Given: Initially hidden
    const { rerender } = render(<IOSNavigationBar title="Test Page" />)

    mockUseScrollDirection.mockReturnValue({
      isVisible: false,
      scrollDirection: 'down',
    })
    rerender(<IOSNavigationBar title="Test Page" />)

    // When: Scroll direction changes to up
    mockUseScrollDirection.mockReturnValue({
      isVisible: true,
      scrollDirection: 'up',
    })
    rerender(<IOSNavigationBar title="Test Page" />)

    // Then: Navigation should be visible
    const header = screen.getByRole('banner')
    expect(header).toHaveStyle({ transform: 'translateY(0)' })
  })

  it('should have smooth transition when hiding/showing', () => {
    // Given/When: Component renders
    render(<IOSNavigationBar title="Test Page" />)

    // Then: Should have transition class
    const header = screen.getByRole('banner')
    expect(header).toHaveClass('transition-transform')
    expect(header).toHaveClass('duration-300')
  })

  it('should maintain backdrop blur when hidden', () => {
    // Given: Navigation is hidden
    mockUseScrollDirection.mockReturnValue({
      isVisible: false,
      scrollDirection: 'down',
    })

    // When: Component renders
    render(<IOSNavigationBar title="Test Page" />)

    // Then: Should still have backdrop blur class
    const header = screen.getByRole('banner')
    expect(header).toHaveClass('backdrop-blur-md')
  })

  it('should work with exercise page configuration', () => {
    // Given: Exercise page with scroll down
    mockUseScrollDirection.mockReturnValue({
      isVisible: false,
      scrollDirection: 'down',
    })

    // When: Component renders as exercise page
    render(
      <IOSNavigationBar
        title="Bench Press"
        isExercisePage
        setInfo="Set 1 of 3"
        progressValue={33}
        totalSets={3}
        completedSets={1}
      />
    )

    // Then: Should hide with all exercise elements
    const header = screen.getByRole('banner')
    expect(header).toHaveStyle({ transform: 'translateY(-100%)' })
    expect(screen.getByText('Set 1 of 3')).toBeInTheDocument()
  })

  it('should not interfere with other navigation features', () => {
    // Given: Navigation with back button and scroll
    mockUseScrollDirection.mockReturnValue({
      isVisible: false,
      scrollDirection: 'down',
    })

    const onBackClick = vi.fn()

    // When: Component renders
    render(
      <IOSNavigationBar
        title="Test Page"
        showBackButton
        onBackClick={onBackClick}
      />
    )

    // Then: Back button should still be present (even if hidden)
    const backButton = screen.getByRole('button', { name: /go back/i })
    expect(backButton).toBeInTheDocument()
  })

  it('should handle rapid scroll direction changes', () => {
    // Given: Component is rendered
    const { rerender } = render(<IOSNavigationBar title="Test Page" />)

    // When: Rapid scroll direction changes
    const scrollStates = [
      { isVisible: false, scrollDirection: 'down' },
      { isVisible: true, scrollDirection: 'up' },
      { isVisible: false, scrollDirection: 'down' },
      { isVisible: true, scrollDirection: 'up' },
    ]

    scrollStates.forEach((state) => {
      mockUseScrollDirection.mockReturnValue(state)
      rerender(<IOSNavigationBar title="Test Page" />)
    })

    // Then: Should end in correct state
    const header = screen.getByRole('banner')
    expect(header).toHaveStyle({ transform: 'translateY(0)' })
  })

  it('should maintain z-index when hidden', () => {
    // Given: Navigation is hidden
    mockUseScrollDirection.mockReturnValue({
      isVisible: false,
      scrollDirection: 'down',
    })

    // When: Component renders
    render(<IOSNavigationBar title="Test Page" />)

    // Then: Should maintain high z-index
    const header = screen.getByRole('banner')
    expect(header).toHaveClass('z-50')
  })

  it('should disable hide-on-scroll when specified', () => {
    // Given: Scroll down but hide-on-scroll is disabled
    mockUseScrollDirection.mockReturnValue({
      isVisible: false,
      scrollDirection: 'down',
    })

    // When: Component renders with hideOnScroll disabled
    render(<IOSNavigationBar title="Test Page" hideOnScroll={false} />)

    // Then: Should remain visible
    const header = screen.getByRole('banner')
    expect(header).toHaveStyle({ transform: 'translateY(0)' })
  })
})
