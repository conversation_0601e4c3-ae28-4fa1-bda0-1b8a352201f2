import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ProgramStatsGrid } from '../ProgramStatsGrid'
import type { ProgramStats } from '@/types'

describe('ProgramStatsGrid (Simplified)', () => {
  const mockStats: ProgramStats = {
    averageWorkoutTime: 45.5,
    totalVolume: 125000,
    personalRecords: 3,
    consecutiveWeeks: 4,
    lastWorkoutDate: '2024-01-15',
    totalWorkoutsCompleted: 24,
    bodyWeight: 180,
    recoveryDays: 2,
    coachRecommendation: 'Great progress!',
  }

  it('renders 6 stat cards', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    const cards = screen.getAllByTestId(/stat-card/)
    expect(cards).toHaveLength(6)
  })

  it('displays weeks streak', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.getByText('Weeks streak')).toBeInTheDocument()
    expect(screen.getByText('4')).toBeInTheDocument()
  })

  it('displays workouts done', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.getByText('Workouts done')).toBeInTheDocument()
    expect(screen.getByText('24')).toBeInTheDocument()
  })

  it('handles missing stats gracefully', () => {
    render(<ProgramStatsGrid stats={undefined} />)

    // Should show dashes for all 6 cards
    const values = screen.getAllByText('-')
    expect(values).toHaveLength(6)
  })

  it('handles zero consecutive weeks', () => {
    const statsWithZeroWeeks = { ...mockStats, consecutiveWeeks: 0 }
    render(<ProgramStatsGrid stats={statsWithZeroWeeks} />)

    expect(screen.getByText('0')).toBeInTheDocument()
  })

  it('displays lbs lifted with formatting', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.getByText('Lbs lifted')).toBeInTheDocument()
    expect(screen.getByText('125K')).toBeInTheDocument()
  })

  it('shows loading state when isLoading is true', () => {
    render(<ProgramStatsGrid stats={mockStats} isLoading />)

    const skeletons = screen.getAllByTestId(/skeleton/)
    expect(skeletons.length).toBeGreaterThan(0)
  })

  it('uses responsive grid layout', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    const grid = screen.getByTestId('program-stats-grid')
    expect(grid).toHaveClass('grid', 'grid-cols-3')
  })

  it('shows workouts done instead of total workouts', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.getByText('Workouts done')).toBeInTheDocument()
    expect(screen.getByText('24')).toBeInTheDocument()
  })

  it('does not show days completed', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.queryByText('Days Completed')).not.toBeInTheDocument()
  })

  it('does not show current week', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.queryByText('Current Week')).not.toBeInTheDocument()
  })

  it('does not show completion percentage', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.queryByText('Completion')).not.toBeInTheDocument()
    expect(screen.queryByText('%')).not.toBeInTheDocument()
  })

  it('displays body weight and recovery stats', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.getByText('Body weight')).toBeInTheDocument()
    expect(screen.getByText('180')).toBeInTheDocument()

    expect(screen.getByText('Recovery')).toBeInTheDocument()
    expect(screen.getByText('2 days')).toBeInTheDocument()
  })
})
