import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ExercisePageV2Client } from '../ExercisePageV2Client'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { vi } from 'vitest'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    replace: vi.fn(),
  }),
  usePathname: () => '/workout/exercise-v2/1',
}))

// Mock components
vi.mock('@/components/workout-v2/CurrentSetCard', () => ({
  CurrentSetCard: ({ exercise }: any) => (
    <div data-testid="current-set-card">
      Current Set Card - {exercise?.Label || 'No Exercise'}
    </div>
  ),
}))

vi.mock('@/components/workout-v2/NextSetsPreview', () => ({
  NextSetsPreview: ({ nextSets }: any) => (
    <div data-testid="next-sets-preview">
      Next Sets Preview - {nextSets.length} sets
    </div>
  ),
}))

vi.mock('@/components/workout-v2/AllSetsViewV2', () => ({
  AllSetsViewV2: () => <div data-testid="all-sets-view">All Sets View</div>,
}))

vi.mock('@/components/workout-v2/RestTimer', () => ({
  RestTimer: () => <div>Rest Timer</div>,
}))

// Mock hooks
vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    isLoadingWorkout: false,
    workoutError: null,
    workoutSession: { id: 1 },
  }),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    loadingStates: new Map(),
  }),
}))

vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: () => ({
    isInitializing: false,
    loadingError: null,
    retryInitialization: vi.fn(),
  }),
}))

vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: () => ({
    currentExercise: { Id: 1, Label: 'Bench Press' },
    exercises: [{ Id: 1, Label: 'Bench Press' }],
    currentSetIndex: 0,
    isSaving: false,
    saveError: null,
    showComplete: false,
    showExerciseComplete: false,
    recommendation: {
      ExerciseId: 1,
      Reps: 10,
      Weight: { Kg: 50, Lb: 110 },
      Series: 3,
    },
    isLoading: false,
    error: null,
    isLastExercise: false,
    isLastSet: false,
    isWarmup: false,
    isFirstWorkSet: true,
    completedSets: [],
    setData: { reps: 10, weight: 50, duration: 0 },
    setSetData: vi.fn(),
    setSaveError: vi.fn(),
    handleSaveSet: vi.fn(),
    refetchRecommendation: vi.fn(),
  }),
}))

vi.mock('@/hooks/useExerciseV2Actions', () => ({
  useExerciseV2Actions: () => ({
    handleCompleteSet: vi.fn(),
    handleSkipSet: vi.fn(),
  }),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'kg' }),
  }),
}))

vi.mock('@/utils/generateAllSets', () => ({
  generateAllSets: () => [
    {
      Id: 1,
      Reps: 10,
      Weight: { Kg: 50, Lb: 110 },
      IsNext: true,
      IsFinished: false,
      IsWarmups: false,
    },
    {
      Id: 2,
      Reps: 10,
      Weight: { Kg: 50, Lb: 110 },
      IsNext: false,
      IsFinished: false,
      IsWarmups: false,
    },
    {
      Id: 3,
      Reps: 10,
      Weight: { Kg: 50, Lb: 110 },
      IsNext: false,
      IsFinished: false,
      IsWarmups: false,
    },
  ],
}))

describe('ExercisePageV2Client - Hybrid View', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })
  })

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      </QueryClientProvider>
    )
  }

  it('should default to hybrid view showing both current set and next sets preview', () => {
    renderComponent()

    // Should show current set card
    expect(screen.getByTestId('current-set-card')).toBeInTheDocument()

    // Should show next sets preview
    expect(screen.getByTestId('next-sets-preview')).toBeInTheDocument()

    // Should show correct number of next sets (excluding current)
    expect(screen.getByText('Next Sets Preview - 2 sets')).toBeInTheDocument()

    // Toggle button should show "View all sets"
    expect(
      screen.getByRole('button', { name: /view all sets/i })
    ).toBeInTheDocument()
  })

  it('should properly layout hybrid view with 60/30 split', () => {
    const { container } = renderComponent()

    // Find the main content area
    const mainContent = container.querySelector(
      '.flex-1.flex.flex-col.px-4.pt-4.overflow-hidden'
    )
    expect(mainContent).toBeInTheDocument()

    // Find the hybrid view container
    const hybridContainer = mainContent?.querySelector('.flex-1.flex.flex-col')
    expect(hybridContainer).toBeInTheDocument()

    // Check for flex-[6] for current set (60%)
    const currentSetContainer = hybridContainer?.querySelector('.flex-\\[6\\]')
    expect(currentSetContainer).toBeInTheDocument()
    expect(currentSetContainer).toContainElement(
      screen.getByTestId('current-set-card')
    )

    // Check for flex-[3] for next sets (30%)
    const nextSetsContainer = hybridContainer?.querySelector('.flex-\\[3\\]')
    expect(nextSetsContainer).toBeInTheDocument()
    expect(nextSetsContainer).toContainElement(
      screen.getByTestId('next-sets-preview')
    )
  })

  it('should filter out finished and current sets from next sets preview', () => {
    renderComponent()

    // The preview should show 2 sets (total 3 sets minus 1 current set)
    expect(screen.getByText('Next Sets Preview - 2 sets')).toBeInTheDocument()
  })

  it('should switch between view modes correctly', async () => {
    const user = userEvent.setup()
    renderComponent()

    // Start in hybrid view
    expect(screen.getByTestId('current-set-card')).toBeInTheDocument()
    expect(screen.getByTestId('next-sets-preview')).toBeInTheDocument()

    // Switch to all sets view
    await user.click(screen.getByRole('button', { name: /view all sets/i }))
    await waitFor(() => {
      expect(screen.getByTestId('all-sets-view')).toBeInTheDocument()
      expect(screen.queryByTestId('current-set-card')).not.toBeInTheDocument()
      expect(screen.queryByTestId('next-sets-preview')).not.toBeInTheDocument()
    })

    // Switch to minimal view
    await user.click(screen.getByRole('button', { name: /minimal view/i }))
    await waitFor(() => {
      expect(screen.getByTestId('current-set-card')).toBeInTheDocument()
      expect(screen.queryByTestId('next-sets-preview')).not.toBeInTheDocument()
      expect(screen.queryByTestId('all-sets-view')).not.toBeInTheDocument()
    })

    // Switch back to hybrid view
    await user.click(screen.getByRole('button', { name: /hybrid view/i }))
    await waitFor(() => {
      expect(screen.getByTestId('current-set-card')).toBeInTheDocument()
      expect(screen.getByTestId('next-sets-preview')).toBeInTheDocument()
      expect(screen.queryByTestId('all-sets-view')).not.toBeInTheDocument()
    })
  })
})
