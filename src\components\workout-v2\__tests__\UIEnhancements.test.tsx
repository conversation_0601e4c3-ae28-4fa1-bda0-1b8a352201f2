import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { CurrentSetCard } from '../CurrentSetCard'
import { RestTimer } from '../RestTimer'
import type { SetLogModel, ExerciseModel } from '@/types'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

// Mock workout store for RestTimer
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    restTimerState: { isActive: true, duration: 60 },
    setRestTimerState: vi.fn(),
  }),
}))

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, className, ...props }: any) => (
      <div className={className} data-testid="motion-div" {...props}>
        {children}
      </div>
    ),
  },
  AnimatePresence: ({ children }: any) => children,
  useAnimation: () => ({
    start: vi.fn(),
  }),
}))

describe('UI Enhancements', () => {
  const mockSet: SetLogModel = {
    Id: 1,
    WorkoutId: 1,
    ExerciseId: 1,
    IsWarmup: false,
    Weight: 100,
    Reps: 10,
    RepsDisplay: 10,
    WeightDisplay: 100,
    SetOrder: 1,
    IsFinished: false,
  }

  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsFinished: false,
  }

  describe('CurrentSetCard - Gold Gradient Button', () => {
    it('should apply gold gradient to save set button', () => {
      render(
        <CurrentSetCard
          exercise={mockExercise}
          currentSet={mockSet}
          setData={{ weight: 100, reps: 10 }}
          onSetDataChange={vi.fn()}
          onComplete={vi.fn()}
          onSkip={vi.fn()}
          isSaving={false}
          completedSets={0}
          totalSets={3}
          unit="kg"
        />
      )

      const saveButton = screen.getByText('Save set')
      expect(saveButton.className).toContain('bg-gradient-to-r')
      expect(saveButton.className).toContain('from-brand-gold-start')
      expect(saveButton.className).toContain('to-brand-gold-end')
    })
  })

  describe('CurrentSetCard - Updated Swipe Text', () => {
    it('should show updated swipe instruction text', () => {
      render(
        <CurrentSetCard
          exercise={mockExercise}
          currentSet={mockSet}
          setData={{ weight: 100, reps: 10 }}
          onSetDataChange={vi.fn()}
          onComplete={vi.fn()}
          onSkip={vi.fn()}
          isSaving={false}
          completedSets={0}
          totalSets={3}
          unit="kg"
        />
      )

      const swipeText = screen.getByText(
        'Swipe left to skip · right to complete'
      )
      expect(swipeText).toBeInTheDocument()
    })
  })

  describe('CurrentSetCard - Bigger Chevrons', () => {
    it('should have larger chevron icons', () => {
      const { container } = render(
        <CurrentSetCard
          exercise={mockExercise}
          currentSet={mockSet}
          setData={{ weight: 100, reps: 10 }}
          onSetDataChange={vi.fn()}
          onComplete={vi.fn()}
          onSkip={vi.fn()}
          isSaving={false}
          completedSets={0}
          totalSets={3}
          unit="kg"
        />
      )

      // Check chevron size classes
      const chevrons = container.querySelectorAll('[data-testid*="chevron"]')
      chevrons.forEach((chevron) => {
        expect(chevron.className).toContain('w-9')
        expect(chevron.className).toContain('h-9')
      })
    })
  })

  describe('CurrentSetCard - Bigger Numbers', () => {
    it('should have larger text for set numbers', () => {
      const { container } = render(
        <CurrentSetCard
          exercise={mockExercise}
          currentSet={mockSet}
          setData={{ weight: 100, reps: 10 }}
          onSetDataChange={vi.fn()}
          onComplete={vi.fn()}
          onSkip={vi.fn()}
          isSaving={false}
          completedSets={0}
          totalSets={3}
          unit="kg"
        />
      )

      // Check number display text size
      const numberDisplays = container.querySelectorAll('.text-6xl')
      expect(numberDisplays.length).toBe(2) // reps and weight
      numberDisplays.forEach((display) => {
        expect(display.className).toContain('text-6xl')
      })
    })
  })

  describe('RestTimer - Full Width', () => {
    it('should be full width at bottom', () => {
      const { container } = render(<RestTimer />)

      const timerOverlay = container.querySelector('.fixed.bottom-0')
      expect(timerOverlay?.className).toContain('left-0')
      expect(timerOverlay?.className).toContain('right-0')
      expect(timerOverlay?.className).not.toContain('mx-4')
      expect(timerOverlay?.className).not.toContain('rounded-t-2xl')

      // Check skip button has gold gradient
      const skipButton = screen.getByText('Skip')
      expect(skipButton.className).toContain('bg-gradient-to-r')
      expect(skipButton.className).toContain('from-brand-gold-start')
      expect(skipButton.className).toContain('to-brand-gold-end')
    })
  })
})
