/* eslint-disable no-console */
import { test, expect } from '@playwright/test'
import { safePageClose } from './helpers/safe-cleanup'
// eslint-disable-next-line import/extensions
import { login } from './helpers'
// eslint-disable-next-line import/extensions
import {
  mockWorkoutAPI,
  mockRecommendations,
  mockExerciseSets,
} from './helpers/workout-mocks'

const testUser = {
  email: '<EMAIL>',
  password: 'Dr123456',
}

test.describe('Warm-up Sets Display', () => {
  test.afterEach(async ({ page }) => {
    console.log('🧹 [WARMUP-SETS-AFTEREACH] Starting cleanup...')

    try {
      // Use defensive checks before accessing page properties
      if (page && !page.isClosed()) {
        console.log(
          `🌐 [WARMUP-SETS-AFTEREACH] Context state: ${page.context().pages().length} pages`
        )
        console.log(`📄 [WARMUP-SETS-AFTEREACH] Page URL: ${page.url()}`)
      }

      // Use safe cleanup helper
      await safePageClose(page)
      console.log('✅ [WARMUP-SETS-AFTEREACH] Page closed successfully')
    } catch (error) {
      console.error('❌ [WARMUP-SETS-AFTEREACH] Error during cleanup:', error)
    }
  })
  test.beforeEach(async ({ page }) => {
    // Set up authentication
    await page.addInitScript(() => {
      localStorage.setItem('app-hydrated', 'true')
    })

    // Login with test credentials
    await page.goto('/login')
    await login(page, testUser.email, testUser.password)
  })

  test('should display warm-up sets before work sets', async ({ page }) => {
    // Mock workout API
    await mockWorkoutAPI(page)

    // Mock recommendation with warmup sets
    await mockRecommendations(page, 1, {
      WarmupsCount: 2,
      Series: 3,
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.23 },
    })

    // Mock exercise sets (empty - will be created from recommendation)
    await mockExerciseSets(page, 1, [])

    // Navigate to workout
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Click on first exercise
    // Click on first exercise card
    await page.click('[data-testid="exercise-card"]:first-child')
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Wait for the exercise sets grid to be visible
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Verify warm-up sets are displayed in the grid (W1, W2, etc.)
    const warmupSet1 = page.locator('[data-testid="set-cell"]:has-text("W1")')
    const warmupSet2 = page.locator('[data-testid="set-cell"]:has-text("W2")')

    await expect(warmupSet1).toBeVisible()
    await expect(warmupSet2).toBeVisible()

    // Verify warm-up sets have appropriate weights
    const firstWarmupRow = warmupSet1.locator('..')
    const secondWarmupRow = warmupSet2.locator('..')

    // First warmup might be bodyweight or light weight
    await expect(firstWarmupRow).toContainText(/\d+/) // Should have reps

    // Second warmup should have weight
    await expect(secondWarmupRow).toContainText(/\d+/) // Should have reps
    await expect(secondWarmupRow).toContainText(/lbs|kg/)
  })

  test('should position warm-up sets before exercise info', async ({
    page,
  }) => {
    // Mock workout API
    await mockWorkoutAPI(page)

    // Mock recommendation with warmup sets
    await mockRecommendations(page, 1, {
      WarmupsCount: 2,
    })

    // Mock exercise sets
    await mockExerciseSets(page, 1, [])

    // Navigate to exercise
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]')
    // Click on first exercise card
    await page.click('[data-testid="exercise-card"]:first-child')
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Wait for the exercise sets grid
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Get warm-up sets and work sets
    const warmupSet1 = await page
      .locator('[data-testid="set-cell"]:has-text("W1")')
      .boundingBox()
    const workSet1 = await page
      .locator('[data-testid="set-cell"]:has-text("1")')
      .boundingBox()

    // Verify warmup sets appear before work sets
    expect(warmupSet1).not.toBeNull()
    expect(workSet1).not.toBeNull()
    expect(warmupSet1!.y).toBeLessThan(workSet1!.y)
  })

  test('should not display warm-up sets for bodyweight exercises without warmups', async ({
    page,
  }) => {
    // Mock workout API
    await mockWorkoutAPI(page)

    // Mock bodyweight recommendation
    await mockRecommendations(page, 1, {
      WarmupsCount: 0,
      IsBodyweight: true,
      Weight: { Lb: 0, Kg: 0 },
    })

    // Mock exercise sets
    await mockExerciseSets(page, 1, [])

    // Navigate to exercise
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]')
    // Click on first exercise card
    await page.click('[data-testid="exercise-card"]:first-child')
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Wait for the exercise sets grid
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Verify no warm-up sets are displayed (no W1, W2, etc.)
    const warmupSets = await page
      .locator('[data-testid="set-cell"]:has-text(/^W\\d+$/)')
      .count()
    expect(warmupSets).toBe(0)
  })

  test('should have mobile-friendly touch targets', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Mock workout API
    await mockWorkoutAPI(page)

    // Mock recommendation with warmup sets
    await mockRecommendations(page, 1, {
      WarmupsCount: 2,
    })

    // Mock exercise sets
    await mockExerciseSets(page, 1, [])

    // Navigate to exercise
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]')
    // Click on first exercise card
    await page.click('[data-testid="exercise-card"]:first-child')
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Wait for the exercise sets grid
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Check warmup set dimensions
    const warmupSetCell = await page
      .locator('[data-testid="set-cell"]:has-text("W1")')
      .boundingBox()
    expect(warmupSetCell).not.toBeNull()

    // Verify minimum height for touch targets (44px)
    expect(warmupSetCell!.height).toBeGreaterThanOrEqual(44)
  })
})
