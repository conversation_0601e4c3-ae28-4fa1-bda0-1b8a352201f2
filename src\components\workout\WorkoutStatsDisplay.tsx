'use client'

import { StreakIcon } from '@/components/icons'
import type { WorkoutSession } from '@/types'

interface WorkoutStats {
  totalExercises: number
  totalSets: number
  workingSets: number
  totalVolume: number
  avgRir: number | null
}

interface WorkoutStatsDisplayProps {
  stats: WorkoutStats | null
  userStats: {
    weekStreak: number
    workoutsCompleted: number
    lbsLifted: number
  } | null
  isLoadingStats: boolean
}

export function calculateWorkoutStats(
  session: WorkoutSession | null
): WorkoutStats | null {
  if (!session) return null

  const totalSets = session.exercises.reduce(
    (sum, exercise) =>
      sum + exercise.sets.filter((set) => !set.isWarmup).length,
    0
  )

  const workingSets = session.exercises.reduce(
    (sum, exercise) =>
      sum + exercise.sets.filter((set) => !set.isWarmup).length,
    0
  )

  const totalVolume = session.exercises.reduce(
    (sum, exercise) =>
      sum +
      exercise.sets
        .filter((set) => !set.isWarmup)
        .reduce((setSum, set) => setSum + set.reps * set.weight.Lb, 0),
    0
  )

  // Calculate average RIR from sets that have RIR data
  const rirValues = session.exercises.flatMap((exercise) =>
    exercise.sets
      .filter((set) => set.rir !== undefined)
      .map((set) => set.rir as number)
  )
  const avgRir =
    rirValues.length > 0
      ? rirValues.reduce((sum, rir) => sum + rir, 0) / rirValues.length
      : null

  return {
    totalExercises: session.exercises.length,
    totalSets,
    workingSets,
    totalVolume,
    avgRir,
  }
}

export function formatVolume(volume: number): string {
  // Format with comma separator
  return `${volume.toLocaleString('en-US')} lbs`
}

export function WorkoutStatsDisplay({
  stats,
  userStats,
  isLoadingStats,
}: WorkoutStatsDisplayProps) {
  return (
    <div className="bg-bg-secondary rounded-theme shadow-theme-md p-6 space-y-4">
      {/* Exercises */}
      <div className="flex justify-between items-center">
        <span className="text-text-secondary">Exercises</span>
        <span
          className="font-medium text-text-primary"
          data-testid="total-exercises"
        >
          {stats?.totalExercises || 0}
        </span>
      </div>

      {/* Sets */}
      <div className="flex justify-between items-center">
        <span className="text-text-secondary">Sets</span>
        <span
          className="font-medium text-text-primary"
          data-testid="total-sets"
        >
          {stats?.totalSets || 0}
        </span>
      </div>

      {/* Total Volume */}
      <div className="flex justify-between items-center">
        <span className="text-text-secondary">Total Volume</span>
        <span className="font-medium text-text-primary">
          {stats ? formatVolume(stats.totalVolume) : '0 lbs'}
        </span>
      </div>

      {/* Average RIR */}
      {stats && stats.avgRir !== null && stats.avgRir !== undefined && (
        <div className="flex justify-between items-center">
          <span className="text-text-secondary">Avg RIR</span>
          <span className="font-medium text-text-primary">
            {stats.avgRir.toFixed(1)}
          </span>
        </div>
      )}

      {/* Workout Streak */}
      <div className="flex justify-between items-center">
        <span className="text-text-secondary flex items-center gap-2">
          <StreakIcon
            size={18}
            className="text-brand-primary"
            data-testid="streak-icon"
          />
          Workout Streak
        </span>
        <span className="font-medium text-text-primary">
          {isLoadingStats
            ? 'Loading...'
            : `${userStats?.weekStreak || 0} ${userStats?.weekStreak === 1 ? 'week' : 'weeks'}`}
        </span>
      </div>
    </div>
  )
}
