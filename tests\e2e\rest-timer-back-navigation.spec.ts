import { test, expect, Page } from '@playwright/test'
// eslint-disable-next-line import/extensions
import { login } from './helpers'

// Mock workout data for tests
const mockWorkout = {
  Id: 123,
  UserId: 'test-user-id',
  DateStarted: new Date().toISOString(),
  Exercises: [
    {
      Id: 1,
      Name: 'Bench Press',
      Sets: 3,
      Completed: false,
    },
    {
      Id: 2,
      Name: 'Incline Dumbbell Press',
      Sets: 3,
      Completed: false,
    },
  ],
}

// Helper function to login and navigate to workout
async function loginAndNavigateToWorkout(page: Page) {
  await page.goto('/login')
  await login(page, '<EMAIL>', 'Dr123456')
  await page.goto('/workout')
}

test.describe('Rest Timer Back Navigation', () => {
  test.beforeEach(async ({ page }) => {
    // Mock workout data
    await page.route('**/me/workout', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockWorkout),
      })
    })

    // Mock exercise recommendation
    await page.route('**/me/recommendation-for-exercise', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Reps: 10,
          Weight: 100,
          LogReps: 10,
          LogWeight: 100,
          RestSeconds: 60,
          RestAfterExerciseSeconds: 90,
          TUL: null,
          SetNumber: 1,
        }),
      })
    })

    // Mock set creation
    await page.route('**/me/workouts/*/exercises/*/sets', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Id: 'set-123',
            Reps: 10,
            Weight: 100,
            RIR: 0,
            IsComplete: true,
          }),
        })
      }
    })
  })

  test('should navigate back to exercise page when tapping back during rest between sets', async ({
    page,
  }) => {
    await loginAndNavigateToWorkout(page)

    // Navigate to first exercise
    const firstExercise = mockWorkout.Exercises[0]
    await page.goto(`/workout/exercise/${firstExercise.Id}`)
    await expect(page).toHaveURL(/\/workout\/exercise(-v2)?\/\d+/)

    // Complete a set and go to rest timer
    await page.getByRole('button', { name: /log.*set/i }).click()
    await page.waitForURL(/\/workout\/rest-timer\?between-sets=true/)

    // Verify we're on the rest timer page
    await expect(page.getByText(/rest/i)).toBeVisible()
    await expect(page.getByRole('timer')).toBeVisible()

    // Click the back button
    await page.getByRole('button', { name: /back/i }).click()

    // Should navigate back to the exercise page, not workout page (supports V1 and V2)
    await expect(page).toHaveURL(/\/workout\/exercise(-v2)?\/\d+/)
    await expect(page.getByText(firstExercise.Name)).toBeVisible()
  })

  test('should navigate back to workout page when tapping back during rest between exercises', async ({
    page,
  }) => {
    await loginAndNavigateToWorkout(page)

    // Navigate to first exercise
    const firstExercise = mockWorkout.Exercises[0]
    await page.goto(`/workout/exercise/${firstExercise.Id}`)

    // Mock that this is the last set
    await page.evaluate(() => {
      const store = (window as any).useWorkoutStore.getState()
      store.currentSetIndex = 2 // Last set (0-indexed, 3 sets total)
    })

    // Complete the last set and go to rest timer between exercises
    await page.getByRole('button', { name: /log.*set/i }).click()
    await page.waitForURL(/\/workout\/rest-timer/)

    // Verify we're on the rest timer page
    await expect(page.getByText(/rest/i)).toBeVisible()
    await expect(page.getByRole('timer')).toBeVisible()

    // Click the back button
    await page.getByRole('button', { name: /back/i }).click()

    // For rest between exercises, it's reasonable to go back to workout overview
    await expect(page).toHaveURL('/workout')
  })

  test('should handle back navigation correctly when navigating directly to rest timer', async ({
    page,
  }) => {
    await loginAndNavigateToWorkout(page)

    // Navigate directly to rest timer with between-sets parameter
    await page.goto('/workout/rest-timer?between-sets=true')

    // Click the back button
    await page.getByRole('button', { name: /back/i }).click()

    // Should navigate to workout page as fallback when no previous exercise
    await expect(page).toHaveURL('/workout')
  })
})
