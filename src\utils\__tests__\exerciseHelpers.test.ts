import { describe, it, expect } from 'vitest'
import { ExerciseHelpers } from '../exerciseHelpers'
import type { UserSettings } from '../exerciseHelpers'

describe('ExerciseHelpers', () => {
  describe('getExerciseInfo', () => {
    it('should identify bodyweight exercises', () => {
      const exercises = [
        'Push-up',
        'Pull-up',
        'Chin-up',
        'Dip',
        'Bodyweight Squat',
        'Mountain Climber',
      ]

      exercises.forEach((exercise) => {
        const info = ExerciseHelpers.getExerciseInfo(exercise)
        expect(info.isBodyweight).toBe(true)
        expect(info.isWeighted).toBe(false)
        expect(info.exerciseType).toBe('bodyweight')
      })
    })

    it('should identify barbell exercises', () => {
      const exercises = [
        'Barbell Bench Press',
        'Squat',
        'Deadlift',
        'Overhead Press',
        'Barbell Row',
      ]

      exercises.forEach((exercise) => {
        const info = ExerciseHelpers.getExerciseInfo(exercise)
        expect(info.isBodyweight).toBe(false)
        expect(info.isWeighted).toBe(true)
        expect(info.usesPlates).toBe(true)
        expect(info.exerciseType).toBe('barbell')
      })
    })

    it('should identify dumbbell exercises', () => {
      const exercises = [
        'Dumbbell Bench Press',
        'DB Curl',
        'Dumbell Fly', // Test misspelling
      ]

      exercises.forEach((exercise) => {
        const info = ExerciseHelpers.getExerciseInfo(exercise)
        expect(info.isBodyweight).toBe(false)
        expect(info.isWeighted).toBe(true)
        expect(info.exerciseType).toBe('dumbbell')
      })
    })

    it('should identify cable exercises', () => {
      const exercises = ['Cable Row', 'Lat Pulldown', 'Cable Fly', 'Face Pull']

      exercises.forEach((exercise) => {
        const info = ExerciseHelpers.getExerciseInfo(exercise)
        expect(info.isBodyweight).toBe(false)
        expect(info.isWeighted).toBe(true)
        expect(info.exerciseType).toBe('cable')
      })
    })

    it('should default to machine for unknown exercises', () => {
      const info = ExerciseHelpers.getExerciseInfo('Unknown Exercise')
      expect(info.isBodyweight).toBe(false)
      expect(info.isWeighted).toBe(true)
      expect(info.exerciseType).toBe('machine')
    })
  })

  describe('getBarbellWeight', () => {
    it('should return user setting when provided', () => {
      const userSettings: UserSettings = {
        unit: 'kg',
        barbellWeight: 25,
      }

      const weight = ExerciseHelpers.getBarbellWeight(
        'Bench Press',
        userSettings
      )
      expect(weight).toBe(25)
    })

    it('should return standard barbell weight by unit', () => {
      const kgSettings: UserSettings = { unit: 'kg' }
      const lbsSettings: UserSettings = { unit: 'lbs' }

      expect(ExerciseHelpers.getBarbellWeight('Bench Press', kgSettings)).toBe(
        20
      )
      expect(ExerciseHelpers.getBarbellWeight('Bench Press', lbsSettings)).toBe(
        45
      )
    })

    it('should return EZ bar weight for curl exercises', () => {
      const kgSettings: UserSettings = { unit: 'kg' }
      const lbsSettings: UserSettings = { unit: 'lbs' }

      expect(ExerciseHelpers.getBarbellWeight('EZ Bar Curl', kgSettings)).toBe(
        7
      )
      expect(
        ExerciseHelpers.getBarbellWeight('Curl Bar Curl', lbsSettings)
      ).toBe(15)
    })

    it('should return trap bar weight', () => {
      const kgSettings: UserSettings = { unit: 'kg' }
      const lbsSettings: UserSettings = { unit: 'lbs' }

      expect(
        ExerciseHelpers.getBarbellWeight('Trap Bar Deadlift', kgSettings)
      ).toBe(25)
      expect(
        ExerciseHelpers.getBarbellWeight('Hex Bar Deadlift', lbsSettings)
      ).toBe(55)
    })
  })

  describe('getDefaultIncrement', () => {
    it('should return 1 for bodyweight exercises', () => {
      expect(ExerciseHelpers.getDefaultIncrement('Push-up', true)).toBe(1)
      expect(ExerciseHelpers.getDefaultIncrement('Pull-up', false)).toBe(1)
    })

    it('should return plate increments for barbell exercises', () => {
      expect(ExerciseHelpers.getDefaultIncrement('Bench Press', true)).toBe(1)
      expect(ExerciseHelpers.getDefaultIncrement('Squat', false)).toBe(2.5)
    })

    it('should return smaller increments for dumbbell exercises', () => {
      expect(ExerciseHelpers.getDefaultIncrement('Dumbbell Press', true)).toBe(
        1
      )
      expect(ExerciseHelpers.getDefaultIncrement('DB Curl', false)).toBe(2.5)
    })

    it('should return machine increments for other exercises', () => {
      expect(ExerciseHelpers.getDefaultIncrement('Leg Press', true)).toBe(1.25)
      expect(ExerciseHelpers.getDefaultIncrement('Cable Row', false)).toBe(2.5)
    })
  })

  describe('estimatePlateAvailability', () => {
    it('should return false for bodyweight exercises', () => {
      expect(ExerciseHelpers.estimatePlateAvailability('Push-up')).toBe(false)
    })

    it('should return true for barbell exercises', () => {
      expect(ExerciseHelpers.estimatePlateAvailability('Bench Press')).toBe(
        true
      )
      expect(ExerciseHelpers.estimatePlateAvailability('Squat')).toBe(true)
    })

    it('should return false for non-barbell exercises', () => {
      expect(ExerciseHelpers.estimatePlateAvailability('Dumbbell Press')).toBe(
        false
      )
      expect(ExerciseHelpers.estimatePlateAvailability('Cable Row')).toBe(false)
      expect(ExerciseHelpers.estimatePlateAvailability('Leg Press')).toBe(false)
    })
  })

  describe('getAvailablePlates', () => {
    it('should return user setting when provided', () => {
      const userSettings: UserSettings = {
        unit: 'kg',
        availablePlates: '20,10,5,2.5',
      }

      expect(ExerciseHelpers.getAvailablePlates(userSettings)).toBe(
        '20,10,5,2.5'
      )
    })

    it('should return default kg plates', () => {
      const userSettings: UserSettings = { unit: 'kg' }
      expect(ExerciseHelpers.getAvailablePlates(userSettings)).toBe(
        '20,15,10,5,2.5,1.25,0.5,0.25'
      )
    })

    it('should return default lb plates', () => {
      const userSettings: UserSettings = { unit: 'lbs' }
      expect(ExerciseHelpers.getAvailablePlates(userSettings)).toBe(
        '45,35,25,10,5,2.5'
      )
    })
  })

  describe('parseAvailablePlates', () => {
    it('should parse comma-separated plate weights', () => {
      const result = ExerciseHelpers.parseAvailablePlates('45,25,10,5,2.5')
      expect(result).toEqual([45, 25, 10, 5, 2.5])
    })

    it('should return empty array for "all"', () => {
      const result = ExerciseHelpers.parseAvailablePlates('all')
      expect(result).toEqual([])
    })

    it('should handle empty string', () => {
      const result = ExerciseHelpers.parseAvailablePlates('')
      expect(result).toEqual([])
    })

    it('should filter out invalid values', () => {
      const result = ExerciseHelpers.parseAvailablePlates(
        '45,invalid,25,NaN,10'
      )
      expect(result).toEqual([45, 25, 10])
    })

    it('should sort plates in descending order', () => {
      const result = ExerciseHelpers.parseAvailablePlates('5,45,10,25')
      expect(result).toEqual([45, 25, 10, 5])
    })
  })

  describe('shouldUsePlateCalculations', () => {
    it('should return true for barbell exercises', () => {
      expect(ExerciseHelpers.shouldUsePlateCalculations('Bench Press')).toBe(
        true
      )
      expect(ExerciseHelpers.shouldUsePlateCalculations('Squat')).toBe(true)
    })

    it('should return false for non-barbell exercises', () => {
      expect(ExerciseHelpers.shouldUsePlateCalculations('Push-up')).toBe(false)
      expect(ExerciseHelpers.shouldUsePlateCalculations('Dumbbell Press')).toBe(
        false
      )
      expect(ExerciseHelpers.shouldUsePlateCalculations('Cable Row')).toBe(
        false
      )
    })
  })
})
