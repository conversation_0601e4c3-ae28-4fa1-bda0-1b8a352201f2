import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { RepsInput } from '../RepsInput'

const defaultProps = {
  reps: 10,
  onChange: vi.fn(),
  onIncrement: vi.fn(),
  onDecrement: vi.fn(),
}

describe('RepsInput - Updated styling', () => {
  it('should display uppercase "REPS" label without italic styling', () => {
    render(<RepsInput {...defaultProps} />)

    const label = screen.getByText('REPS')
    expect(label).toBeInTheDocument()
    expect(label).not.toHaveClass('italic')
    expect(label).toHaveClass('text-text-secondary')
  })

  it('should not display lowercase "reps" label', () => {
    render(<RepsInput {...defaultProps} />)

    expect(screen.queryByText('reps')).not.toBeInTheDocument()
  })

  it('should have label with proper styling', () => {
    render(<RepsInput {...defaultProps} />)

    const label = screen.getByText('REPS')
    // Should have small size and secondary color but no italic
    expect(label).toHaveClass('text-sm')
    expect(label).toHaveClass('text-text-secondary')
    expect(label).not.toHaveClass('italic')
  })
})
