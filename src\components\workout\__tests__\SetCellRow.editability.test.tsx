import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SetCellRow } from '../SetCellRow'

describe('SetCellRow - Finished Sets Editability', () => {
  const baseProps = {
    setNo: 1,
    reps: 10,
    isFinished: false,
    isBodyweight: false,
    setType: 'Normal' as const,
    onRepsChange: vi.fn(),
    onWeightChange: vi.fn(),
    onSetComplete: vi.fn(),
    onSetTypeBadgeClick: vi.fn(),
    getFormattedWeight: () => '135',
  }

  it('should allow editing reps input when set is finished', async () => {
    // Test that finished sets remain editable (not disabled)
    const user = userEvent.setup()
    const onRepsChange = vi.fn()

    render(<SetCellRow {...baseProps} isFinished onRepsChange={onRepsChange} />)

    const repsInput = screen.getByLabelText('Reps for set 1')

    // Verify input is NOT disabled
    expect(repsInput).not.toBeDisabled()

    // Verify user can interact with the input
    await user.clear(repsInput)
    await user.type(repsInput, '12')

    // onRepsChange should be called
    expect(onRepsChange).toHaveBeenCalled()
  })

  it('should allow editing weight input when set is finished', async () => {
    // Test that finished sets remain editable (not disabled)
    const user = userEvent.setup()
    const onWeightChange = vi.fn()

    render(
      <SetCellRow {...baseProps} isFinished onWeightChange={onWeightChange} />
    )

    const weightInput = screen.getByLabelText('Weight for set 1')

    // Verify input is NOT disabled
    expect(weightInput).not.toBeDisabled()

    // Verify user can interact with the input
    await user.clear(weightInput)
    await user.type(weightInput, '145')

    // onWeightChange should be called
    expect(onWeightChange).toHaveBeenCalled()
  })

  it('should keep weight input disabled for bodyweight exercises regardless of finished state', () => {
    // Bodyweight exercises should always have weight input disabled
    render(<SetCellRow {...baseProps} isFinished isBodyweight />)

    const weightInput = screen.getByLabelText('Weight for set 1')

    // Weight input should be disabled for bodyweight exercises
    expect(weightInput).toBeDisabled()
  })

  it('should show check icon for finished sets', () => {
    render(<SetCellRow {...baseProps} isFinished />)

    const checkIcon = screen.getByTestId('check-icon')
    expect(checkIcon).toBeInTheDocument()
    expect(checkIcon).toHaveClass('text-success')
  })

  it('should allow clicking check icon to toggle completion', async () => {
    const user = userEvent.setup()
    const onSetComplete = vi.fn()

    render(
      <SetCellRow {...baseProps} isFinished onSetComplete={onSetComplete} />
    )

    const checkIcon = screen.getByTestId('check-icon')
    await user.click(checkIcon)

    expect(onSetComplete).toHaveBeenCalledOnce()
  })

  it('should maintain visual styling for finished sets', () => {
    render(<SetCellRow {...baseProps} isFinished />)

    const repsInput = screen.getByLabelText('Reps for set 1')
    const weightInput = screen.getByLabelText('Weight for set 1')

    // Inputs should have the finished styling classes available (even if not disabled)
    expect(repsInput).toHaveClass(
      'disabled:bg-bg-tertiary',
      'disabled:text-text-tertiary'
    )
    expect(weightInput).toHaveClass(
      'disabled:bg-bg-tertiary',
      'disabled:text-text-tertiary'
    )
  })

  it('should handle warm-up sets (setNo="W") when finished', async () => {
    const user = userEvent.setup()
    const onRepsChange = vi.fn()
    const onWeightChange = vi.fn()

    render(
      <SetCellRow
        {...baseProps}
        setNo="W"
        isFinished
        onRepsChange={onRepsChange}
        onWeightChange={onWeightChange}
      />
    )

    const repsInput = screen.getByLabelText('Reps for set W')
    const weightInput = screen.getByLabelText('Weight for set W')

    // Both inputs should be editable
    expect(repsInput).not.toBeDisabled()
    expect(weightInput).not.toBeDisabled()

    // Test interaction
    await user.clear(repsInput)
    await user.type(repsInput, '5')
    await user.clear(weightInput)
    await user.type(weightInput, '95')

    expect(onRepsChange).toHaveBeenCalled()
    expect(onWeightChange).toHaveBeenCalled()
  })
})
