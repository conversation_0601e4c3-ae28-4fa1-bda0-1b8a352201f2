'use client'

import { useEffect, useState } from 'react'
import { SuccessIcon } from './SuccessIcon'

interface ExerciseTransitionScreenProps {
  exerciseName?: string
  onComplete: () => void
}

export function ExerciseTransitionScreen({
  exerciseName,
  onComplete,
}: ExerciseTransitionScreenProps) {
  const [showCheckmark, setShowCheckmark] = useState(true)
  const [showExerciseName, setShowExerciseName] = useState(false)

  useEffect(() => {
    // Hide checkmark after 400ms
    const hideCheckmarkTimer = setTimeout(() => setShowCheckmark(false), 400)
    // Show exercise name at 400ms
    const exerciseNameTimer = setTimeout(() => setShowExerciseName(true), 400)
    // Complete at 800ms using requestAnimationFrame to avoid XHR violation
    const completeTimer = setTimeout(() => {
      // Use requestAnimationFrame to avoid XHR violation during DOM updates
      // This prevents synchronous XHR requests during layout/paint operations
      if (typeof requestAnimationFrame !== 'undefined') {
        requestAnimationFrame(() => {
          onComplete()
        })
      } else {
        // Fallback for environments without requestAnimationFrame
        onComplete()
      }
    }, 800)

    return () => {
      clearTimeout(hideCheckmarkTimer)
      clearTimeout(exerciseNameTimer)
      clearTimeout(completeTimer)
    }
  }, [onComplete])

  return (
    <div
      data-testid="exercise-transition-screen"
      className="min-h-[100dvh] flex flex-col items-center justify-center bg-bg-primary px-6"
    >
      {showCheckmark && (
        <div data-testid="success-icon-wrapper">
          <SuccessIcon size={120} className="animate-fade-in" />
        </div>
      )}
      {showExerciseName && (
        <p className="text-xl font-medium text-text-primary animate-fade-in">
          {exerciseName || 'Loading exercise...'}
        </p>
      )}
    </div>
  )
}
