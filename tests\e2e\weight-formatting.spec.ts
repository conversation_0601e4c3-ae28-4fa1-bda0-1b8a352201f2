import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from '../fixtures/auth-helper'
import { MOCK_ENDPOINTS } from '../config/api-endpoints'
import { mockWorkoutData } from '../fixtures/workout-fixtures'

test.describe('Weight Formatting', () => {
  test.beforeEach(async ({ page, context }) => {
    await setupAuthenticatedUser(context)

    // Mock workout data with various weight values
    await page.route(
      MOCK_ENDPOINTS.workout.getUserWorkoutProgramInfo,
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            StatusCode: 200,
            Result: mockWorkoutData.programInfo,
          }),
        })
      }
    )

    // Mock customized workout with exercises
    await page.route(
      MOCK_ENDPOINTS.workout.getCustomizedWorkout,
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            StatusCode: 200,
            Result: {
              ...mockWorkoutData.customizedWorkout,
              Exercises: [
                {
                  ...mockWorkoutData.customizedWorkout.Exercises[0],
                  Id: 101,
                  Label: 'Bench Press',
                },
              ],
            },
          }),
        })
      }
    )

    // Mock recommendation with floating point weight values
    await page.route(
      /.*GetRecommendationNormalRIRForExerciseWithoutWarmupsNew.*/,
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            StatusCode: 200,
            Result: {
              ExerciseId: 101,
              Series: 3,
              Reps: 10,
              Weight: { Lb: 135.12345678, Kg: 61.23456789 },
              WeightIncrement: 2.5,
              Increments: { Lb: 2.5, Kg: 1 },
              OneRMProgress: 1.05,
              RecommendationInKg: 61.23456789,
              OneRMPercentage: 75,
              IsBodyweight: false,
              IsNormalSets: true,
              WarmUpsList: [
                {
                  WarmUpReps: 5,
                  WarmUpWeightSet: { Lb: 95.87654321, Kg: 43.51234567 },
                },
                {
                  WarmUpReps: 3,
                  WarmUpWeightSet: { Lb: 115.12345678, Kg: 52.23456789 },
                },
              ],
            },
          }),
        })
      }
    )

    // Mock exercise sets
    await page.route(/.*GetExerciseSets.*/, async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          StatusCode: 200,
          Result: [],
        }),
      })
    })

    await page.goto('/workout')
  })

  test('should format weight with proper decimals in exercise grid', async ({
    page,
  }) => {
    // Start workout
    await page.getByRole('button', { name: /start workout/i }).click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise\/\d+/)
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Check warmup weights are formatted properly
    const warmupWeights = await page
      .locator('input[aria-label*="Weight for set W"]')
      .all()

    // First warmup should show 95.88 (rounded from 95.87654321)
    await expect(warmupWeights[0]).toHaveValue('95.88')

    // Second warmup should show 115.12 (rounded from 115.12345678)
    await expect(warmupWeights[1]).toHaveValue('115.12')

    // Check work set weights are formatted properly
    const workSetWeights = await page
      .locator('input[aria-label="Weight for set 1"]')
      .all()

    // Should show 135.12 (rounded from 135.12345678)
    await expect(workSetWeights[0]).toHaveValue('135.12')
  })

  test('should apply proper increments when using arrow buttons', async ({
    page,
  }) => {
    // Start workout
    await page.getByRole('button', { name: /start workout/i }).click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise\/\d+/)
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Find the weight up arrow for the active set
    const weightUpArrow = page
      .locator('button[aria-label="Increase weight"]')
      .first()

    // Click to increment weight
    await weightUpArrow.click()

    // Check that weight increased by 2.5 lbs (135.12 + 2.5 = 137.62, displayed as 137.5)
    const activeSetWeight = page
      .locator('input[aria-label="Weight for set 1"]')
      .first()
    await expect(activeSetWeight).toHaveValue('137.5')

    // Click down arrow
    const weightDownArrow = page
      .locator('button[aria-label="Decrease weight"]')
      .first()
    await weightDownArrow.click()

    // Should go back to 135 (137.5 - 2.5 = 135)
    await expect(activeSetWeight).toHaveValue('135')
  })

  test('should show whole numbers without decimals', async ({ page }) => {
    // Mock recommendation with whole number weights
    await page.route(
      /.*GetRecommendationNormalRIRForExerciseWithoutWarmupsNew.*/,
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            StatusCode: 200,
            Result: {
              ExerciseId: 101,
              Series: 3,
              Reps: 10,
              Weight: { Lb: 200.0, Kg: 90.718474 },
              Increments: { Lb: 2.5, Kg: 1 },
              WarmUpsList: [
                {
                  WarmUpReps: 5,
                  WarmUpWeightSet: { Lb: 135.0, Kg: 61.23466 },
                },
              ],
            },
          }),
        })
      }
    )

    // Start workout
    await page.getByRole('button', { name: /start workout/i }).click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise\/\d+/)
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Check that whole numbers don't show decimals
    const warmupWeight = page
      .locator('input[aria-label*="Weight for set W"]')
      .first()
    await expect(warmupWeight).toHaveValue('135')

    const workSetWeight = page
      .locator('input[aria-label="Weight for set 1"]')
      .first()
    await expect(workSetWeight).toHaveValue('200')
  })

  test('should format weights correctly in kg mode', async ({
    page,
    context,
  }) => {
    // Set user preference to kg
    await context.addCookies([
      {
        name: 'massUnit',
        value: 'kg',
        domain: 'localhost',
        path: '/',
      },
    ])

    await page.reload()

    // Start workout
    await page.getByRole('button', { name: /start workout/i }).click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise\/\d+/)
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Check kg weights are formatted properly
    const warmupWeights = await page
      .locator('input[aria-label*="Weight for set W"]')
      .all()

    // Should show 43.51 kg (rounded from 43.51234567)
    await expect(warmupWeights[0]).toHaveValue('43.51')

    // Check work set weight
    const workSetWeight = page
      .locator('input[aria-label="Weight for set 1"]')
      .first()

    // Should show 61.23 kg (rounded from 61.23456789)
    await expect(workSetWeight).toHaveValue('61.23')

    // Test increment in kg mode (1 kg increment)
    const weightUpArrow = page
      .locator('button[aria-label="Increase weight"]')
      .first()
    await weightUpArrow.click()

    // Should show 62 kg (61.23 + 1 = 62.23, rounded to nearest 1 kg)
    await expect(workSetWeight).toHaveValue('62')
  })
})
