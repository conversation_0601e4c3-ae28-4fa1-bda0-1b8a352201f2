import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExercisePageV2Client } from '../ExercisePageV2Client'

// Mock all dependencies
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => ({
    setTitle: vi.fn(),
    setSetInfo: vi.fn(),
    setSetProgress: vi.fn(),
  }),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'kg' }),
  }),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    isLoadingWorkout: false,
    workoutError: null,
    workoutSession: { id: '123' },
    saveSet: vi.fn(),
  }),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    loadingStates: new Map(),
    nextSet: vi.fn(),
  }),
}))

vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: () => ({
    isInitializing: false,
    loadingError: null,
    retryInitialization: vi.fn(),
  }),
}))

vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: () => ({
    currentExercise: { Id: 1, Label: 'Bench Press' },
    exercises: [{ Id: 1, Label: 'Bench Press' }],
    currentSetIndex: 0,
    isSaving: false,
    saveError: null,
    showComplete: false,
    showExerciseComplete: false,
    recommendation: { WeightIncrement: 5 },
    isLoading: false,
    error: null,
    isLastExercise: false,
    isLastSet: false,
    isWarmup: false,
    isFirstWorkSet: false,
    completedSets: [],
    setData: { reps: 10, weight: 80, duration: 0 },
    setSetData: vi.fn(),
    setSaveError: vi.fn(),
    handleSaveSet: vi.fn(),
    refetchRecommendation: vi.fn(),
  }),
}))

vi.mock('@/components/workout-v2/RestTimer', () => ({
  RestTimer: () => <div data-testid="rest-timer">Rest Timer</div>,
  useRestTimer: () => ({ startRestTimer: vi.fn() }),
}))

vi.mock('@/components/workout-v2/CurrentSetCard', () => ({
  CurrentSetCard: () => (
    <div data-testid="current-set-card">Current Set Card</div>
  ),
}))

vi.mock('@/components/workout-v2/ExerciseQuickNav', () => ({
  ExerciseQuickNav: () => (
    <div data-testid="exercise-quick-nav">Exercise Quick Nav</div>
  ),
}))

vi.mock('@/utils/generateAllSets', () => ({
  generateAllSets: () => [
    { Id: 1, IsNext: true, IsWarmups: false, IsFinished: false },
  ],
}))

describe('ExercisePageV2Client - Layout Changes', () => {
  it('should position rest timer at bottom under current set', () => {
    // Given: Exercise page with rest timer
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: Rest timer and current set render
    const restTimer = screen.getByTestId('rest-timer')
    const currentSetCard = screen.getByTestId('current-set-card')

    // Then: Rest timer should be positioned after current set in DOM order
    expect(restTimer).toBeInTheDocument()
    expect(currentSetCard).toBeInTheDocument()

    // Timer should come after set card in layout flow
    const mainContent = restTimer.closest('.flex-1')
    expect(mainContent).toContainElement(restTimer)
    expect(mainContent).toContainElement(currentSetCard)
  })

  it('should maintain proper spacing with standard navigation', () => {
    // Given: Exercise page structure
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: Content renders
    const mainContainer = screen
      .getByTestId('current-set-card')
      .closest('.flex-1')

    // Then: Should have proper spacing classes for navigation
    expect(mainContainer).toHaveClass('flex-1')
    expect(mainContainer).toHaveClass('flex', 'flex-col')
  })

  it('should not have overflow-y-auto on NextSetsPreview container to prevent WheelPicker conflicts', () => {
    // Given: Exercise page is rendered with all components
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: Examining the main content area for scroll containers
    const container = screen.getByTestId('exercise-page-container')

    // Then: No overflow-y-auto should be present on any child containers
    // This test will fail initially until overflow-y-auto is removed from line 241
    const scrollContainers = container.querySelectorAll(
      '[class*="overflow-y-auto"]'
    )
    expect(scrollContainers).toHaveLength(0)
  })

  it('should have minimal white space between CurrentSetCard and NextSetsPreview', () => {
    // Given: Exercise page renders with components
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: Examining layout structure
    const container = screen.getByTestId('exercise-page-container')
    const mainContent = container.querySelector('.flex-1.flex.flex-col')

    // Then: Should have compact spacing with appropriate flex ratios
    const hybridView = mainContent?.querySelector('.flex-1.flex.flex-col')
    expect(hybridView).toBeTruthy()

    // Check that excessive flex ratios are not used
    const currentSetContainer = hybridView?.querySelector('.flex-\\[6\\]')
    const nextSetsContainer = hybridView?.querySelector('.flex-\\[3\\]')

    // These should be null after fix (using better ratios)
    expect(currentSetContainer).toBeNull()
    expect(nextSetsContainer).toBeNull()
  })

  it('should have reduced padding in main content area', () => {
    // Given: Exercise page renders
    render(<ExercisePageV2Client exerciseId={1} />)

    // When: Examining main content padding
    const container = screen.getByTestId('exercise-page-container')
    const mainContent = container.querySelector('.px-4')

    // Then: Should not have excessive top padding
    expect(mainContent).not.toHaveClass('pt-4')

    // Check for reduced padding
    expect(mainContent).toHaveClass('pt-2')
  })
})
