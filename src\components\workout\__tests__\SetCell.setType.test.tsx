import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { SetCell } from '../SetCell'

describe('SetCell - Set Type Display', () => {
  const baseProps = {
    setNo: 1,
    reps: 10,
    weight: 135,
    isFinished: false,
    unit: 'lbs' as const,
  }

  it('should display set type badge for special sets', () => {
    render(<SetCell {...baseProps} setType="Rest-pause" />)

    const badge = screen.getByRole('button', { name: /Rest-pause set type/i })
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveTextContent('Rest-pause')
  })

  it('should display badge for normal sets', () => {
    render(<SetCell {...baseProps} setType="Normal" />)

    const badge = screen.queryByRole('button', { name: /Normal set type/i })
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveTextContent('Normal')
  })

  it('should show explainer when badge is clicked', () => {
    render(<SetCell {...baseProps} setType="Drop set" />)

    // Initially explainer should not be visible
    expect(
      screen.queryByText(/immediately reduce the weight/i)
    ).not.toBeInTheDocument()

    // Click badge
    const badge = screen.getByRole('button', { name: /Drop set set type/i })
    fireEvent.click(badge)

    // Explainer should now be visible
    expect(
      screen.getByText(/immediately reduce the weight/i)
    ).toBeInTheDocument()
  })

  it('should toggle explainer on badge clicks', () => {
    render(<SetCell {...baseProps} setType="Pyramid" />)

    const badge = screen.getByRole('button', { name: /Pyramid set type/i })

    // Click to show
    fireEvent.click(badge)
    expect(screen.getByText(/weight increases/i)).toBeInTheDocument()

    // Click to hide
    fireEvent.click(badge)
    expect(screen.queryByText(/weight increases/i)).not.toBeInTheDocument()
  })

  it('should position badge next to set number', () => {
    const { container } = render(<SetCell {...baseProps} setType="Back-off" />)

    // Check that badge is within the set number area
    const setNumberArea = container.querySelector(
      '.grid-cols-\\[25px_60px_1fr_25px_1fr\\]'
    )
    const badge = screen.getByRole('button', { name: /Back-off set type/i })

    expect(setNumberArea).toContainElement(badge)
  })

  it('should show explainer above the set inputs', () => {
    render(<SetCell {...baseProps} setType="Reverse pyramid" />)

    const badge = screen.getByRole('button', {
      name: /Reverse pyramid set type/i,
    })
    fireEvent.click(badge)

    const explainer = screen.getByRole('region', {
      name: /Reverse pyramid set explanation/i,
    })
    expect(explainer).toBeInTheDocument()
  })

  it('should maintain all existing functionality with set type display', () => {
    const onRepsChange = vi.fn()
    const onWeightChange = vi.fn()

    render(
      <SetCell
        {...baseProps}
        setType="Rest-pause"
        onRepsChange={onRepsChange}
        onWeightChange={onWeightChange}
      />
    )

    // Test that inputs still work
    const repsInput = screen.getByDisplayValue('10')
    fireEvent.change(repsInput, { target: { value: '12' } })
    expect(onRepsChange).toHaveBeenCalledWith(12)

    const weightInput = screen.getByDisplayValue('135')
    fireEvent.change(weightInput, { target: { value: '140' } })
    expect(onWeightChange).toHaveBeenCalledWith(140)
  })
})
