'use client'

import React from 'react'
import type {
  ExerciseModel,
  WorkoutLogSerieModel,
  RecommendationModel,
} from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import { SetCell } from './SetCell'
import { ExerciseSetsGridItem } from './ExerciseSetsGridItem'
import { createWorkoutSets } from '@/utils/createWorkoutSetsMAUI'

// Extended type to include warmup properties
type ExtendedWorkoutLogSerieModel = WorkoutLogSerieModel &
  Partial<WorkoutLogSerieModelRef> & {
    WarmUpReps?: number
    WarmUpWeightSet?: { Lb: number; Kg: number }
  }

interface ExerciseSetsGridProps {
  exercise: ExerciseModel
  recommendation?: RecommendationModel
  sets?: ExtendedWorkoutLogSerieModel[]
  userBodyWeight?: number
  onSetUpdate: (
    setId: number,
    updates: { reps?: number; weight?: number }
  ) => void
  onSetComplete?: (setIndex: number) => void
  onFinishExercise?: () => void
  onAddSet?: () => void
  onOneRMUpdate?: (data: {
    weight: number
    reps: number
    exercise?: {
      Id?: number
      Name?: string
      IsBodyweight?: boolean
    }
    recommendation?: {
      ExerciseId?: number
      Weight?: { Lb: number; Kg: number }
      Reps?: number
      Series?: number
    }
    isKg: boolean
    userBodyWeight: number
    isFirstWorkSet?: boolean
  }) => void
  onSaveCurrentSet?: () => void
  isSaving?: boolean
  unit?: 'kg' | 'lbs'
}

export function ExerciseSetsGrid({
  exercise,
  recommendation,
  sets: providedSets,
  userBodyWeight = 80,
  onSetUpdate,
  onSetComplete,
  onFinishExercise,
  onAddSet,
  onOneRMUpdate,
  onSaveCurrentSet,
  isSaving = false,
  unit = 'lbs',
}: ExerciseSetsGridProps) {
  // Generate sets using MAUI logic if recommendation is provided
  const sets =
    providedSets ||
    (recommendation
      ? createWorkoutSets(exercise, recommendation, unit === 'kg')
      : [])

  // Check if all sets are finished
  const allSetsFinished = sets.length > 0 && sets.every((set) => set.IsFinished)

  // Check if any set has IsNext
  const currentSetIndex = sets.findIndex((set) => set.IsNext)

  if (sets.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] p-4">
        <p className="text-text-secondary mb-4">No sets for this exercise</p>
        {onAddSet && (
          <button
            onClick={onAddSet}
            className="px-4 py-2 border-2 border-brand-primary text-brand-primary bg-transparent rounded-theme font-medium hover:bg-brand-primary hover:text-text-inverse transition-colors min-h-[44px]"
          >
            Add set
          </button>
        )}
      </div>
    )
  }

  return (
    <div className="flex flex-col" data-testid="exercise-sets-grid">
      {/* Header Row */}
      <SetCell isHeaderCell unit={unit} />

      {/* Sets */}
      <div className="divide-y divide-border-secondary">
        {sets.map((set, index) => (
          <ExerciseSetsGridItem
            key={set.Id || index}
            set={set}
            index={index}
            sets={sets}
            exercise={exercise}
            recommendation={recommendation}
            userBodyWeight={userBodyWeight}
            onSetUpdate={onSetUpdate}
            onSetComplete={onSetComplete}
            onAddSet={onAddSet}
            onOneRMUpdate={onOneRMUpdate}
            onSaveCurrentSet={onSaveCurrentSet}
            isSaving={isSaving}
            unit={unit}
            currentSetIndex={currentSetIndex}
            allSetsFinished={allSetsFinished}
          />
        ))}
      </div>

      {/* All sets done message (shown once, not per set) */}
      {allSetsFinished && (
        <div className="px-4 py-6">
          <div className="bg-success/10 rounded-theme p-4">
            <p className="text-center text-success font-medium italic">
              All sets done—congrats!
            </p>
          </div>
          {onFinishExercise && (
            <button
              onClick={onFinishExercise}
              className="w-full mt-4 bg-success text-text-inverse font-bold py-4 rounded-theme text-lg hover:bg-success/90 transition-colors min-h-[66px]"
            >
              Finish exercise
            </button>
          )}
        </div>
      )}
    </div>
  )
}
