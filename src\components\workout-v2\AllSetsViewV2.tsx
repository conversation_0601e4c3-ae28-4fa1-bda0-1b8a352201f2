'use client'

import React from 'react'
import { ChevronUp, ChevronDown } from 'lucide-react'
import type { WorkoutLogSerieModel } from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import { vibrate } from '@/utils/haptics'
import type { SetType } from '@/utils/setTypeUtils'

// Extended type to include warmup properties
type ExtendedWorkoutLogSerieModel = WorkoutLogSerieModel &
  Partial<WorkoutLogSerieModelRef> & {
    WarmUpReps?: number
    WarmUpWeightSet?: { Lb: number; Kg: number }
    IsSkipped?: boolean
  }

interface AllSetsViewV2Props {
  allSets: ExtendedWorkoutLogSerieModel[]
  setData: { reps: number; weight: number; duration: number }
  onSetDataChange: (data: {
    reps: number
    weight: number
    duration: number
  }) => void
  onComplete: () => void
  onSkip: () => void
  isSaving: boolean
  unit: 'kg' | 'lbs'
}

export function AllSetsViewV2({
  allSets,
  setData,
  onSetDataChange,
  onComplete,
  onSkip,
  isSaving,
  unit,
}: AllSetsViewV2Props) {
  const incrementValue = (field: 'reps' | 'weight', amount: number) => {
    vibrate('light')
    const newValue = Math.max(0, setData[field] + amount)
    onSetDataChange({
      ...setData,
      [field]: newValue,
    })
  }

  // Get proper weight increment based on unit
  const getWeightIncrement = () => {
    return unit === 'kg' ? 1 : 2.5
  }

  const currentSet = allSets.find((s) => s.IsNext)
  const workSets = allSets.filter((s) => !s.IsWarmups)

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Grid Header */}
      <div className="grid grid-cols-4 gap-2 px-4 py-3 bg-surface-secondary rounded-t-lg">
        <div className="text-center text-xs font-semibold text-text-secondary uppercase">
          SET
        </div>
        <div className="text-center text-xs font-semibold text-text-secondary uppercase">
          REPS
        </div>
        <div className="text-center text-xs font-semibold text-text-secondary uppercase">
          *
        </div>
        <div className="text-center text-xs font-semibold text-text-secondary uppercase">
          {unit.toUpperCase()}
        </div>
      </div>

      {/* Sets Grid */}
      <div className="bg-surface-primary divide-y divide-border-secondary">
        {allSets.map((set, index) => {
          const isCurrentSet = set.IsNext
          const isWarmup = set.IsWarmups
          const workSetIndex = !isWarmup
            ? workSets.findIndex((ws) => ws.Id === set.Id)
            : -1
          // For warmup sets, find the warmup index
          const warmupIndex = isWarmup
            ? allSets.filter((s, i) => s.IsWarmups && i <= index).length
            : -1
          const displayNumber = isWarmup
            ? `W${warmupIndex}`
            : (workSetIndex + 1).toString()
          // Determine set type from individual set flags
          const setType: SetType | null = (() => {
            if (isWarmup) return 'Warm-up'
            if ('IsRestPause' in set && set.IsRestPause) return 'Rest-pause'
            if ('IsPyramid' in set && set.IsPyramid) return 'Pyramid'
            if ('IsDrop' in set && set.IsDrop) return 'Drop set'
            if ('IsDropSet' in set && set.IsDropSet) return 'Drop set'
            if ('IsBackOff' in set && set.IsBackOff) return 'Back-off'
            if ('IsBackOffSet' in set && set.IsBackOffSet) return 'Back-off'
            if ('IsReversePyramid' in set && set.IsReversePyramid)
              return 'Reverse pyramid'
            return null
          })()

          // Get display values
          const reps = isWarmup ? set.WarmUpReps : set.Reps
          const weight = isWarmup
            ? set.WarmUpWeightSet?.[unit === 'kg' ? 'Kg' : 'Lb']
            : set.Weight?.[unit === 'kg' ? 'Kg' : 'Lb']

          return (
            <div
              key={set.Id || index}
              data-testid={`set-row-${index}`}
              className={`grid grid-cols-4 gap-2 px-4 py-3 items-center ${
                isCurrentSet ? 'bg-brand-primary/10' : ''
              }`}
            >
              {/* Set Number */}
              <div className="text-center">
                <span className="font-medium text-text-primary">
                  {displayNumber}
                </span>
                {setType && (
                  <div className="text-xs text-text-secondary mt-1">
                    {setType}
                  </div>
                )}
              </div>

              {/* Reps */}
              <div className="text-center">
                {isCurrentSet ? (
                  <div className="flex items-center justify-center gap-1">
                    <button
                      onClick={() => incrementValue('reps', -1)}
                      className="p-1 hover:bg-surface-secondary rounded"
                      aria-label="Decrement reps"
                      disabled={isSaving}
                    >
                      <ChevronDown className="w-4 h-4" />
                    </button>
                    <input
                      type="number"
                      value={setData.reps}
                      onChange={(e) =>
                        onSetDataChange({
                          ...setData,
                          reps: parseInt(e.target.value) || 0,
                        })
                      }
                      className="w-12 text-center bg-transparent border-b border-border-primary"
                      disabled={isSaving}
                    />
                    <button
                      onClick={() => incrementValue('reps', 1)}
                      className="p-1 hover:bg-surface-secondary rounded"
                      aria-label="Increment reps"
                      disabled={isSaving}
                    >
                      <ChevronUp className="w-4 h-4" />
                    </button>
                  </div>
                ) : (
                  <span className="text-text-primary">{reps || 0}</span>
                )}
              </div>

              {/* Status */}
              <div className="text-center">
                {(() => {
                  if (set.IsFinished)
                    return <span className="text-success">✓</span>
                  if (set.IsSkipped)
                    return <span className="text-error">✗</span>
                  if (isCurrentSet) return '-'
                  return ''
                })()}
              </div>

              {/* Weight */}
              <div className="text-center">
                {isCurrentSet ? (
                  <div className="flex items-center justify-center gap-1">
                    <button
                      onClick={() =>
                        incrementValue('weight', -getWeightIncrement())
                      }
                      className="p-1 hover:bg-surface-secondary rounded"
                      aria-label="Decrement weight"
                      disabled={isSaving}
                    >
                      <ChevronDown className="w-4 h-4" />
                    </button>
                    <input
                      type="number"
                      value={setData.weight}
                      onChange={(e) =>
                        onSetDataChange({
                          ...setData,
                          weight: parseFloat(e.target.value) || 0,
                        })
                      }
                      className="w-16 text-center bg-transparent border-b border-border-primary"
                      disabled={isSaving}
                    />
                    <button
                      onClick={() =>
                        incrementValue('weight', getWeightIncrement())
                      }
                      className="p-1 hover:bg-surface-secondary rounded"
                      aria-label="Increment weight"
                      disabled={isSaving}
                    >
                      <ChevronUp className="w-4 h-4" />
                    </button>
                  </div>
                ) : (
                  <span className="text-text-primary">{weight || 0}</span>
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* Save/Skip buttons for current set */}
      {currentSet && (
        <div className="grid grid-cols-2 gap-4 px-4 py-4 bg-surface-secondary rounded-b-lg">
          <button
            onClick={onSkip}
            disabled={isSaving}
            className="px-6 py-3 bg-surface-primary text-text-primary rounded-lg font-medium hover:bg-surface-tertiary transition-colors disabled:opacity-50"
          >
            Skip
          </button>
          <button
            onClick={onComplete}
            disabled={isSaving}
            className="px-6 py-3 bg-brand-primary text-text-inverse rounded-lg font-medium hover:bg-brand-primary/90 transition-colors disabled:opacity-50"
          >
            {isSaving ? 'Saving...' : 'Save set'}
          </button>
        </div>
      )}
    </div>
  )
}
