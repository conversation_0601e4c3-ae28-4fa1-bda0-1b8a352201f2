import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExerciseSetsGrid } from '../ExerciseSetsGrid'
import type { ExerciseModel, RecommendationModel } from '@/types'

const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  Name: 'Bench Press',
  IsSystemExercise: true,
  IsSwapTarget: false,
  IsFinished: false,
  IsUnilateral: false,
  IsTimeBased: false,
  IsEasy: false,
  IsMedium: true,
  IsBodyweight: false,
  VideoUrl: '',
  IsNextExercise: false,
  IsPlate: true,
  IsWeighted: false,
  IsPyramid: false,
  IsNormalSets: true,
  LocalVideo: '',
  IsAssisted: false,
  BodyPartId: 1,
  EquipmentId: 1,
  IsFlexibility: false,
}

const mockRecommendation: RecommendationModel = {
  ExerciseId: 1,
  Series: 3,
  Reps: 12,
  Weight: { Kg: 27.2, Lb: 60 },
  WarmupsCount: 2,
  RpRest: 120,
  NbPauses: 0,
  NbRepsPauses: 0,
  WarmUpsList: [
    { WarmUpReps: 9, WarmUpWeightSet: { Kg: 13.6, Lb: 30 } }, // ~50% weight, ~75% reps
    { WarmUpReps: 6, WarmUpWeightSet: { Kg: 20.4, Lb: 45 } }, // ~75% weight, ~50% reps
  ],
  FirstWorkSetReps: 12,
  FirstWorkSetWeight: { Kg: 27.2, Lb: 60 },
  Increments: { Kg: 1, Lb: 2.5 },
  Min: { Kg: 0, Lb: 0 },
  Max: { Kg: 200, Lb: 440 },
}

describe('Warmup Sets Display', () => {
  it('should display correct warmup values (not work set values)', () => {
    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockRecommendation}
        unit="kg"
        onSetUpdate={() => {}}
      />
    )

    // Find all weight and reps displays
    const allInputs = screen.getAllByRole('spinbutton')

    // Separate reps and weight inputs based on their aria-label
    const repsInputs = allInputs.filter((input) =>
      input.getAttribute('aria-label')?.includes('Reps')
    )
    const weightInputs = allInputs.filter((input) =>
      input.getAttribute('aria-label')?.includes('Weight')
    )

    // First warmup set should show 13.6kg × 9 reps
    expect(weightInputs[0]).toHaveAttribute('value', '13.6 kg')
    expect(repsInputs[0]).toHaveValue(9)

    // Second warmup set should show 20.4kg × 6 reps
    expect(weightInputs[1]).toHaveAttribute('value', '20.4 kg')
    expect(repsInputs[1]).toHaveValue(6)

    // First work set should show 27.2kg × 12 reps
    expect(weightInputs[2]).toHaveAttribute('value', '27.2 kg')
    expect(repsInputs[2]).toHaveValue(12)

    // Verify warmup sets are NOT showing work set values
    expect(weightInputs[0]).not.toHaveAttribute('value', '27.2 kg')
    expect(repsInputs[0]).not.toHaveValue(12)
  })

  it('should display warmup values in lbs correctly', () => {
    render(
      <ExerciseSetsGrid
        exercise={mockExercise}
        recommendation={mockRecommendation}
        unit="lbs"
        onSetUpdate={() => {}}
      />
    )

    const allInputs = screen.getAllByRole('spinbutton')

    // Separate reps and weight inputs based on their aria-label
    const repsInputs = allInputs.filter((input) =>
      input.getAttribute('aria-label')?.includes('Reps')
    )
    const weightInputs = allInputs.filter((input) =>
      input.getAttribute('aria-label')?.includes('Weight')
    )

    // First warmup set should show 30lbs × 9 reps
    expect(weightInputs[0]).toHaveAttribute('value', '30 lbs')
    expect(repsInputs[0]).toHaveValue(9)

    // Second warmup set should show 45lbs × 6 reps
    expect(weightInputs[1]).toHaveAttribute('value', '45 lbs')
    expect(repsInputs[1]).toHaveValue(6)

    // First work set should show 60lbs × 12 reps
    expect(weightInputs[2]).toHaveAttribute('value', '60 lbs')
    expect(repsInputs[2]).toHaveValue(12)
  })
})
