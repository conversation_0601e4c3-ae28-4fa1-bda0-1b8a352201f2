import { test, expect } from '@playwright/test'

test.describe('Exercise V2 - Completed Sets Visual Test', () => {
  test('should show completed sets section properly styled', async ({
    page,
  }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 390, height: 844 })

    // Navigate directly to a test page with mocked data
    await page.goto('/test-rest-pause')

    // Take a screenshot of the initial state
    await page.screenshot({
      path: 'test-results/exercise-v2-completed-sets-initial.png',
      fullPage: true,
    })

    // Verify the page loads
    expect(await page.title()).toBeTruthy()
  })
})
