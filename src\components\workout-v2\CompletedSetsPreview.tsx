'use client'

import React from 'react'
import type { WorkoutLogSerieModel } from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'

// Extended type to include warmup properties
type ExtendedWorkoutLogSerieModel = WorkoutLogSerieModel &
  Partial<WorkoutLogSerieModelRef> & {
    WarmUpReps?: number
    WarmUpWeightSet?: { Lb: number; Kg: number }
    IsSkipped?: boolean
    NbPause?: number
    SetTitle?: string
  }

interface CompletedSetsPreviewProps {
  completedSets: ExtendedWorkoutLogSerieModel[]
  unit: 'kg' | 'lbs'
}

export function CompletedSetsPreview({
  completedSets,
  unit,
}: CompletedSetsPreviewProps) {
  if (completedSets.length === 0) {
    return null
  }

  return (
    <div className="w-full max-w-md bg-surface-secondary/50 rounded-lg p-2 sm:p-3">
      <h3 className="text-sm font-medium text-text-secondary mb-1.5 sm:mb-2">
        Completed Sets
      </h3>
      <div className="space-y-1">
        {completedSets.map((set, index) => {
          const isWarmup = set.IsWarmups
          const reps = isWarmup ? set.WarmUpReps : set.Reps
          const weight = isWarmup
            ? set.WarmUpWeightSet?.[unit === 'kg' ? 'Kg' : 'Lb']
            : set.Weight?.[unit === 'kg' ? 'Kg' : 'Lb']

          // Count warmup sets
          let warmupNumber = 1
          if (isWarmup) {
            warmupNumber = completedSets
              .slice(0, index + 1)
              .filter((s) => s.IsWarmups).length
          }

          // Count work sets
          let workSetNumber = 1
          if (!isWarmup) {
            workSetNumber = completedSets
              .slice(0, index + 1)
              .filter((s) => !s.IsWarmups).length
          }

          // Determine set label
          let setLabel = ''
          if (isWarmup) {
            setLabel = `W${warmupNumber}`
          } else if (set.NbPause && set.NbPause > 0) {
            // Rest-pause set
            setLabel = `RP ${workSetNumber}`
          } else {
            setLabel = `Set ${workSetNumber}`
          }

          return (
            <div
              key={set.Id || index}
              className="flex items-center justify-between py-1 px-2 sm:py-1.5 sm:px-2.5 bg-surface-primary/50 rounded-md"
            >
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-text-primary">
                  {setLabel}
                </span>
                {/* Check mark for completed sets */}
                <svg
                  className="w-4 h-4 text-green-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <div className="flex items-center gap-1.5 sm:gap-2 text-sm text-text-secondary">
                <span>{reps || 0} reps</span>
                <span className="text-text-tertiary">×</span>
                <span>
                  {weight || 0} {unit}
                </span>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
