# WebKit CI Fixes

## Problem
Playwright tests were failing in CI with the error:
```
Error: browserContext.newPage: Target page, context or browser has been closed
```

This error specifically affected Mobile Safari (WebKit) tests on self-hosted macOS runners.

## Root Causes Identified

1. **Missing system dependencies**: WebKit requires specific system dependencies that weren't being installed
2. **Resource exhaustion**: Too many retries and workers were overwhelming the WebKit browser
3. **Browser launch configuration**: WebKit-specific environment variables weren't properly set
4. **Installation validation**: No verification that WebKit was properly installed before running tests

## Changes Made

### 1. Enhanced Playwright Browser Installation (`.github/actions/setup-playwright/action.yml`)
- Added WebKit launch capability test after installation
- Ensured `--with-deps` flag is used for system dependencies
- Added better error handling and diagnostics

### 2. Updated CI Workflow (`.github/workflows/ci-optimized.yml`)
- Added pre-test WebKit validation steps
- Set WebKit-specific environment variables (`WEBKIT_DISABLE_COMPOSITING`, `WEBKIT_FORCE_COMPOSITING_MODE`)
- Added browser directory existence checks before running tests

### 3. Improved WebKit Browser Factory (`tests/e2e/webkit-browser-factory.ts`)
- Enhanced launch options with WebKit-specific stability settings
- Fixed TypeScript compatibility issues
- Added proper PATH environment variable handling

### 4. Optimized Playwright Configuration (`playwright.ci.optimized.config.ts`)
- Reduced workers from 2 to 1 for WebKit stability
- Reduced retries to prevent resource exhaustion
- Maintained single-worker configuration for WebKit projects

### 5. Enhanced Global Setup (`tests/e2e/global-setup.ts`)
- Added WebKit installation verification before tests
- Better error reporting for missing WebKit installations
- Platform-specific cache directory detection

### 6. Added Diagnostic Tools
- Created `scripts/test-webkit-setup.js` for WebKit installation testing
- Added `npm run test:webkit-setup` command for easy diagnostics

## Testing the Fixes

### 1. Local Testing
```bash
# Test WebKit installation
npm run test:webkit-setup

# Run critical tests only
npm run test:critical-flows

# Run full E2E tests
npm run test:e2e
```

### 2. CI Testing
The fixes will be automatically applied in CI. Look for these improvements:
- Pre-test validation steps showing WebKit directory found
- Reduced retry attempts (3 instead of 5 for critical, 2 for full)
- Better error messages if WebKit installation fails

### 3. Monitoring
Watch for these success indicators in CI logs:
```
✅ WebKit browser directory found
✅ WebKit launch test passed
✅ Pre-test validation completed
```

## Expected Improvements

1. **Reduced failure rate**: WebKit tests should fail less frequently due to browser launch issues
2. **Faster feedback**: Fewer retries mean faster test completion
3. **Better diagnostics**: Clear error messages when WebKit installation fails
4. **Resource efficiency**: Single worker prevents resource exhaustion

## Troubleshooting

If WebKit tests still fail:

1. **Check installation**: Run `npm run test:webkit-setup` locally
2. **Verify dependencies**: Ensure `npx playwright install --with-deps webkit` succeeds
3. **Check resources**: Monitor memory and CPU usage during tests
4. **Review logs**: Look for WebKit-specific error messages in CI output

## Rollback Plan

If these changes cause issues:
1. Revert `playwright.ci.optimized.config.ts` workers back to 2
2. Increase retries back to original values
3. Remove pre-test validation steps from CI workflow

## Next Steps

1. Monitor CI runs for improved stability
2. Consider adding WebKit-specific timeout adjustments if needed
3. Evaluate if further resource optimization is required
