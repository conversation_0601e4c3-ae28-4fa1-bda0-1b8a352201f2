import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { FloatingCTAButton } from '../FloatingCTAButton'

describe('FloatingCTAButton V2 Styling', () => {
  const mockOnClick = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('applies gold metallic gradient style matching Exercise V2 buttons', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByTestId('start-workout-button')

    // Should have gold metallic gradient class
    expect(button).toHaveClass('bg-gradient-metallic-gold')

    // Should have shimmer hover effect
    expect(button).toHaveClass('shimmer-hover')

    // Should have proper text styling
    expect(button).toHaveClass('text-text-inverse')
    expect(button).toHaveClass('text-shadow-sm')

    // Should have V2 shadow styling
    expect(button).toHaveClass('shadow-theme-md')
    expect(button).toHaveClass('hover:shadow-theme-lg')

    // Should have active scale effect
    expect(button).toHaveClass('active:scale-[0.98]')
  })

  it('removes old primary brand styling', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByTestId('start-workout-button')

    // Should NOT have old brand primary classes
    expect(button).not.toHaveClass('bg-brand-primary')
    expect(button).not.toHaveClass('hover:bg-brand-primary/90')
  })

  it('maintains minimum height for touch targets', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByTestId('start-workout-button')

    // Should have minimum height for mobile touch targets
    expect(button).toHaveClass('min-h-[62px]')
  })

  it('applies proper rounded corners matching V2 style', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByTestId('start-workout-button')

    // Should use theme rounded corners
    expect(button).toHaveClass('rounded-theme')
  })

  it('maintains floating positioning', () => {
    const { container } = render(<FloatingCTAButton onClick={mockOnClick} />)

    const floatingContainer = container.querySelector('.fixed.bottom-6')

    // Should still be positioned at bottom
    expect(floatingContainer).toBeInTheDocument()
    expect(floatingContainer).toHaveClass('fixed')
    expect(floatingContainer).toHaveClass('bottom-6')
    expect(floatingContainer).toHaveClass('left-0')
    expect(floatingContainer).toHaveClass('right-0')
  })
})
