#!/usr/bin/env node

/**
 * Test script to validate WebKit browser setup
 * This script helps diagnose WebKit installation issues before running full tests
 */

const { webkit } = require('playwright')
const fs = require('fs')
const path = require('path')
const os = require('os')

async function testWebKitSetup() {
  console.log('🔧 Testing WebKit browser setup...')
  console.log(`Platform: ${process.platform}`)
  console.log(`Node version: ${process.version}`)
  
  // Check environment variables
  console.log('\n📋 Environment variables:')
  console.log(`PLAYWRIGHT_BROWSERS_PATH: ${process.env.PLAYWRIGHT_BROWSERS_PATH || '(not set)'}`)
  console.log(`WEBKIT_DISABLE_COMPOSITING: ${process.env.WEBKIT_DISABLE_COMPOSITING || '(not set)'}`)
  console.log(`WEBKIT_FORCE_COMPOSITING_MODE: ${process.env.WEBKIT_FORCE_COMPOSITING_MODE || '(not set)'}`)
  
  // Check cache directory
  const cacheBasePath = process.platform === 'darwin' 
    ? path.join(os.homedir(), 'Library', 'Caches', 'ms-playwright')
    : path.join(os.homedir(), '.cache', 'ms-playwright')
  
  console.log(`\n📁 Checking cache directory: ${cacheBasePath}`)
  
  if (fs.existsSync(cacheBasePath)) {
    const entries = fs.readdirSync(cacheBasePath)
    const webkitDirs = entries.filter(entry => entry.startsWith('webkit-'))
    
    if (webkitDirs.length > 0) {
      console.log(`✅ Found WebKit installation: ${webkitDirs[0]}`)
      
      const webkitPath = path.join(cacheBasePath, webkitDirs[0])
      const webkitContents = fs.readdirSync(webkitPath)
      console.log(`WebKit directory contents: ${webkitContents.slice(0, 5).join(', ')}${webkitContents.length > 5 ? '...' : ''}`)
    } else {
      console.error('❌ No WebKit browser directories found')
      console.log('Available directories:', entries)
      return false
    }
  } else {
    console.error('❌ Playwright cache directory not found')
    console.log('💡 This might be expected if Playwright browsers are not installed yet.')
    console.log('💡 Run "npx playwright install --with-deps webkit" to install WebKit.')

    if (process.platform === 'win32') {
      console.log('ℹ️  Note: WebKit support on Windows is limited. This test is primarily for macOS/Linux CI environments.')
    }

    return false
  }
  
  // Test browser launch
  console.log('\n🚀 Testing WebKit browser launch...')
  
  try {
    const browser = await webkit.launch({
      headless: true,
      timeout: 30000,
      env: {
        ...process.env,
        WEBKIT_DISABLE_COMPOSITING: '1',
        WEBKIT_FORCE_COMPOSITING_MODE: '0',
      }
    })
    
    console.log('✅ WebKit browser launched successfully')
    
    // Test context creation
    const context = await browser.newContext({
      viewport: { width: 390, height: 844 }
    })
    
    console.log('✅ Browser context created successfully')
    
    // Test page creation and navigation
    const page = await context.newPage()
    await page.goto('data:text/html,<html><body>Test Page</body></html>')
    
    const content = await page.textContent('body')
    if (content === 'Test Page') {
      console.log('✅ Page navigation and content extraction successful')
    } else {
      console.error('❌ Page content mismatch')
    }
    
    await page.close()
    await context.close()
    await browser.close()
    
    console.log('✅ All WebKit tests passed!')
    return true
    
  } catch (error) {
    console.error('❌ WebKit browser test failed:', error.message)
    console.error('Full error:', error)
    return false
  }
}

// Run the test
testWebKitSetup()
  .then(success => {
    process.exit(success ? 0 : 1)
  })
  .catch(error => {
    console.error('❌ Test script failed:', error)
    process.exit(1)
  })
