import React from 'react'

interface GradientKebabMenuIconProps {
  className?: string
  onClick?: () => void
  size?: number
}

export function GradientKebabMenuIcon({
  className = '',
  onClick,
  size = 24,
}: GradientKebabMenuIconProps) {
  const gradientId = `kebab-gradient-${Math.random().toString(36).substr(2, 9)}`

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      onClick={onClick}
      aria-hidden="true"
    >
      <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#d4af37" />
          <stop offset="100%" stopColor="#f7e98e" />
        </linearGradient>
      </defs>
      <circle cx="12" cy="5" r="1" fill={`url(#${gradientId})`} />
      <circle cx="12" cy="12" r="1" fill={`url(#${gradientId})`} />
      <circle cx="12" cy="19" r="1" fill={`url(#${gradientId})`} />
    </svg>
  )
}
