name: 'Check System Resources'
description: 'Validates system resources and fails early if insufficient'
inputs:
  min-disk-gb:
    description: 'Minimum disk space required in GB'
    required: false
    default: '2'
  min-memory-gb:
    description: 'Minimum available memory in GB'
    required: false
    default: '1'
  cleanup-on-low-resources:
    description: 'Attempt cleanup if resources are low'
    required: false
    default: 'true'

outputs:
  disk-available-gb:
    description: 'Available disk space in GB'
    value: ${{ steps.check-resources.outputs.disk-available-gb }}
  memory-available-gb:
    description: 'Available memory in GB'
    value: ${{ steps.check-resources.outputs.memory-available-gb }}

runs:
  using: 'composite'
  steps:
    - name: Check system resources
      id: check-resources
      shell: bash
      run: |
        echo "=== System Resource Check ==="
        
        # Check disk space
        if [[ "$RUNNER_OS" == "macOS" ]]; then
          DISK_AVAILABLE_KB=$(df / | tail -1 | awk '{print $4}')
        else
          DISK_AVAILABLE_KB=$(df / | tail -1 | awk '{print $4}')
        fi
        DISK_AVAILABLE_GB=$((DISK_AVAILABLE_KB / 1024 / 1024))
        echo "disk-available-gb=$DISK_AVAILABLE_GB" >> $GITHUB_OUTPUT
        
        # Check memory
        if [[ "$RUNNER_OS" == "macOS" ]]; then
          # Get free memory on macOS - more robust parsing
          FREE_PAGES=$(vm_stat | grep "Pages free:" | awk '{print $3}' | sed 's/\.//')
          if [ -z "$FREE_PAGES" ]; then
            # Fallback: try different format
            FREE_PAGES=$(vm_stat | grep "free:" | awk '{print $2}' | sed 's/\.//')
          fi
          # Get page size (typically 4096 on macOS)
          PAGE_SIZE=$(vm_stat | head -1 | grep -o '[0-9]*' | tail -1)
          if [ -z "$PAGE_SIZE" ]; then
            PAGE_SIZE=4096  # Default page size
          fi
          MEMORY_AVAILABLE_KB=$((FREE_PAGES * PAGE_SIZE / 1024))
        else
          # Get available memory on Linux
          MEMORY_AVAILABLE_KB=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
        fi
        MEMORY_AVAILABLE_GB=$((MEMORY_AVAILABLE_KB / 1024 / 1024))
        echo "memory-available-gb=$MEMORY_AVAILABLE_GB" >> $GITHUB_OUTPUT
        
        echo "Available disk space: ${DISK_AVAILABLE_GB}GB"
        echo "Available memory: ${MEMORY_AVAILABLE_GB}GB"
        echo "Required disk space: ${{ inputs.min-disk-gb }}GB"
        echo "Required memory: ${{ inputs.min-memory-gb }}GB"
        
        # Check if resources meet requirements
        DISK_OK=true
        MEMORY_OK=true
        
        if [ "$DISK_AVAILABLE_GB" -lt "${{ inputs.min-disk-gb }}" ]; then
          echo "⚠️ Disk space below requirement: ${DISK_AVAILABLE_GB}GB < ${{ inputs.min-disk-gb }}GB"
          DISK_OK=false
        fi
        
        if [ "$MEMORY_AVAILABLE_GB" -lt "${{ inputs.min-memory-gb }}" ]; then
          echo "⚠️ Memory below requirement: ${MEMORY_AVAILABLE_GB}GB < ${{ inputs.min-memory-gb }}GB"
          MEMORY_OK=false
        fi
        
        # Store status for cleanup step
        echo "DISK_OK=$DISK_OK" >> $GITHUB_ENV
        echo "MEMORY_OK=$MEMORY_OK" >> $GITHUB_ENV

    - name: Attempt resource cleanup
      if: inputs.cleanup-on-low-resources == 'true' && (env.DISK_OK == 'false' || env.MEMORY_OK == 'false')
      shell: bash
      run: |
        echo "=== Attempting Resource Cleanup ==="
        
        if [[ "$DISK_OK" == "false" ]]; then
          echo "Cleaning up disk space..."
          # Clean npm cache
          npm cache clean --force || true
          # Clean Playwright cache (both possible locations)
          if [[ "$RUNNER_OS" == "macOS" ]]; then
            rm -rf ~/Library/Caches/ms-playwright || true
          fi
          rm -rf ~/.cache/ms-playwright || true
          # Clean temp files
          rm -rf /tmp/playwright-* || true
          rm -rf /tmp/npm-* || true
          # Clean Docker if available
          docker system prune -f || true
        fi
        
        if [[ "$MEMORY_OK" == "false" ]]; then
          echo "Attempting memory cleanup..."
          # Kill zombie processes
          pkill -9 safaridriver || true
          pkill -9 WebKitWebContent || true
          pkill -9 WebKitNetworkProcess || true
          pkill -9 WebKitWebProcess || true
          pkill -9 com.apple.WebKit.WebContent || true
          pkill -9 Safari || true
          pkill -9 WebKit || true
          pkill -9 playwright || true
          
          # Force memory cleanup on macOS
          if [[ "$RUNNER_OS" == "macOS" ]]; then
            sudo purge || true
            sudo dscacheutil -flushcache || true
          fi
        fi
        
        sleep 3
        echo "Cleanup completed"

    - name: Re-check resources after cleanup
      if: inputs.cleanup-on-low-resources == 'true' && (env.DISK_OK == 'false' || env.MEMORY_OK == 'false')
      shell: bash
      run: |
        echo "=== Re-checking Resources After Cleanup ==="
        
        # Re-check disk space
        if [[ "$RUNNER_OS" == "macOS" ]]; then
          DISK_AVAILABLE_KB=$(df / | tail -1 | awk '{print $4}')
        else
          DISK_AVAILABLE_KB=$(df / | tail -1 | awk '{print $4}')
        fi
        DISK_AVAILABLE_GB=$((DISK_AVAILABLE_KB / 1024 / 1024))
        
        # Re-check memory
        if [[ "$RUNNER_OS" == "macOS" ]]; then
          FREE_PAGES=$(vm_stat | grep "Pages free:" | awk '{print $3}' | sed 's/\.//')
          if [ -z "$FREE_PAGES" ]; then
            FREE_PAGES=$(vm_stat | grep "free:" | awk '{print $2}' | sed 's/\.//')
          fi
          PAGE_SIZE=$(vm_stat | head -1 | grep -o '[0-9]*' | tail -1)
          if [ -z "$PAGE_SIZE" ]; then
            PAGE_SIZE=4096
          fi
          MEMORY_AVAILABLE_KB=$((FREE_PAGES * PAGE_SIZE / 1024))
        else
          MEMORY_AVAILABLE_KB=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
        fi
        MEMORY_AVAILABLE_GB=$((MEMORY_AVAILABLE_KB / 1024 / 1024))
        
        echo "Available disk space after cleanup: ${DISK_AVAILABLE_GB}GB"
        echo "Available memory after cleanup: ${MEMORY_AVAILABLE_GB}GB"

    - name: Fail if resources still insufficient
      shell: bash
      run: |
        # Final check
        if [[ "$RUNNER_OS" == "macOS" ]]; then
          DISK_AVAILABLE_KB=$(df / | tail -1 | awk '{print $4}')
        else
          DISK_AVAILABLE_KB=$(df / | tail -1 | awk '{print $4}')
        fi
        DISK_AVAILABLE_GB=$((DISK_AVAILABLE_KB / 1024 / 1024))
        
        if [[ "$RUNNER_OS" == "macOS" ]]; then
          FREE_PAGES=$(vm_stat | grep "Pages free:" | awk '{print $3}' | sed 's/\.//')
          if [ -z "$FREE_PAGES" ]; then
            FREE_PAGES=$(vm_stat | grep "free:" | awk '{print $2}' | sed 's/\.//')
          fi
          PAGE_SIZE=$(vm_stat | head -1 | grep -o '[0-9]*' | tail -1)
          if [ -z "$PAGE_SIZE" ]; then
            PAGE_SIZE=4096
          fi
          MEMORY_AVAILABLE_KB=$((FREE_PAGES * PAGE_SIZE / 1024))
        else
          MEMORY_AVAILABLE_KB=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
        fi
        MEMORY_AVAILABLE_GB=$((MEMORY_AVAILABLE_KB / 1024 / 1024))
        
        if [ "$DISK_AVAILABLE_GB" -lt "${{ inputs.min-disk-gb }}" ]; then
          echo "❌ FATAL: Insufficient disk space after cleanup: ${DISK_AVAILABLE_GB}GB < ${{ inputs.min-disk-gb }}GB"
          echo "Please free up disk space on the runner or increase disk capacity"
          exit 1
        fi
        
        if [ "$MEMORY_AVAILABLE_GB" -lt "${{ inputs.min-memory-gb }}" ]; then
          echo "❌ FATAL: Insufficient memory after cleanup: ${MEMORY_AVAILABLE_GB}GB < ${{ inputs.min-memory-gb }}GB"
          echo "Please restart the runner or increase memory capacity"
          exit 1
        fi
        
        echo "✅ System resources are sufficient"
