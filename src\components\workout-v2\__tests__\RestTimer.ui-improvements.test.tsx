import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { RestTimer } from '../RestTimer'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock the workout store
vi.mock('@/stores/workoutStore')
const mockUseWorkoutStore = vi.mocked(useWorkoutStore)

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock notifications
Object.defineProperty(window, 'Notification', {
  value: vi.fn().mockImplementation(() => ({
    close: vi.fn(),
  })),
  configurable: true,
})

Object.defineProperty(window.Notification, 'permission', {
  value: 'granted',
  configurable: true,
})

describe('RestTimer UI Improvements', () => {
  const mockSetRestTimerState = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: true,
        duration: 90,
        nextSetInfo: {
          reps: 8,
          weight: 135,
          unit: 'lbs',
        },
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)
  })

  it('shows Hide button instead of Skip', () => {
    render(<RestTimer />)

    const hideButton = screen.getByText('Hide')
    expect(hideButton).toBeInTheDocument()
    expect(screen.queryByText('Skip')).not.toBeInTheDocument()
  })

  it('applies gold gradient to progress bar', () => {
    render(<RestTimer />)

    const progressBar = screen.getByTestId('rest-timer-progress')
    expect(progressBar).toHaveClass(
      'bg-gradient-to-r',
      'from-brand-gold-start',
      'to-brand-gold-end'
    )
  })

  it('applies gold gradient to countdown text', () => {
    render(<RestTimer />)

    const countdownText = screen.getByTestId('countdown-text')
    expect(countdownText).toHaveClass(
      'bg-gradient-to-r',
      'from-brand-gold-start',
      'to-brand-gold-end',
      'bg-clip-text',
      'text-transparent'
    )
  })

  it('has semi-opaque background like top navigation', () => {
    render(<RestTimer />)

    const container = screen.getByTestId('rest-timer-container')
    expect(container).toHaveClass('bg-surface-primary/90', 'backdrop-blur-sm')
  })

  it('displays next set info in correct format', () => {
    render(<RestTimer />)

    const nextSetInfo = screen.getByText('Get ready for: 8 x 135 lbs')
    expect(nextSetInfo).toBeInTheDocument()
  })

  it('displays next set info for kg units', () => {
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: true,
        duration: 90,
        nextSetInfo: {
          reps: 10,
          weight: 60,
          unit: 'kg',
        },
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)

    render(<RestTimer />)

    const nextSetInfo = screen.getByText('Get ready for: 10 x 60 kg')
    expect(nextSetInfo).toBeInTheDocument()
  })

  it('displays reps only for bodyweight exercises', () => {
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: true,
        duration: 90,
        nextSetInfo: {
          reps: 15,
          weight: 0,
          unit: 'lbs',
        },
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)

    render(<RestTimer />)

    const nextSetInfo = screen.getByText('Get ready for: 15 reps')
    expect(nextSetInfo).toBeInTheDocument()
  })

  it('shows fallback message when no next set info', () => {
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: true,
        duration: 90,
        nextSetInfo: null,
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)

    render(<RestTimer />)

    const fallbackMessage = screen.getByText('Get ready for your next set!')
    expect(fallbackMessage).toBeInTheDocument()
  })

  it('does not render when rest timer is not active', () => {
    mockUseWorkoutStore.mockReturnValue({
      restTimerState: {
        isActive: false,
        duration: 0,
        nextSetInfo: null,
      },
      setRestTimerState: mockSetRestTimerState,
    } as any)

    render(<RestTimer />)

    expect(screen.queryByTestId('rest-timer-container')).not.toBeInTheDocument()
  })
})
