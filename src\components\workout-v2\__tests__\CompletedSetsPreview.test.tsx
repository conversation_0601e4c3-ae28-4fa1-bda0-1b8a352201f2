import { render, screen } from '@testing-library/react'
import { CompletedSetsPreview } from '../CompletedSetsPreview'
import type { WorkoutLogSerieModel } from '@/types'

// Extended type to match component expectations
type ExtendedWorkoutLogSerieModel = WorkoutLogSerieModel & {
  WarmUpReps?: number
  WarmUpWeightSet?: { Lb: number; Kg: number }
  IsSkipped?: boolean
}

describe('CompletedSetsPreview', () => {
  it('should return null when no completed sets', () => {
    const { container } = render(
      <CompletedSetsPreview completedSets={[]} unit="kg" />
    )

    expect(container.firstChild).toBeNull()
  })

  it('should display completed warmup set with correct format', () => {
    const completedSets: ExtendedWorkoutLogSerieModel[] = [
      {
        Id: 1,
        SetNo: '1',
        IsWarmups: true,
        IsFinished: true,
        WarmUpReps: 8,
        WarmUpWeightSet: { Kg: 20, Lb: 44 },
        Reps: 0,
        Weight: { Kg: 0, Lb: 0 },
      },
    ]

    render(<CompletedSetsPreview completedSets={completedSets} unit="kg" />)

    expect(screen.getByText('Completed Sets')).toBeInTheDocument()
    expect(screen.getByText('W1')).toBeInTheDocument()
    expect(screen.getByText('8 reps')).toBeInTheDocument()
    expect(screen.getByText('20 kg')).toBeInTheDocument()
  })

  it('should display multiple completed sets in order', () => {
    const completedSets: ExtendedWorkoutLogSerieModel[] = [
      {
        Id: 1,
        SetNo: '1',
        IsWarmups: true,
        IsFinished: true,
        WarmUpReps: 8,
        WarmUpWeightSet: { Kg: 20, Lb: 44 },
        Reps: 0,
        Weight: { Kg: 0, Lb: 0 },
      },
      {
        Id: 2,
        SetNo: '2',
        IsWarmups: true,
        IsFinished: true,
        WarmUpReps: 6,
        WarmUpWeightSet: { Kg: 30, Lb: 66 },
        Reps: 0,
        Weight: { Kg: 0, Lb: 0 },
      },
      {
        Id: 3,
        SetNo: '3',
        IsWarmups: false,
        IsFinished: true,
        Reps: 10,
        Weight: { Kg: 40, Lb: 88 },
      },
    ]

    render(<CompletedSetsPreview completedSets={completedSets} unit="kg" />)

    expect(screen.getByText('W1')).toBeInTheDocument()
    expect(screen.getByText('W2')).toBeInTheDocument()
    expect(screen.getByText('Set 1')).toBeInTheDocument()
    expect(screen.getByText('10 reps')).toBeInTheDocument()
    expect(screen.getByText('40 kg')).toBeInTheDocument()
  })

  it('should display weight in correct unit', () => {
    const completedSets: ExtendedWorkoutLogSerieModel[] = [
      {
        Id: 1,
        SetNo: '1',
        IsWarmups: false,
        IsFinished: true,
        Reps: 12,
        Weight: { Kg: 50, Lb: 110 },
      },
    ]

    const { rerender } = render(
      <CompletedSetsPreview completedSets={completedSets} unit="kg" />
    )
    expect(screen.getByText('50 kg')).toBeInTheDocument()

    rerender(<CompletedSetsPreview completedSets={completedSets} unit="lbs" />)
    expect(screen.getByText('110 lbs')).toBeInTheDocument()
  })

  it('should show completed sets with check mark icon', () => {
    const completedSets: ExtendedWorkoutLogSerieModel[] = [
      {
        Id: 1,
        SetNo: '1',
        IsWarmups: false,
        IsFinished: true,
        Reps: 10,
        Weight: { Kg: 40, Lb: 88 },
      },
    ]

    render(<CompletedSetsPreview completedSets={completedSets} unit="kg" />)

    // Check for completed indicator - check mark svg
    const checkIcon = screen
      .getByText('Set 1')
      .parentElement?.querySelector('svg')
    expect(checkIcon).toBeInTheDocument()
    expect(checkIcon).toHaveClass('w-4 h-4 text-green-500')
  })

  it('should handle rest-pause sets correctly', () => {
    const completedSets: ExtendedWorkoutLogSerieModel[] = [
      {
        Id: 1,
        SetNo: '1',
        IsWarmups: false,
        IsFinished: true,
        Reps: 10,
        Weight: { Kg: 40, Lb: 88 },
        SetTitle: '1st work set',
      },
      {
        Id: 2,
        SetNo: '2',
        IsWarmups: false,
        IsFinished: true,
        Reps: 5,
        Weight: { Kg: 40, Lb: 88 },
        SetTitle: "All right! Now let's try:",
        NbPause: 1,
      },
    ]

    render(<CompletedSetsPreview completedSets={completedSets} unit="kg" />)

    expect(screen.getByText('Set 1')).toBeInTheDocument()
    expect(screen.getByText('RP 2')).toBeInTheDocument() // Rest-pause indicator
  })
})
