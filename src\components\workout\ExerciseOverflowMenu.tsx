'use client'

import React, { useState, useRef, useEffect } from 'react'
import type { ExerciseModel } from '@/types'
import { KebabMenuIcon } from '../icons'

interface ExerciseOverflowMenuProps {
  exercise: ExerciseModel
  onSkip: (exerciseId: number) => void
  onViewHistory: (exerciseId: number) => void
  onAddNote: (exerciseId: number) => void
}

export function ExerciseOverflowMenu({
  exercise,
  onSkip,
  onViewHistory,
  onAddNote,
}: ExerciseOverflowMenuProps) {
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('touchstart', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('touchstart', handleClickOutside)
    }
  }, [isOpen])

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false)
        buttonRef.current?.focus()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      // Focus first menu item when menu opens
      const firstMenuItem = menuRef.current?.querySelector('[role="menuitem"]')
      if (firstMenuItem instanceof HTMLElement) {
        firstMenuItem.focus()
      }
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen])

  const handleToggleMenu = (event: React.MouseEvent | React.TouchEvent) => {
    event.stopPropagation()
    setIsOpen(!isOpen)
  }

  const handleMenuAction = (
    event: React.MouseEvent | React.TouchEvent,
    action: () => void
  ) => {
    event.stopPropagation()
    action()
    setIsOpen(false)
  }

  const getMenuPosition = () => {
    if (!buttonRef.current) return {}

    const rect = buttonRef.current.getBoundingClientRect()
    const menuHeight = 150 // Approximate menu height
    const spaceBelow = window.innerHeight - rect.bottom

    // Position menu above button if not enough space below
    if (spaceBelow < menuHeight) {
      return {
        bottom: `${window.innerHeight - rect.top + 5}px`,
        right: '0',
      }
    }

    return {
      top: `${rect.bottom + 5}px`,
      right: '0',
    }
  }

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        onClick={handleToggleMenu}
        onTouchEnd={handleToggleMenu}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            e.stopPropagation()
            setIsOpen(!isOpen)
          }
        }}
        className="p-2 text-text-tertiary hover:text-text-secondary transition-colors touch-manipulation"
        aria-label="Exercise options"
        aria-haspopup="true"
        aria-expanded={isOpen}
        type="button"
      >
        <KebabMenuIcon size={20} />
      </button>

      {isOpen && (
        <div
          ref={menuRef}
          role="menu"
          aria-label="Exercise options menu"
          className="absolute z-50 min-w-[160px] rounded-theme bg-bg-primary shadow-theme-lg border border-brand-primary/10 py-1"
          style={getMenuPosition()}
        >
          <button
            role="menuitem"
            onClick={(e) => handleMenuAction(e, () => onSkip(exercise.Id))}
            onTouchEnd={(e) => handleMenuAction(e, () => onSkip(exercise.Id))}
            className="w-full px-4 py-2 text-left text-sm text-text-primary hover:bg-bg-secondary transition-colors touch-manipulation flex items-center gap-2"
            tabIndex={0}
          >
            <svg
              className="h-4 w-4 text-text-tertiary"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 5l7 7-7 7M5 5l7 7-7 7"
              />
            </svg>
            Skip Exercise
          </button>

          <button
            role="menuitem"
            onClick={(e) =>
              handleMenuAction(e, () => onViewHistory(exercise.Id))
            }
            onTouchEnd={(e) =>
              handleMenuAction(e, () => onViewHistory(exercise.Id))
            }
            className="w-full px-4 py-2 text-left text-sm text-text-primary hover:bg-bg-secondary transition-colors touch-manipulation flex items-center gap-2"
            tabIndex={0}
          >
            <svg
              className="h-4 w-4 text-text-tertiary"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            View History
          </button>

          <button
            role="menuitem"
            onClick={(e) => handleMenuAction(e, () => onAddNote(exercise.Id))}
            onTouchEnd={(e) =>
              handleMenuAction(e, () => onAddNote(exercise.Id))
            }
            className="w-full px-4 py-2 text-left text-sm text-text-primary hover:bg-bg-secondary transition-colors touch-manipulation flex items-center gap-2"
            tabIndex={0}
          >
            <svg
              className="h-4 w-4 text-text-tertiary"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
              />
            </svg>
            Add Note
          </button>
        </div>
      )}
    </div>
  )
}
