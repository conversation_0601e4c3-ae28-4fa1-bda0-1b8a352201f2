import { test, expect } from '@playwright/test'
import { login } from './helpers/auth'

test.describe('Workout Preload', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await login(page)
  })

  test('should preload exercises when starting workout', async ({ page }) => {
    // Navigate to workout page
    await page.goto('/workout')

    // Wait for workout overview to load
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Click start workout button
    await page.click('button:has-text("Start Workout")')

    // Wait for navigation to exercise page (supports V1 and V2)
    await page.waitForURL(/\/workout\/exercise(-v2)?\/\d+/)

    // Verify we're on an exercise page with content
    await expect(page.locator('h1')).toBeVisible()

    // Verify exercise details are loaded (not blank)
    await expect(page.locator('[data-testid="exercise-name"]')).toBeVisible()
  })

  test('should navigate to correct first exercise', async ({ page }) => {
    // Navigate to workout page
    await page.goto('/workout')

    // Wait for workout overview to load
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Get the first exercise card's ID
    const firstExerciseCard = page
      .locator('[data-testid^="exercise-card-"]')
      .first()
    // Store the test id for future use if needed
    await firstExerciseCard.getAttribute('data-testid')

    // Click start workout button
    await page.click('button:has-text("Start Workout")')

    // Wait for navigation (supports V1 and V2)
    await page.waitForURL(/\/workout\/exercise(-v2)?\/\d+/)

    // Verify we navigated to the correct exercise
    const currentUrl = page.url()
    expect(currentUrl).toMatch(/\/workout\/exercise(-v2)?\/\d+/)
  })

  test('should handle workout with no exercises gracefully', async ({
    page,
  }) => {
    // This test would require mocking the API to return a workout with no exercises
    // For now, we'll just verify the start button is present
    await page.goto('/workout')

    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Verify start button exists
    await expect(page.locator('button:has-text("Start Workout")')).toBeVisible()
  })
})
