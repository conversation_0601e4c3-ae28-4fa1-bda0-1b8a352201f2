import { describe, it, expect } from 'vitest'
import { createWorkoutSets } from '../createWorkoutSetsMAUI'
import type { ExerciseModel, RecommendationModel } from '@/types'

describe('createWorkoutSets - Increment Defaults', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Test Exercise',
    IsBodyweight: false,
    IsTimeBased: false,
    IsUnilateral: false,
    IsNormalSets: true,
    IsFlexibility: false,
    BodyPartId: 1,
    EquipmentId: 1,
    VideoUrl: '',
    Notes: '',
    IsFinished: false,
  }

  it('should use correct default increment values when recommendation has no Increments', () => {
    // Test for the bug: current code uses { Kg: 2.5, Lb: 5 } but should use { Kg: 1, Lb: 2.5 }
    const recommendationWithoutIncrements: RecommendationModel = {
      Id: 1,
      ExerciseId: 1,
      Weight: { Kg: 100, Lb: 220 },
      Reps: 10,
      Series: 3,
      WarmupsCount: 0,
      WarmUpsList: [],
      FirstWorkSetReps: 10,
      FirstWorkSetWeight: { Kg: 100, Lb: 220 },
      // Intentionally omitting Increments to test defaults
      Min: { Kg: 0, Lb: 0 },
      Max: { Kg: 200, Lb: 440 },
      RIR: 2,
      RestTime: 180,
      IsEasy: false,
      IsMedium: true,
      IsBodyweight: false,
      IsNormalSets: true,
      WeightIncrement: 5, // This should not be used
      RepsIncrement: 1,
      NbPause: 0,
      NbPauses: 0,
      NbRepsPauses: 0,
      OneRMProgress: 1.0,
      PreviousReps: 8,
      PreviousWeight: { Kg: 95, Lb: 209 },
      Speed: 1.0,
    }

    const result = createWorkoutSets(
      mockExercise,
      recommendationWithoutIncrements,
      true
    )

    // Get any work set (not warmup) to check its Increments property
    const workSet = result.find((set) => !set.IsWarmups)

    // This test should FAIL initially, showing the bug
    // Expected: kg should default to 1, lbs should default to 2.5
    expect(workSet?.Increments).toEqual({ Kg: 1, Lb: 2.5 })

    // The bug is that it currently returns { Kg: 2.5, Lb: 5 }
    // This test will fail until we fix the implementation
  })

  it('should preserve custom increment values when provided in recommendation', () => {
    const recommendationWithCustomIncrements: RecommendationModel = {
      Id: 1,
      ExerciseId: 1,
      Weight: { Kg: 100, Lb: 220 },
      Reps: 10,
      Series: 3,
      WarmupsCount: 0,
      WarmUpsList: [],
      FirstWorkSetReps: 10,
      FirstWorkSetWeight: { Kg: 100, Lb: 220 },
      Increments: { Kg: 5, Lb: 10 }, // Custom values
      Min: { Kg: 0, Lb: 0 },
      Max: { Kg: 200, Lb: 440 },
      RIR: 2,
      RestTime: 180,
      IsEasy: false,
      IsMedium: true,
      IsBodyweight: false,
      IsNormalSets: true,
      WeightIncrement: 5,
      RepsIncrement: 1,
      NbPause: 0,
      NbPauses: 0,
      NbRepsPauses: 0,
      OneRMProgress: 1.0,
      PreviousReps: 8,
      PreviousWeight: { Kg: 95, Lb: 209 },
      Speed: 1.0,
    }

    const result = createWorkoutSets(
      mockExercise,
      recommendationWithCustomIncrements,
      true
    )
    const workSet = result.find((set) => !set.IsWarmups)

    // Custom values should be preserved
    expect(workSet?.Increments).toEqual({ Kg: 5, Lb: 10 })
  })
})
