import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SetCell } from '@/components/workout/SetCell'

describe('Weight Increment Functionality', () => {
  const mockOnWeightChange = vi.fn()
  const defaultProps = {
    setNo: 1,
    reps: 10,
    weight: 100,
    isNext: true,
    unit: 'lbs' as const,
    onWeightChange: mockOnWeightChange,
  }

  beforeEach(() => {
    mockOnWeightChange.mockClear()
  })

  describe('Default increments', () => {
    it('should increment weight by 2.5 lbs when unit is lbs', async () => {
      const user = userEvent.setup()
      render(<SetCell {...defaultProps} />)

      const upArrow = screen.getByLabelText('Increase weight')
      await user.click(upArrow)

      expect(mockOnWeightChange).toHaveBeenCalledWith(102.5)
    })

    it('should decrement weight by 2.5 lbs when unit is lbs', async () => {
      const user = userEvent.setup()
      render(<SetCell {...defaultProps} />)

      const downArrow = screen.getByLabelText('Decrease weight')
      await user.click(downArrow)

      expect(mockOnWeightChange).toHaveBeenCalledWith(97.5)
    })

    it('should increment weight by 1 kg when unit is kg', async () => {
      const user = userEvent.setup()
      render(<SetCell {...defaultProps} unit="kg" weight={50} />)

      const upArrow = screen.getByLabelText('Increase weight')
      await user.click(upArrow)

      expect(mockOnWeightChange).toHaveBeenCalledWith(51)
    })

    it('should decrement weight by 1 kg when unit is kg', async () => {
      const user = userEvent.setup()
      render(<SetCell {...defaultProps} unit="kg" weight={50} />)

      const downArrow = screen.getByLabelText('Decrease weight')
      await user.click(downArrow)

      expect(mockOnWeightChange).toHaveBeenCalledWith(49)
    })
  })

  describe('Custom increments from recommendation', () => {
    it('should use custom increment from recommendation for lbs', async () => {
      const user = userEvent.setup()
      const recommendation = {
        ExerciseId: 1,
        Weight: { Lb: 100, Kg: 45 },
        Reps: 10,
        Increments: { Lb: 2.5, Kg: 1 },
      }

      render(<SetCell {...defaultProps} recommendation={recommendation} />)

      const upArrow = screen.getByLabelText('Increase weight')
      await user.click(upArrow)

      expect(mockOnWeightChange).toHaveBeenCalledWith(102.5)
    })

    it('should use custom increment from recommendation for kg', async () => {
      const user = userEvent.setup()
      const recommendation = {
        ExerciseId: 1,
        Weight: { Lb: 100, Kg: 45 },
        Reps: 10,
        Increments: { Lb: 2.5, Kg: 1 },
      }

      render(
        <SetCell
          {...defaultProps}
          unit="kg"
          weight={45}
          recommendation={recommendation}
        />
      )

      const upArrow = screen.getByLabelText('Increase weight')
      await user.click(upArrow)

      expect(mockOnWeightChange).toHaveBeenCalledWith(46)
    })

    it('should fall back to default if Increments is missing', async () => {
      const user = userEvent.setup()
      const recommendation = {
        ExerciseId: 1,
        Weight: { Lb: 100, Kg: 45 },
        Reps: 10,
        // No Increments property
      }

      render(<SetCell {...defaultProps} recommendation={recommendation} />)

      const upArrow = screen.getByLabelText('Increase weight')
      await user.click(upArrow)

      expect(mockOnWeightChange).toHaveBeenCalledWith(102.5)
    })
  })

  describe('Edge cases', () => {
    it('should allow weight to go to 0 but not negative', async () => {
      const user = userEvent.setup()
      render(<SetCell {...defaultProps} weight={1} />)

      const downArrow = screen.getByLabelText('Decrease weight')
      await user.click(downArrow)

      // Should set weight to 0 when decrementing from 1
      expect(mockOnWeightChange).toHaveBeenCalledWith(0)
    })

    it('should keep weight at 0 when already at 0', async () => {
      const user = userEvent.setup()
      render(<SetCell {...defaultProps} weight={0} />)

      const downArrow = screen.getByLabelText('Decrease weight')
      await user.click(downArrow)

      // Should keep weight at 0 when already at 0
      expect(mockOnWeightChange).toHaveBeenCalledWith(0)
    })

    it('should not show arrows for finished sets', () => {
      render(<SetCell {...defaultProps} isFinished />)

      expect(screen.queryByLabelText('Increase weight')).not.toBeInTheDocument()
      expect(screen.queryByLabelText('Decrease weight')).not.toBeInTheDocument()
    })

    it('should not show arrows for non-active sets', () => {
      render(<SetCell {...defaultProps} isNext={false} />)

      expect(screen.queryByLabelText('Increase weight')).not.toBeInTheDocument()
      expect(screen.queryByLabelText('Decrease weight')).not.toBeInTheDocument()
    })
  })
})
