import { render } from '@testing-library/react'
import { SetScreenLoadingState } from '../SetScreenLoadingState'

describe('SetScreenLoadingState', () => {
  it('should show golden checkmark with loading and exercise name with ellipsis when loading recommendations', () => {
    const { getByText, getByTestId } = render(
      <SetScreenLoadingState
        exerciseName="Bench Press"
        showDetailedSkeleton
        isLoadingRecommendations
      />
    )

    // Should show golden checkmark (✓)
    expect(getByTestId('golden-checkmark')).toBeInTheDocument()

    // Should show "Loading [exercise name]..." text
    expect(getByText('Loading Bench Press...')).toBeInTheDocument()
  })

  it('should show golden checkmark with loading and generic text with ellipsis when no exercise name provided', () => {
    const { getByText, getByTestId } = render(
      <SetScreenLoadingState showDetailedSkeleton isLoadingRecommendations />
    )

    // Should show golden checkmark (✓)
    expect(getByTestId('golden-checkmark')).toBeInTheDocument()

    // Should show "Loading exercise..." text
    expect(getByText('Loading exercise...')).toBeInTheDocument()
  })

  it('should show simple loading when not loading recommendations', () => {
    const { getByText, container } = render(
      <SetScreenLoadingState exerciseName="Squats" />
    )

    expect(getByText('Loading Squats...')).toBeInTheDocument()

    // Should not show skeleton
    const skeletons = container.querySelectorAll('.animate-pulse')
    expect(skeletons.length).toBe(0)
  })

  it('should show default loading message when no props provided', () => {
    const { getByText } = render(<SetScreenLoadingState />)

    expect(getByText('Loading exercise data...')).toBeInTheDocument()
  })
})
