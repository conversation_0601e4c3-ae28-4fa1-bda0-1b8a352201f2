import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { useWorkoutStore } from '../workoutStore'
import type {
  GetUserProgramInfoResponseModel,
  WorkoutTemplateModel,
  RecommendationModel,
} from '@/types'

// Mock data
const mockUserProgramInfo: GetUserProgramInfoResponseModel = {
  UserId: 'test-user',
  WeeklyStatus: 'Week 1',
  ProgramLabel: 'Test Program',
  NbDaysInTheWeek: 5,
  NbNonTrainingDays: 2,
  MondayIsFirst: false,
  TimeLogged: '2024-01-01T10:00:00',
  NextWorkoutDayText: 'Today',
  IsInIntroWorkout: false,
  IsInFirstWeek: true,
  TodaysWorkoutId: '1234',
  TodaysWorkoutText: 'Push Day',
  RecommendedProgram: {
    Id: 1,
    Label: 'Beginner Program',
    RemainingToLevelUp: 10,
    IconUrl: 'https://example.com/icon.png',
  },
  NextWorkoutTemplate: {
    Id: 1,
    Label: 'Push Day',
    IsSystemExercise: false,
    Exercises: [
      {
        Id: 123,
        Label: 'Bench Press',
        Path: 'chest/benchpress',
        TargetWeight: { Mass: 100, MassUnit: 'lbs' },
        TargetReps: 8,
        IsWarmup: false,
        HasPastLogs: true,
      },
      {
        Id: 456,
        Label: 'Shoulder Press',
        Path: 'shoulders/press',
        TargetWeight: { Mass: 60, MassUnit: 'lbs' },
        TargetReps: 10,
        IsWarmup: false,
        HasPastLogs: true,
      },
    ],
  },
  NextNonTrainingDay: '2024-01-03',
  MondayHere: '2024-01-01',
  NextIntensityTechnique: 'Standard',
  ServerTimeUtc: '2024-01-01T10:00:00Z',
  MaxWorkoutSets: 20,
  NbMediumSets: 5,
  NbChallenges: 3,
  WorkoutTemplates: [],
}

const mockWorkout: WorkoutTemplateModel = {
  Id: 1,
  UserId: 'test-user',
  Label: 'Push Day',
  Exercises: [
    {
      Id: 123,
      Label: 'Bench Press',
      Path: 'chest/benchpress',
      TargetWeight: { Mass: 100, MassUnit: 'lbs' },
      TargetReps: 8,
      IsWarmup: false,
      HasPastLogs: true,
    },
  ],
  IsSystemExercise: false,
  WorkoutSettingsModel: {
    Id: 1,
    Pause: 120,
    Equipment: '',
    ChildWorkoutTemplateId: null,
    SetsModel: null,
    WorkoutProgramId: 1,
    IsFirstSet: false,
    IsFail: false,
    NbRepsMinimalInc: null,
    AvgDuration: null,
    IsNotRealData: false,
  },
}

const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 10,
  Weight: { Lb: 100, Kg: 45.36 },
  Increments: { Lb: 2.5, Kg: 1 },
  LastLogDate: new Date('2024-01-01'),
  LastReps: 8,
  LastWeight: { Lb: 95, Kg: 43.09 },
  LastSeries: 3,
  IsWaitingForSwap: false,
  RM: 105,
  RIR: 2,
  History: [],
  IsBodyweight: false,
  IsTimeBased: false,
  IsUnilateral: false,
  IsPlate: false,
  RecommendedCountdown: null,
  NegativeWeight: null,
  PartialWeight: null,
  IsNegative: false,
  IsPartial: false,
}

describe('Workout Store - Complete Integration', () => {
  beforeEach(() => {
    // Reset store to initial state
    useWorkoutStore.setState({
      cachedData: {
        userProgramInfo: null,
        userWorkouts: null,
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: 0,
          userWorkouts: 0,
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
      hasHydrated: false,
      cacheVersion: 1,
      cacheStats: {
        hits: 0,
        misses: 0,
        hitRate: 0,
        operationCount: 0,
        averageLatency: 0,
        totalLatency: 0,
        totalSize: 0,
        itemCount: 0,
        oldestDataAge: 0,
        freshDataCount: 0,
        staleDataCount: 0,
        hydrationTime: 0,
      },
    })

    // Clear localStorage
    localStorage.clear()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Complete User Journey with Caching', () => {
    it('should provide instant workout display on repeat visit', async () => {
      const store = useWorkoutStore.getState()

      // Simulate initial visit - cache data
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setCachedUserWorkouts([mockWorkout])
      store.setCachedExerciseRecommendation(123, mockRecommendation)
      store.setHasHydrated(true) // Enable cache reads

      // Verify data is cached
      expect(store.getCachedUserProgramInfo()).toEqual(mockUserProgramInfo)
      expect(store.getCachedUserWorkouts()).toEqual([mockWorkout])
      expect(store.getCachedExerciseRecommendation(123)).toEqual(
        mockRecommendation
      )

      // Note: In a real browser environment, Zustand persist would save to localStorage
      // In tests, we verify the cache methods work correctly
      // For true persistence testing, E2E tests with a real browser would be needed

      // Verify cache is available for instant display
      const cachedProgramInfo = store.getCachedUserProgramInfo()
      const cachedWorkouts = store.getCachedUserWorkouts()
      const cachedRecommendation = store.getCachedExerciseRecommendation(123)

      expect(cachedProgramInfo).toBeTruthy()
      expect(cachedWorkouts).toBeTruthy()
      expect(cachedRecommendation).toBeTruthy()
    })

    it('should handle offline/online transitions with cache', () => {
      const store = useWorkoutStore.getState()

      // Start online and cache data
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setCachedUserWorkouts([mockWorkout])
      store.setHasHydrated(true)

      // Verify data accessible while "offline"
      const programInfo = store.getCachedUserProgramInfo()
      const workouts = store.getCachedUserWorkouts()

      expect(programInfo).toEqual(mockUserProgramInfo)
      expect(workouts).toEqual([mockWorkout])

      // Cache stats should track operations
      const stats = store.getCacheStats()
      expect(stats.hits).toBe(2)
      expect(stats.misses).toBe(0)
      expect(stats.hitRate).toBeCloseTo(1.0, 2)
    })

    it('should maintain cache performance under load', () => {
      const store = useWorkoutStore.getState()
      const operationCount = 100

      // Enable cache reads
      store.setHasHydrated(true)

      // Perform many cache operations
      const startTime = performance.now()

      for (let i = 0; i < operationCount; i++) {
        store.setCachedExerciseRecommendation(i, {
          ...mockRecommendation,
          Reps: i,
        })
      }

      const writeTime = performance.now() - startTime
      const readStartTime = performance.now()

      // Read all cached data
      for (let i = 0; i < operationCount; i++) {
        store.getCachedExerciseRecommendation(i)
      }

      const readTime = performance.now() - readStartTime

      // Performance assertions
      expect(writeTime).toBeLessThan(50) // Writing 100 items < 50ms
      expect(readTime).toBeLessThan(10) // Reading 100 items < 10ms

      // Verify cache stats
      const stats = store.getCacheStats()
      expect(stats.operationCount).toBe(operationCount)
      expect(stats.averageLatency).toBeLessThan(1) // <1ms per operation
    })
  })

  describe('Cache Size Management', () => {
    it('should limit cached exercise recommendations', () => {
      const store = useWorkoutStore.getState()
      const maxRecommendations = 50

      // Add more than max recommendations
      for (let i = 0; i < maxRecommendations + 10; i++) {
        store.setCachedExerciseRecommendation(i, {
          ...mockRecommendation,
          Reps: i,
        })
      }

      // Verify limit is enforced
      const cachedCount = Object.keys(
        store.cachedData.exerciseRecommendations
      ).length
      expect(cachedCount).toBeLessThanOrEqual(maxRecommendations)
    })

    it('should calculate cache size accurately', () => {
      const store = useWorkoutStore.getState()

      // Add various data types
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setCachedUserWorkouts([mockWorkout])
      store.setCachedExerciseRecommendation(123, mockRecommendation)

      // Check cache size
      const stats = store.getCacheStats()
      expect(stats.totalSize).toBeGreaterThan(0)
      expect(stats.totalSize).toBeLessThan(500 * 1024) // Under 500KB
      expect(stats.itemCount).toBe(3) // programInfo + workouts + 1 recommendation
    })
  })

  describe('Cache Corruption Recovery', () => {
    it('should handle corrupted cache data gracefully', () => {
      // Corrupt the localStorage data
      localStorage.setItem(
        'drmuscle-workout',
        '{"state":{"cachedData":null,"invalidJSON'
      )

      // Store should initialize with defaults on corruption
      const store = useWorkoutStore.getState()
      expect(store.cachedData).toBeDefined()
      expect(store.cachedData.userProgramInfo).toBeNull()
      expect(store.cachedData.exerciseRecommendations).toEqual({})
    })

    it('should handle cache version mismatch', () => {
      const store = useWorkoutStore.getState()

      // Set some cached data
      store.setCachedUserProgramInfo(mockUserProgramInfo)

      // Simulate version mismatch
      store.handleCacheVersionMismatch()

      // Cache should be cleared
      expect(store.getCachedUserProgramInfo()).toBeNull()
      expect(store.cacheVersion).toBe(1)
    })
  })

  describe('Production Performance', () => {
    it('should meet performance targets for cache operations', () => {
      const store = useWorkoutStore.getState()
      const iterations = 1000

      // Measure write performance
      const writeStart = performance.now()
      for (let i = 0; i < iterations; i++) {
        store.setCachedExerciseRecommendation(i % 50, mockRecommendation)
      }
      const writeTime = performance.now() - writeStart
      const avgWriteTime = writeTime / iterations

      // Measure read performance
      const readStart = performance.now()
      for (let i = 0; i < iterations; i++) {
        store.getCachedExerciseRecommendation(i % 50)
      }
      const readTime = performance.now() - readStart
      const avgReadTime = readTime / iterations

      // Assert performance targets
      expect(avgWriteTime).toBeLessThan(1) // <1ms average write
      expect(avgReadTime).toBeLessThan(0.1) // <0.1ms average read

      // Check cache health
      const health = store.getCacheHealth()
      expect(health.isHealthy).toBe(true)
    })

    it('should maintain high cache hit rate', () => {
      const store = useWorkoutStore.getState()

      // Enable cache reads
      store.setHasHydrated(true)

      // Populate cache
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setCachedUserWorkouts([mockWorkout])
      for (let i = 0; i < 10; i++) {
        store.setCachedExerciseRecommendation(i, mockRecommendation)
      }

      // Simulate typical usage pattern
      store.getCachedUserProgramInfo() // Hit
      store.getCachedUserWorkouts() // Hit
      store.getCachedExerciseRecommendation(0) // Hit
      store.getCachedExerciseRecommendation(1) // Hit
      store.getCachedExerciseRecommendation(99) // Miss
      store.getCachedTodaysWorkout() // Miss

      // Verify high hit rate
      const stats = store.getCacheStats()
      expect(stats.hitRate).toBeGreaterThan(0.6) // >60% hit rate
    })
  })

  describe('Memory Management', () => {
    it('should not cause memory leaks with repeated operations', () => {
      const store = useWorkoutStore.getState()

      // Clear cache first to get baseline
      store.clearAllCache()
      const baselineMemory = store.getCacheSize()

      // Set initial data
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setCachedUserWorkouts([mockWorkout])
      store.setCachedExerciseRecommendation(1, mockRecommendation)
      const initialMemory = store.getCacheSize()

      // Perform many operations
      for (let i = 0; i < 100; i++) {
        // Overwrite same keys repeatedly
        store.setCachedUserProgramInfo(mockUserProgramInfo)
        store.setCachedUserWorkouts([mockWorkout])
        store.setCachedExerciseRecommendation(1, mockRecommendation)
      }

      const finalMemory = store.getCacheSize()

      // Memory should not grow significantly when overwriting same keys
      // Allow some variance due to JSON serialization
      expect(finalMemory).toBeLessThanOrEqual(initialMemory * 1.1) // Max 10% growth
      expect(finalMemory).toBeGreaterThan(baselineMemory) // Should have data
    })

    it('should clean up expired data on hydration', () => {
      const store = useWorkoutStore.getState()

      // Add stale data by directly setting state
      useWorkoutStore.setState({
        cachedData: {
          ...store.cachedData,
          userProgramInfo: mockUserProgramInfo,
          lastUpdated: {
            ...store.cachedData.lastUpdated,
            userProgramInfo: Date.now() - 25 * 60 * 60 * 1000, // 25 hours old
          },
        },
      })

      // Clear expired cache
      store.clearExpiredCache()

      // Get fresh state
      const updatedStore = useWorkoutStore.getState()

      // Stale data should be removed
      expect(updatedStore.getCachedUserProgramInfo()).toBeNull()
      expect(updatedStore.cachedData.lastUpdated.userProgramInfo).toBe(0)
    })
  })
})
