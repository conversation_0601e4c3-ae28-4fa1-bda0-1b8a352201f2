/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/design-system/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    // Mobile-first screens (default breakpoints are mobile-first)
    screens: {
      'sm': '640px',  // Small devices (large phones)
      'md': '768px',  // Medium devices (tablets)
      'lg': '1024px', // Large devices (laptops)
      'xl': '1280px', // Extra large devices (desktops)
    },
    extend: {
      // Font families with comprehensive fallbacks
      fontFamily: {
        'sans': ['var(--font-body)', 'system-ui', 'sans-serif'],
        'heading': ['var(--font-heading)', 'system-ui', 'sans-serif'],
        // Theme-specific fonts
        'playfair': ['"Playfair Display"', 'serif'],
        'bebas': ['"Bebas Neue"', 'sans-serif'],
        'didot': ['Didot', 'Georgia', 'serif'],
        'inter': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],
        'sf-pro': ['"SF Pro Display"', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],
        'helvetica': ['"Helvetica Neue"', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],
      },
      // Design system shadows
      boxShadow: {
        'theme-sm': 'var(--shadow-sm)',
        'theme-md': 'var(--shadow-md)',
        'theme-lg': 'var(--shadow-lg)',
        'theme-xl': 'var(--shadow-xl)',
      },
      // Theme-specific border radius
      borderRadius: {
        'theme': 'var(--radius-button)',
      },
      // Mobile-optimized spacing
      spacing: {
        'touch': '44px', // Minimum touch target size
        'safe-top': 'env(safe-area-inset-top)',
        'safe-bottom': 'env(safe-area-inset-bottom)',
        'safe-left': 'env(safe-area-inset-left)',
        'safe-right': 'env(safe-area-inset-right)',
      },
      // Mobile-friendly typography
      fontSize: {
        'touch': ['18px', '24px'], // Touch-friendly text size
        'mobile-xl': ['24px', '32px'],
        'mobile-2xl': ['32px', '40px'],
        // Ultra-minimal theme text sizes with golden ratio
        'um-2xl': ['1.618rem', { lineHeight: '1.618' }], // ~25.89px
        'um-3xl': ['2.618rem', { lineHeight: '1.618' }], // ~41.89px
        'um-4xl': ['4.236rem', { lineHeight: '1.2' }], // ~67.78px
      },
      // Design System colors using CSS variables
      colors: {
        // Dr. Muscle X brand colors
        'brand': {
          'primary': 'var(--color-brand-primary)',
          'secondary': 'var(--color-brand-secondary)',
          'accent': 'var(--color-brand-accent)',
          'gold-start': '#d4af37',
          'gold-end': '#f7e98e',
        },
        'bg': {
          'primary': 'var(--color-bg-primary)',
          'secondary': 'var(--color-bg-secondary)',
          'tertiary': 'var(--color-bg-tertiary)',
          'overlay': 'var(--color-bg-overlay)',
        },
        'text': {
          'primary': 'var(--color-text-primary)',
          'secondary': 'var(--color-text-secondary)',
          'tertiary': 'var(--color-text-tertiary)',
          'inverse': 'var(--color-text-inverse)',
        },
        'border': {
          'primary': 'var(--color-text-tertiary)',
          'secondary': 'var(--color-text-secondary)',
        },
        // Semantic colors
        'error': 'var(--color-error, #ef4444)',
        'warning': 'var(--color-warning, #f59e0b)',
        'success': 'var(--color-success, #10b981)',
        'info': 'var(--color-info, #3b82f6)',
        // Legacy colors (for gradual migration)
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          900: '#1e3a8a',
        },
        // iOS/Android native-like colors
        system: {
          blue: '#007AFF',
          green: '#34C759',
          red: '#FF3B30',
          orange: '#FF9500',
          background: '#F2F2F7',
          secondaryBackground: '#FFFFFF',
        },
        // Ultra-minimal colors
        'accent': '#0066FF',
        'black': '#000000',
        'gray': {
          200: '#E5E5E5',
          300: '#D4D4D4',
          500: '#666666',
          700: '#333333',
        }
      },
      // Mobile animations
      animation: {
        'touch-bounce': 'touchBounce 0.2s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'fade-in': 'fadeIn 0.2s ease-in',
        'pulse-glow': 'pulseGlow 0.3s ease-in-out',
      },
      keyframes: {
        touchBounce: {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(0.95)' },
        },
        slideUp: {
          '0%': { transform: 'translateY(100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        pulseGlow: {
          '0%': { filter: 'brightness(1) drop-shadow(0 0 0 transparent)' },
          '50%': { filter: 'brightness(1.2) drop-shadow(0 0 8px rgba(212, 175, 55, 0.5))' },
          '100%': { filter: 'brightness(1) drop-shadow(0 0 0 transparent)' },
        },
      },
      // Mobile-specific utilities
      minHeight: {
        'touch': '44px',
        'screen-safe': 'calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom))',
      },
      maxWidth: {
        'mobile': '414px', // iPhone Pro Max width
      },
      // Ultra-minimal theme spacing using golden ratio
      padding: {
        'um-xs': '0.236rem', // ~3.78px
        'um-sm': '0.618rem', // ~9.89px
        'um-md': '1.618rem', // ~25.89px
        'um-lg': '2.618rem', // ~41.89px
        'um-xl': '4.236rem', // ~67.78px
      },
      height: {
        'um-xs': '0.236rem', // ~3.78px
        'um-sm': '0.618rem', // ~9.89px
        'um-md': '1.618rem', // ~25.89px
        'um-lg': '2.618rem', // ~41.89px
        'um-xl': '4.236rem', // ~67.78px
      },
      width: {
        'um-xs': '0.236rem', // ~3.78px
        'um-sm': '0.618rem', // ~9.89px
        'um-md': '1.618rem', // ~25.89px
        'um-lg': '2.618rem', // ~41.89px
        'um-xl': '4.236rem', // ~67.78px
      },
      margin: {
        'um-xs': '0.236rem', // ~3.78px
        'um-sm': '0.618rem', // ~9.89px
        'um-md': '1.618rem', // ~25.89px
        'um-lg': '2.618rem', // ~41.89px
        'um-xl': '4.236rem', // ~67.78px
      },
      borderWidth: {
        'hairline': '0.5px',
      },
      // Subtle Depth Theme Extensions
      backgroundImage: {
        'gradient-premium': 'var(--gradient-bg-premium)',
        'gradient-metallic-gold': 'var(--gradient-metallic-gold)',
        'gradient-metallic-silver': 'var(--gradient-metallic-silver)',
        'gradient-overlay-subtle': 'var(--gradient-overlay-subtle)',
        'gradient-overlay-premium': 'var(--gradient-overlay-premium)',
        'gradient-shimmer': 'var(--gradient-shimmer)',
      },
      letterSpacing: {
        'luxury': 'var(--letter-spacing-wide)',
        'luxury-wide': 'var(--letter-spacing-wider)',
        'luxury-widest': 'var(--letter-spacing-widest)',
        'tight': '-0.02em',
      },
      lineHeight: {
        'golden': '1.618',
      },
      fontWeight: {
        'light': '100',
        'regular': '300',
        'medium': '400',
        'semibold': '500',
        'bold': '700',
      },
      textShadow: {
        'sm': 'var(--text-shadow-sm)',
        'md': 'var(--text-shadow-md)',
        'lg': 'var(--text-shadow-lg)',
        'gold': 'var(--text-shadow-gold)',
      }
    },
  },
  plugins: [
    // Add mobile-specific utility classes
    function({ addUtilities, addComponents }) {
      addUtilities({
        '.tap-highlight-none': {
          '-webkit-tap-highlight-color': 'transparent',
        },
        '.touch-manipulation': {
          'touch-action': 'manipulation',
        },
        '.overscroll-none': {
          'overscroll-behavior': 'none',
        },
        '.safe-area-inset': {
          'padding-top': 'env(safe-area-inset-top)',
          'padding-bottom': 'env(safe-area-inset-bottom)',
          'padding-left': 'env(safe-area-inset-left)',
          'padding-right': 'env(safe-area-inset-right)',
        },
        '.pb-safe': {
          'padding-bottom': 'env(safe-area-inset-bottom)',
        },
        // Glassmorphism utilities
        '.backdrop-blur-sm': {
          'backdrop-filter': 'blur(4px)',
          '-webkit-backdrop-filter': 'blur(4px)',
        },
        '.backdrop-blur-md': {
          'backdrop-filter': 'blur(8px)',
          '-webkit-backdrop-filter': 'blur(8px)',
        },
        '.backdrop-blur-lg': {
          'backdrop-filter': 'blur(12px)',
          '-webkit-backdrop-filter': 'blur(12px)',
        },
        '.backdrop-blur-xl': {
          'backdrop-filter': 'blur(16px)',
          '-webkit-backdrop-filter': 'blur(16px)',
        },
        '.backdrop-saturate-150': {
          'backdrop-filter': 'saturate(1.5)',
          '-webkit-backdrop-filter': 'saturate(1.5)',
        },
        '.backdrop-saturate-180': {
          'backdrop-filter': 'saturate(1.8)',
          '-webkit-backdrop-filter': 'saturate(1.8)',
        },
        '.backdrop-saturate-200': {
          'backdrop-filter': 'saturate(2)',
          '-webkit-backdrop-filter': 'saturate(2)',
        },
        // Text shadow utilities for subtle-depth theme
        '.text-shadow-sm': {
          'text-shadow': 'var(--text-shadow-sm)',
        },
        '.text-shadow-md': {
          'text-shadow': 'var(--text-shadow-md)',
        },
        '.text-shadow-lg': {
          'text-shadow': 'var(--text-shadow-lg)',
        },
        '.text-shadow-gold': {
          'text-shadow': 'var(--text-shadow-gold)',
        },
        '.text-shadow-none': {
          'text-shadow': 'none',
        },
        '.scrollbar-hide': {
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
        },
        '.scrollbar-hide::-webkit-scrollbar': {
          'display': 'none',
        },
      })
      
      // Add glassmorphism component classes
      addComponents({
        '.glass': {
          background: 'var(--color-bg-glass-1, rgba(255, 255, 255, 0.08))',
          backdropFilter: 'blur(var(--glass-blur, 12px)) saturate(var(--glass-saturation, 1.8))',
          WebkitBackdropFilter: 'blur(var(--glass-blur, 12px)) saturate(var(--glass-saturation, 1.8))',
          border: '1px solid var(--glass-border-light, rgba(255, 255, 255, 0.2))',
        },
        '.glass-dark': {
          background: 'var(--color-bg-glass-2, rgba(255, 255, 255, 0.12))',
          backdropFilter: 'blur(var(--glass-blur, 12px)) saturate(var(--glass-saturation, 1.8))',
          WebkitBackdropFilter: 'blur(var(--glass-blur, 12px)) saturate(var(--glass-saturation, 1.8))',
          border: '1px solid var(--glass-border-dark, rgba(255, 255, 255, 0.1))',
        },
        '.glass-light': {
          background: 'var(--color-bg-glass-3, rgba(255, 255, 255, 0.16))',
          backdropFilter: 'blur(calc(var(--glass-blur, 12px) * 0.6)) saturate(calc(var(--glass-saturation, 1.8) * 0.9))',
          WebkitBackdropFilter: 'blur(calc(var(--glass-blur, 12px) * 0.6)) saturate(calc(var(--glass-saturation, 1.8) * 0.9))',
          border: '1px solid var(--glass-border-light, rgba(255, 255, 255, 0.2))',
        },
      })
    },
  ],
}