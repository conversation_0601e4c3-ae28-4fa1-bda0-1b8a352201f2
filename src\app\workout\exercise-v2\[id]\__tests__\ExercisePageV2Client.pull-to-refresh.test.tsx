import { render, screen } from '@testing-library/react'
import { ExercisePageV2Client } from '../ExercisePageV2Client'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { vi } from 'vitest'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    replace: vi.fn(),
  }),
  usePathname: () => '/workout/exercise-v2/1',
}))

// Mock components
vi.mock('@/components/workout-v2/CurrentSetCard', () => ({
  CurrentSetCard: ({ exercise }: any) => (
    <div data-testid="current-set-card">
      Current Set Card - {exercise?.Label || 'No Exercise'}
    </div>
  ),
}))

vi.mock('@/components/workout-v2/NextSetsPreview', () => ({
  NextSetsPreview: ({ nextSets }: any) => (
    <div data-testid="next-sets-preview">
      Next Sets Preview - {nextSets.length} sets
    </div>
  ),
}))

vi.mock('@/components/workout-v2/RestTimer', () => ({
  RestTimer: () => <div>Rest Timer</div>,
}))

vi.mock('@/components/workout-v2/ExercisePageStates', () => ({
  ExercisePageStates: () => <div>Exercise Page States</div>,
}))

vi.mock('@/components/PullToRefreshIndicator', () => ({
  PullToRefreshIndicator: ({ pullDistance, threshold, isRefreshing }: any) => (
    <div
      data-testid="pull-to-refresh-indicator"
      data-pull-distance={pullDistance}
      data-threshold={threshold}
      data-is-refreshing={isRefreshing}
    >
      Pull to Refresh Indicator
    </div>
  ),
}))

// Mock hooks
vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: vi.fn(() => ({
    isLoadingWorkout: false,
    workoutError: null,
    workoutSession: { id: 1 },
  })),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    loadingStates: new Map(),
  }),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'kg' }),
  }),
}))

vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: vi.fn(() => ({
    isInitializing: false,
    loadingError: null,
    retryInitialization: vi.fn(),
  })),
}))

const mockRefetchRecommendation = vi.fn()
const mockSetScreenLogic = {
  currentExercise: { Id: 1, Label: 'Bench Press' },
  exercises: [{ Id: 1, Label: 'Bench Press' }],
  currentSetIndex: 0,
  isSaving: false,
  saveError: null,
  showComplete: false,
  showExerciseComplete: false,
  recommendation: {
    ExerciseId: 1,
    Reps: 10,
    Weight: { Kg: 50, Lb: 110 },
    Series: 3,
  },
  isLoading: false,
  error: null,
  isLastExercise: false,
  isLastSet: false,
  isWarmup: false,
  isFirstWorkSet: true,
  completedSets: [],
  setData: { reps: 10, weight: 50, duration: 0 },
  setSetData: vi.fn(),
  setSaveError: vi.fn(),
  handleSaveSet: vi.fn(),
  refetchRecommendation: mockRefetchRecommendation,
  showRIRPicker: false,
  handleRIRSelect: vi.fn(),
  handleRIRCancel: vi.fn(),
}

vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: () => mockSetScreenLogic,
}))

vi.mock('@/hooks/useExerciseV2Actions', () => ({
  useExerciseV2Actions: () => ({
    handleSkipSet: vi.fn(),
  }),
}))

const mockPullToRefresh = {
  pullDistance: 0,
  isRefreshing: false,
  isPulling: false,
}

vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: vi.fn(() => mockPullToRefresh),
}))

describe('ExercisePageV2Client - Pull to Refresh', () => {
  let queryClient: QueryClient

  beforeEach(async () => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })
    vi.clearAllMocks()
    // Reset mock implementations
    const { useWorkout } = await import('@/hooks/useWorkout')
    vi.mocked(useWorkout).mockReturnValue({
      isLoadingWorkout: false,
      workoutError: null,
      workoutSession: { id: 1 },
    })
    const { useExercisePageInitialization } = await import(
      '@/hooks/useExercisePageInitialization'
    )
    vi.mocked(useExercisePageInitialization).mockReturnValue({
      isInitializing: false,
      loadingError: null,
      retryInitialization: vi.fn(),
    })
    mockSetScreenLogic.isSaving = false
  })

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      </QueryClientProvider>
    )
  }

  it('should render pull to refresh indicator', () => {
    renderComponent()

    expect(screen.getByTestId('pull-to-refresh-indicator')).toBeInTheDocument()
  })

  it('should call refetchRecommendation when pull to refresh is triggered', async () => {
    const { usePullToRefresh } = await import('@/hooks/usePullToRefresh')
    const mockUsePullToRefresh = vi.mocked(usePullToRefresh)

    // Capture the onRefresh callback
    let onRefreshCallback: () => Promise<void> = async () => {}
    mockUsePullToRefresh.mockImplementation((options: any) => {
      onRefreshCallback = options.onRefresh
      return mockPullToRefresh
    })

    renderComponent()

    // Trigger the refresh
    await onRefreshCallback()

    expect(mockRefetchRecommendation).toHaveBeenCalled()
  })

  it('should disable pull to refresh during initialization', async () => {
    const { usePullToRefresh } = await import('@/hooks/usePullToRefresh')
    const mockUsePullToRefresh = vi.mocked(usePullToRefresh)

    // Mock initialization state
    const { useExercisePageInitialization } = await import(
      '@/hooks/useExercisePageInitialization'
    )
    vi.mocked(useExercisePageInitialization).mockReturnValue({
      isInitializing: true,
      loadingError: null,
      retryInitialization: vi.fn(),
    })

    let capturedOptions: any = {}
    mockUsePullToRefresh.mockImplementation((options: any) => {
      capturedOptions = options
      return mockPullToRefresh
    })

    renderComponent()

    expect(capturedOptions.enabled).toBe(false)
  })

  it('should disable pull to refresh when loading workout', async () => {
    const { usePullToRefresh } = await import('@/hooks/usePullToRefresh')
    const mockUsePullToRefresh = vi.mocked(usePullToRefresh)

    // Mock loading state
    const { useWorkout } = await import('@/hooks/useWorkout')
    vi.mocked(useWorkout).mockReturnValue({
      isLoadingWorkout: true,
      workoutError: null,
      workoutSession: { id: 1 },
    })

    let capturedOptions: any = {}
    mockUsePullToRefresh.mockImplementation((options: any) => {
      capturedOptions = options
      return mockPullToRefresh
    })

    renderComponent()

    expect(capturedOptions.enabled).toBe(false)
  })

  it('should disable pull to refresh when saving', async () => {
    const { usePullToRefresh } = await import('@/hooks/usePullToRefresh')
    const mockUsePullToRefresh = vi.mocked(usePullToRefresh)

    // Mock saving state
    mockSetScreenLogic.isSaving = true

    let capturedOptions: any = {}
    mockUsePullToRefresh.mockImplementation((options: any) => {
      capturedOptions = options
      return mockPullToRefresh
    })

    renderComponent()

    expect(capturedOptions.enabled).toBe(false)
  })

  it('should pass correct pull to refresh configuration', async () => {
    const { usePullToRefresh } = await import('@/hooks/usePullToRefresh')
    const mockUsePullToRefresh = vi.mocked(usePullToRefresh)

    let capturedOptions: any = {}
    mockUsePullToRefresh.mockImplementation((options: any) => {
      capturedOptions = options
      return mockPullToRefresh
    })

    renderComponent()

    expect(capturedOptions.threshold).toBe(120)
    expect(capturedOptions.deadZone).toBe(20)
    expect(capturedOptions.resistance).toBe(3.5)
  })

  it('should show refreshing state when pull to refresh is active', () => {
    mockPullToRefresh.isRefreshing = true
    mockPullToRefresh.pullDistance = 120

    renderComponent()

    const indicator = screen.getByTestId('pull-to-refresh-indicator')
    expect(indicator).toHaveAttribute('data-is-refreshing', 'true')
    expect(indicator).toHaveAttribute('data-pull-distance', '120')
  })
})
