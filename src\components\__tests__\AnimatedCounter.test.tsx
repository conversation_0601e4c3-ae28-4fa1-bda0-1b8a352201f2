import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, act } from '@testing-library/react'
import { AnimatedCounter } from '../AnimatedCounter'

// Setup for tests
beforeEach(() => {
  vi.useFakeTimers()
})

afterEach(() => {
  vi.useRealTimers()
})

describe('AnimatedCounter', () => {
  it('renders with initial value of 0', () => {
    render(<AnimatedCounter targetValue={100} />)
    expect(screen.getByTestId('animated-counter-value')).toHaveTextContent('0')
  })

  it('renders without icon by default', () => {
    const { container } = render(<AnimatedCounter targetValue={100} />)
    const iconElement = container.querySelector('.w-6.h-6')
    expect(iconElement).not.toBeInTheDocument()
  })

  it('renders with icon when provided', () => {
    function TestIcon() {
      return <svg data-testid="test-icon" />
    }
    render(
      <AnimatedCounter targetValue={100} icon={<TestIcon />} label="Test" />
    )
    expect(screen.getByTestId('test-icon')).toBeInTheDocument()
  })

  it('scales icon according to size prop', () => {
    function TestIcon() {
      return <svg data-testid="test-icon" />
    }
    const { container: smallContainer } = render(
      <AnimatedCounter
        targetValue={100}
        icon={<TestIcon />}
        size="small"
        label="Small"
      />
    )
    const { container: mediumContainer } = render(
      <AnimatedCounter
        targetValue={100}
        icon={<TestIcon />}
        size="medium"
        label="Medium"
      />
    )
    const { container: largeContainer } = render(
      <AnimatedCounter
        targetValue={100}
        icon={<TestIcon />}
        size="large"
        label="Large"
      />
    )

    expect(smallContainer.querySelector('.w-4.h-4')).toBeInTheDocument()
    expect(mediumContainer.querySelector('.w-5.h-5')).toBeInTheDocument()
    expect(largeContainer.querySelector('.w-6.h-6')).toBeInTheDocument()
  })

  it('displays the label when provided', () => {
    render(<AnimatedCounter targetValue={100} label="Workouts Completed" />)
    expect(screen.getByText('Workouts Completed')).toBeInTheDocument()
  })

  it('animates from 0 to target value', async () => {
    render(<AnimatedCounter targetValue={100} />)

    // Should start at 0
    expect(screen.getByTestId('animated-counter-value')).toHaveTextContent('0')

    // Fast-forward animation (default duration is now 400ms)
    act(() => {
      vi.advanceTimersByTime(500)
    })

    // Should reach target value
    expect(screen.getByTestId('animated-counter-value')).toHaveTextContent(
      '100'
    )
  })

  it('handles large numbers with formatting', () => {
    render(<AnimatedCounter targetValue={1234} />)

    act(() => {
      vi.advanceTimersByTime(500)
    })

    expect(screen.getByTestId('animated-counter-value')).toHaveTextContent(
      '1,234'
    )
  })

  it('updates when target value changes', () => {
    const { rerender } = render(<AnimatedCounter targetValue={50} />)

    act(() => {
      vi.advanceTimersByTime(500)
    })
    expect(screen.getByTestId('animated-counter-value')).toHaveTextContent('50')

    // Change target value
    rerender(<AnimatedCounter targetValue={150} />)

    act(() => {
      vi.advanceTimersByTime(500)
    })
    expect(screen.getByTestId('animated-counter-value')).toHaveTextContent(
      '150'
    )
  })

  it('respects prefers-reduced-motion', () => {
    // Mock matchMedia
    window.matchMedia = vi.fn().mockImplementation((query) => ({
      matches: query === '(prefers-reduced-motion: reduce)',
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }))

    render(<AnimatedCounter targetValue={100} />)

    // Should immediately show target value without animation
    expect(screen.getByTestId('animated-counter-value')).toHaveTextContent(
      '100'
    )
  })

  it('applies custom className', () => {
    render(<AnimatedCounter targetValue={100} className="custom-class" />)
    expect(screen.getByTestId('animated-counter')).toHaveClass('custom-class')
  })

  it('applies different size variants', () => {
    const { rerender } = render(
      <AnimatedCounter targetValue={100} size="small" />
    )
    expect(screen.getByTestId('animated-counter-value')).toHaveClass('text-2xl')

    rerender(<AnimatedCounter targetValue={100} size="medium" />)
    expect(screen.getByTestId('animated-counter-value')).toHaveClass('text-4xl')

    rerender(<AnimatedCounter targetValue={100} size="large" />)
    expect(screen.getByTestId('animated-counter-value')).toHaveClass('text-6xl')
  })

  it('handles loading state', () => {
    render(<AnimatedCounter targetValue={100} isLoading />)
    expect(screen.getByTestId('shimmer-overlay')).toBeInTheDocument()
    expect(screen.getByTestId('animated-counter-value')).toBeInTheDocument()
  })

  it('shows icon in loading state when icon and label provided', () => {
    function TestIcon() {
      return <svg data-testid="test-icon" />
    }
    render(
      <AnimatedCounter
        targetValue={100}
        icon={<TestIcon />}
        label="Test"
        isLoading
      />
    )

    // Should show actual icon during loading (new behavior)
    expect(screen.getByTestId('test-icon')).toBeInTheDocument()

    // Should show shimmer overlay
    expect(screen.getByTestId('shimmer-overlay')).toBeInTheDocument()
  })

  it('handles null or undefined target value', () => {
    const { rerender } = render(<AnimatedCounter targetValue={null} />)
    expect(screen.getByTestId('animated-counter-value')).toHaveTextContent('0')

    rerender(<AnimatedCounter targetValue={undefined} />)
    expect(screen.getByTestId('animated-counter-value')).toHaveTextContent('0')
  })

  describe('Shimmer Effect', () => {
    it('shows shimmer overlay when showShimmer is true', () => {
      render(<AnimatedCounter targetValue={0} showShimmer />)

      expect(screen.getByTestId('shimmer-overlay')).toBeInTheDocument()
    })

    it('does not show shimmer when showShimmer is false', () => {
      render(<AnimatedCounter targetValue={100} showShimmer={false} />)

      expect(screen.queryByTestId('shimmer-overlay')).not.toBeInTheDocument()
    })

    it('applies shimmer offset delay', () => {
      render(
        <AnimatedCounter targetValue={0} showShimmer shimmerOffset={200} />
      )

      const shimmer = screen.getByTestId('shimmer-overlay')
      expect(shimmer).toBeInTheDocument()
    })

    it('hides shimmer when transitioning to actual value', () => {
      const { rerender } = render(
        <AnimatedCounter targetValue={0} showShimmer />
      )

      expect(screen.getByTestId('shimmer-overlay')).toBeInTheDocument()

      // Update with actual value
      act(() => {
        rerender(<AnimatedCounter targetValue={100} showShimmer />)
      })

      // Shimmer should be hidden immediately when transitioning
      expect(screen.queryByTestId('shimmer-overlay')).not.toBeInTheDocument()
    })

    it('adds glow effect during transition', () => {
      const { rerender } = render(
        <AnimatedCounter targetValue={0} showShimmer />
      )

      // Update with actual value to trigger transition
      act(() => {
        rerender(<AnimatedCounter targetValue={100} showShimmer />)
      })

      // Advance timers to trigger glow (100ms delay + some buffer)
      act(() => {
        vi.advanceTimersByTime(150)
      })

      const counter = screen.getByTestId('animated-counter-value')
      expect(counter.className).toContain('animate-pulse-glow')
    })
  })

  describe('Haptic Feedback', () => {
    it.skip('triggers vibration on animation complete', async () => {
      const mockVibrate = vi.fn()

      // Mock navigator.vibrate before rendering
      const originalVibrate = navigator.vibrate
      navigator.vibrate = mockVibrate

      render(<AnimatedCounter targetValue={100} duration={100} />)

      // Complete animation
      act(() => {
        vi.runAllTimers()
      })

      expect(mockVibrate).toHaveBeenCalledWith(10)

      // Restore original
      navigator.vibrate = originalVibrate
    })

    it('does not vibrate when target is 0', async () => {
      const mockVibrate = vi.fn()

      // Mock navigator.vibrate before rendering
      const originalVibrate = navigator.vibrate
      navigator.vibrate = mockVibrate

      render(<AnimatedCounter targetValue={0} />)

      act(() => {
        vi.runAllTimers()
      })

      expect(mockVibrate).not.toHaveBeenCalled()

      // Restore original
      navigator.vibrate = originalVibrate
    })
  })
})
