import { test, expect } from '@playwright/test'

test.describe('Exercise V2 - Label Styling', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication before navigating
    await page.addInitScript(() => {
      const mockUser = {
        Email: '<EMAIL>',
        MassUnit: 'lbs',
      }
      const mockWorkout = {
        id: 1,
        exercises: [{ Id: 1, Label: 'Bench Press' }],
      }
      localStorage.setItem(
        'auth-store',
        JSON.stringify({
          state: {
            user: mockUser,
            isAuthenticated: true,
            token: 'mock-token',
          },
        })
      )
      localStorage.setItem(
        'workout-store',
        JSON.stringify({
          state: {
            workoutSession: mockWorkout,
            exercises: mockWorkout.exercises,
            recommendations: {
              1: { Reps: 10, Weight: { Lb: 100, Kg: 45 } },
            },
          },
        })
      )
    })
  })

  test('should display lowercase labels with italic styling matching explainer text', async ({
    page,
  }) => {
    // Navigate to exercise v2 page
    await page.goto('/workout/exercise-v2/1')

    // Wait for the page to load
    await page.waitForSelector('[data-testid="current-set-card"]')

    // Check reps label
    const repsLabel = page.locator('text=reps').first()
    await expect(repsLabel).toBeVisible()
    await expect(repsLabel).toHaveClass(/italic/)
    await expect(repsLabel).toHaveClass(/text-text-secondary/)
    await expect(repsLabel).toHaveClass(/text-sm/)

    // Check weight unit label (assuming lbs)
    const unitLabel = page.locator('text=lbs').first()
    await expect(unitLabel).toBeVisible()
    await expect(unitLabel).toHaveClass(/italic/)
    await expect(unitLabel).toHaveClass(/text-text-secondary/)
    await expect(unitLabel).toHaveClass(/text-sm/)

    // Ensure uppercase labels are not present
    await expect(page.locator('text=REPS')).not.toBeVisible()
    await expect(page.locator('text=LBS')).not.toBeVisible()

    // Take a screenshot for visual verification
    await page.screenshot({
      path: 'exercise-v2-labels.png',
      fullPage: true,
    })
  })

  test('should handle uppercase unit input and display as lowercase', async ({
    page,
  }) => {
    // Mock with uppercase unit
    await page.addInitScript(() => {
      const mockUser = {
        Email: '<EMAIL>',
        MassUnit: 'KG', // Uppercase
      }
      const mockWorkout = {
        id: 1,
        exercises: [{ Id: 1, Label: 'Bench Press' }],
      }
      localStorage.setItem(
        'auth-store',
        JSON.stringify({
          state: {
            user: mockUser,
            isAuthenticated: true,
            token: 'mock-token',
          },
        })
      )
      localStorage.setItem(
        'workout-store',
        JSON.stringify({
          state: {
            workoutSession: mockWorkout,
            exercises: mockWorkout.exercises,
            recommendations: {
              1: { Reps: 10, Weight: { Lb: 100, Kg: 45 } },
            },
          },
        })
      )
    })

    // Navigate to exercise v2 page
    await page.goto('/workout/exercise-v2/1')

    // Wait for the page to load
    await page.waitForSelector('[data-testid="current-set-card"]')

    // Check that unit is displayed as lowercase
    const unitLabel = page.locator('text=kg').first()
    await expect(unitLabel).toBeVisible()
    await expect(unitLabel).toHaveClass(/italic/)
    await expect(unitLabel).toHaveClass(/text-text-secondary/)

    // Ensure uppercase is not displayed
    await expect(page.locator('text=KG')).not.toBeVisible()
  })
})
