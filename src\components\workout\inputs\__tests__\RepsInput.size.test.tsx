import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { RepsInput } from '../RepsInput'

const defaultProps = {
  reps: 10,
  onChange: vi.fn(),
  onIncrement: vi.fn(),
  onDecrement: vi.fn(),
}

describe('RepsInput - Button and Icon Sizes', () => {
  it('should have larger SVG icons (w-12 h-12) for better mobile tap targets', () => {
    render(<RepsInput {...defaultProps} />)

    // Get both increment and decrement buttons
    const incrementButton = screen.getByLabelText('Increase reps')
    const decrementButton = screen.getByLabelText('Decrease reps')

    // Check SVG sizes inside buttons
    const incrementSvg = incrementButton.querySelector('svg')
    const decrementSvg = decrementButton.querySelector('svg')

    expect(incrementSvg).toHaveClass('w-12')
    expect(incrementSvg).toHaveClass('h-12')
    expect(decrementSvg).toHaveClass('w-12')
    expect(decrementSvg).toHaveClass('h-12')
  })

  it('should have larger button padding (p-3) for 50% bigger tap targets', () => {
    render(<RepsInput {...defaultProps} />)

    const incrementButton = screen.getByLabelText('Increase reps')
    const decrementButton = screen.getByLabelText('Decrease reps')

    // Check button padding classes
    expect(incrementButton).toHaveClass('p-3')
    expect(decrementButton).toHaveClass('p-3')
  })

  it('should maintain rounded-full shape for buttons', () => {
    render(<RepsInput {...defaultProps} />)

    const incrementButton = screen.getByLabelText('Increase reps')
    const decrementButton = screen.getByLabelText('Decrease reps')

    expect(incrementButton).toHaveClass('rounded-full')
    expect(decrementButton).toHaveClass('rounded-full')
  })

  it('should calculate to approximately 60px total tap target', () => {
    render(<RepsInput {...defaultProps} />)

    const incrementButton = screen.getByLabelText('Increase reps')

    // w-12 = 48px (icon) + p-3 = 12px padding each side = 72px total
    // This provides a tap target 50% larger than the original 48px
    expect(incrementButton).toHaveClass('p-3')
    const svg = incrementButton.querySelector('svg')
    expect(svg).toHaveClass('w-12', 'h-12')
  })
})
