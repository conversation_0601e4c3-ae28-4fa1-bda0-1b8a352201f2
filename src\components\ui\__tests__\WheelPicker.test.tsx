import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { WheelPicker } from '../WheelPicker'

describe('WheelPicker', () => {
  describe('Display', () => {
    it('should display current value in center with large font', () => {
      render(
        <WheelPicker
          value={20}
          onChange={vi.fn()}
          min={0}
          max={50}
          increment={1}
        />
      )

      const centerValue = screen.getByTestId('wheel-picker-center')
      expect(centerValue).toBeInTheDocument()
      // The actual value is displayed in the buttons, not the center indicator
      const valueButton = screen.getByTestId('wheel-value-20')
      expect(valueButton).toHaveClass('text-6xl', { exact: false })
    })

    it('should display smaller values on the left', () => {
      render(
        <WheelPicker
          value={20}
          onChange={vi.fn()}
          min={0}
          max={50}
          increment={1}
        />
      )

      expect(screen.getByText('19')).toBeInTheDocument()
      expect(screen.getByText('18')).toBeInTheDocument()
      expect(screen.getByText('17')).toBeInTheDocument()
    })

    it('should display larger values on the right', () => {
      render(
        <WheelPicker
          value={20}
          onChange={vi.fn()}
          min={0}
          max={50}
          increment={1}
        />
      )

      expect(screen.getByText('21')).toBeInTheDocument()
      expect(screen.getByText('22')).toBeInTheDocument()
      expect(screen.getByText('23')).toBeInTheDocument()
    })
  })

  describe('Weight Increments', () => {
    it('should handle 2.5 increments for weight', () => {
      render(
        <WheelPicker
          value={100}
          onChange={vi.fn()}
          min={0}
          max={200}
          increment={2.5}
        />
      )

      // Check left side decrements
      expect(screen.getByText('97.5')).toBeInTheDocument()
      expect(screen.getByText('95')).toBeInTheDocument()
      expect(screen.getByText('92.5')).toBeInTheDocument()

      // Check right side increments
      expect(screen.getByText('102.5')).toBeInTheDocument()
      expect(screen.getByText('105')).toBeInTheDocument()
      expect(screen.getByText('107.5')).toBeInTheDocument()
    })
  })

  describe('Interactions', () => {
    it('should call onChange when clicking a value', () => {
      const onChange = vi.fn()
      render(
        <WheelPicker
          value={20}
          onChange={onChange}
          min={0}
          max={50}
          increment={1}
        />
      )

      fireEvent.click(screen.getByText('22'))
      expect(onChange).toHaveBeenCalledWith(22)
    })

    it('should not go below minimum value', () => {
      const onChange = vi.fn()
      render(
        <WheelPicker
          value={0}
          onChange={onChange}
          min={0}
          max={50}
          increment={1}
        />
      )

      // Should not show negative values
      expect(screen.queryByText('-1')).not.toBeInTheDocument()
    })

    it('should not go above maximum value', () => {
      const onChange = vi.fn()
      render(
        <WheelPicker
          value={50}
          onChange={onChange}
          min={0}
          max={50}
          increment={1}
        />
      )

      // Should not show values above max
      expect(screen.queryByText('51')).not.toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(
        <WheelPicker
          value={20}
          onChange={vi.fn()}
          min={0}
          max={50}
          increment={1}
          label="Reps"
        />
      )

      const container = screen.getByRole('spinbutton')
      expect(container).toHaveAttribute('aria-label', 'Reps')
      expect(container).toHaveAttribute('aria-valuenow', '20')
      expect(container).toHaveAttribute('aria-valuemin', '0')
      expect(container).toHaveAttribute('aria-valuemax', '50')
    })

    it('should be keyboard navigable', () => {
      const onChange = vi.fn()
      render(
        <WheelPicker
          value={20}
          onChange={onChange}
          min={0}
          max={50}
          increment={1}
        />
      )

      const container = screen.getByRole('spinbutton')

      // Arrow right should increase
      fireEvent.keyDown(container, { key: 'ArrowRight' })
      expect(onChange).toHaveBeenCalledWith(21)

      // Arrow left should decrease
      fireEvent.keyDown(container, { key: 'ArrowLeft' })
      expect(onChange).toHaveBeenCalledWith(19)
    })

    it('should prevent browser scroll interference with touch-action CSS', () => {
      render(
        <WheelPicker
          value={20}
          onChange={vi.fn()}
          min={0}
          max={50}
          increment={1}
        />
      )

      const container = screen.getByTestId('wheel-picker-container')

      // Test will fail initially - touch-action should be 'pan-x' to prevent vertical scrolling
      expect(container).toHaveStyle('touch-action: pan-x')
    })
  })
})
