import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from './helpers/authHelper'

test.describe('Exercise V2 Pull to Refresh', () => {
  test.beforeEach(async ({ page }) => {
    await setupAuthenticatedUser(page)
  })

  test('should refresh exercise data when pulling down', async ({ page }) => {
    // Navigate to workout overview
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Click on first exercise to go to exercise V2 page
    const firstExercise = await page
      .locator('[data-testid="exercise-card"]')
      .first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForSelector('[data-testid="exercise-page-container"]')
    await page.waitForTimeout(500) // Wait for any animations

    // Intercept the recommendation API call
    let refreshCalled = false
    await page.route('**/api/exercise/*/recommendation', (route) => {
      refreshCalled = true
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          ExerciseId: 1,
          Reps: 12, // Different from initial load
          Weight: { Kg: 55, Lb: 121 },
          Series: 3,
        }),
      })
    })

    // Simulate pull-to-refresh gesture
    const container = await page.locator(
      '[data-testid="exercise-page-container"]'
    )
    const box = await container.boundingBox()
    if (!box) throw new Error('Container not found')

    // Start touch at top of container
    await page.mouse.move(box.x + box.width / 2, box.y + 10)
    await page.mouse.down()

    // Pull down beyond threshold (120px)
    await page.mouse.move(box.x + box.width / 2, box.y + 150, { steps: 10 })

    // Release to trigger refresh
    await page.mouse.up()

    // Verify refresh was triggered
    await expect(async () => {
      expect(refreshCalled).toBe(true)
    }).toPass({ timeout: 3000 })
  })

  test('should show pull-to-refresh indicator when pulling down', async ({
    page,
  }) => {
    // Navigate to workout overview
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Click on first exercise
    const firstExercise = await page
      .locator('[data-testid="exercise-card"]')
      .first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForSelector('[data-testid="exercise-page-container"]')
    await page.waitForTimeout(500)

    // Verify indicator is not visible initially
    await expect(
      page.locator('[data-testid="pull-to-refresh-indicator"]')
    ).not.toBeVisible()

    // Simulate pull gesture
    const container = await page.locator(
      '[data-testid="exercise-page-container"]'
    )
    const box = await container.boundingBox()
    if (!box) throw new Error('Container not found')

    // Start touch
    await page.mouse.move(box.x + box.width / 2, box.y + 10)
    await page.mouse.down()

    // Pull down slightly (but not past threshold)
    await page.mouse.move(box.x + box.width / 2, box.y + 50, { steps: 5 })

    // Indicator should be visible while pulling
    await expect(
      page.locator('[data-testid="pull-to-refresh-indicator"]')
    ).toBeVisible()

    // Release
    await page.mouse.up()

    // Indicator should disappear after release
    await expect(
      page.locator('[data-testid="pull-to-refresh-indicator"]')
    ).not.toBeVisible()
  })

  test('should not trigger refresh when pull distance is below threshold', async ({
    page,
  }) => {
    // Navigate to workout overview
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Click on first exercise
    const firstExercise = await page
      .locator('[data-testid="exercise-card"]')
      .first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Intercept API call
    let refreshCalled = false
    await page.route('**/api/exercise/*/recommendation', (route) => {
      refreshCalled = true
      route.continue()
    })

    // Simulate small pull gesture (below 120px threshold)
    const container = await page.locator(
      '[data-testid="exercise-page-container"]'
    )
    const box = await container.boundingBox()
    if (!box) throw new Error('Container not found')

    await page.mouse.move(box.x + box.width / 2, box.y + 10)
    await page.mouse.down()
    await page.mouse.move(box.x + box.width / 2, box.y + 50, { steps: 5 })
    await page.mouse.up()

    // Wait a bit to ensure no refresh happens
    await page.waitForTimeout(1000)

    // Verify refresh was NOT triggered
    expect(refreshCalled).toBe(false)
  })
})
