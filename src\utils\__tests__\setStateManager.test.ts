import { describe, it, expect } from 'vitest'
import {
  updateNextSetStates,
  completeSet,
  uncompleteSet,
  areAllWorkSetsCompleted,
  getSetStatistics,
} from '../setStateManager'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'

// Mock set data
const createMockSet = (
  overrides: Partial<WorkoutLogSerieModelRef> = {}
): WorkoutLogSerieModelRef => ({
  Id: 1,
  ExerciseId: 1,
  ExerciseName: 'Test Exercise',
  Weight: { Kg: 60, Lb: 132 },
  Reps: 10,
  SetNo: '1',
  SetTitle: '',
  LastTimeSet: '',
  IsNext: false,
  IsFinished: false,
  IsActive: false,
  IsEditing: false,
  IsLastSet: false,
  IsFirstSide: false,
  IsHeaderCell: false,
  IsFirstSetFinished: false,
  IsFirstWorkSet: false,
  IsLastWarmupSet: false,
  IsExerciseFinished: false,
  IsJustSetup: false,
  ShouldUpdateIncrement: false,
  IsBackOffSet: false,
  IsNextBackOffSet: false,
  IsDropSet: false,
  IsNormalset: true,
  IsMaxChallenge: false,
  IsAssisted: false,
  IsFlexibility: false,
  IsTimeBased: false,
  IsUnilateral: false,
  IsBodyweight: false,
  IsTimerOff: false,
  IsSizeChanged: false,
  IsWarmups: false,
  ShowWorkTimer: false,
  NbPause: 0,
  OneRMProgress: 0,
  PreviousReps: 0,
  PreviousWeight: { Kg: 0, Lb: 0 },
  Speed: 0,
  Increments: { Kg: 1, Lb: 2.5 },
  Min: { Kg: 0, Lb: 0 },
  Max: { Kg: 200, Lb: 440 },
  HeaderImage: '',
  HeaderTitle: '',
  VideoUrl: '',
  ShowPlusTooltip: false,
  ShowSuperSet3: false,
  ShowSuperSet2: false,
  BodypartId: 1,
  EquipmentId: 1,
  ...overrides,
})

describe('setStateManager', () => {
  describe('updateNextSetStates', () => {
    it('should mark first unfinished set as next', () => {
      const sets = [
        createMockSet({ IsFinished: true }),
        createMockSet({ IsFinished: false }),
        createMockSet({ IsFinished: false }),
      ]

      const result = updateNextSetStates(sets)

      expect(result[0].IsNext).toBe(false)
      expect(result[1].IsNext).toBe(true)
      expect(result[2].IsNext).toBe(false)
    })

    it('should clear all IsNext flags when all sets are finished', () => {
      const sets = [
        createMockSet({ IsFinished: true }),
        createMockSet({ IsFinished: true }),
        createMockSet({ IsFinished: true }),
      ]

      const result = updateNextSetStates(sets)

      expect(result.every((set) => !set.IsNext)).toBe(true)
    })
  })

  describe('completeSet', () => {
    it('should mark set as finished and update next states', () => {
      const sets = [
        createMockSet({ IsNext: true }),
        createMockSet({ IsFinished: false }),
        createMockSet({ IsFinished: false }),
      ]

      const result = completeSet(sets, 0)

      expect(result[0].IsFinished).toBe(true)
      expect(result[0].IsNext).toBe(false)
      expect(result[1].IsNext).toBe(true)
    })

    it('should handle invalid set index', () => {
      const sets = [createMockSet()]
      const result = completeSet(sets, 5)

      expect(result).toEqual(sets)
    })
  })

  describe('uncompleteSet', () => {
    it('should mark set as unfinished and update next states', () => {
      const sets = [
        createMockSet({ IsFinished: true }),
        createMockSet({ IsNext: true }),
        createMockSet({ IsFinished: false }),
      ]

      const result = uncompleteSet(sets, 0)

      expect(result[0].IsFinished).toBe(false)
      expect(result[0].IsNext).toBe(true)
      expect(result[1].IsNext).toBe(false)
    })
  })

  describe('areAllWorkSetsCompleted', () => {
    it('should return true when all work sets are completed', () => {
      const sets = [
        createMockSet({ IsWarmups: true, IsFinished: false }),
        createMockSet({ IsWarmups: false, IsFinished: true }),
        createMockSet({ IsWarmups: false, IsFinished: true }),
      ]

      expect(areAllWorkSetsCompleted(sets)).toBe(true)
    })

    it('should return false when some work sets are incomplete', () => {
      const sets = [
        createMockSet({ IsWarmups: true, IsFinished: false }),
        createMockSet({ IsWarmups: false, IsFinished: true }),
        createMockSet({ IsWarmups: false, IsFinished: false }),
      ]

      expect(areAllWorkSetsCompleted(sets)).toBe(false)
    })
  })

  describe('canFinishExercise', () => {
    it('should return true when all work sets are completed', () => {
      const sets = [
        createMockSet({ IsWarmups: true, IsFinished: false }),
        createMockSet({ IsWarmups: false, IsFinished: true }),
        createMockSet({ IsWarmups: false, IsFinished: true }),
      ]

      expect(canFinishExercise(sets)).toBe(true)
    })
  })

  describe('addSet', () => {
    it('should add new set based on last work set', () => {
      const sets = [
        createMockSet({ IsWarmups: true, SetNo: 'W' }),
        createMockSet({ IsWarmups: false, SetNo: '1', IsLastSet: true }),
      ]

      const result = addSet(sets, {})

      expect(result).toHaveLength(3)
      expect(result[2].SetNo).toBe('2')
      expect(result[2].IsLastSet).toBe(true)
      expect(result[1].IsLastSet).toBe(false)
    })
  })

  describe('removeSet', () => {
    it('should remove set and update set numbers', () => {
      const sets = [
        createMockSet({ IsWarmups: false, SetNo: '1' }),
        createMockSet({ IsWarmups: false, SetNo: '2' }),
        createMockSet({ IsWarmups: false, SetNo: '3', IsLastSet: true }),
      ]

      const result = removeSet(sets, 1)

      expect(result).toHaveLength(2)
      expect(result[0].SetNo).toBe('1')
      expect(result[1].SetNo).toBe('2')
      expect(result[1].IsLastSet).toBe(true)
    })
  })

  describe('getSetStatistics', () => {
    it('should return correct statistics', () => {
      const sets = [
        createMockSet({ IsWarmups: true, IsFinished: true }),
        createMockSet({ IsWarmups: false, IsFinished: true }),
        createMockSet({ IsWarmups: false, IsFinished: false }),
        createMockSet({ IsWarmups: false, IsFinished: false }),
      ]

      const stats = getSetStatistics(sets)

      expect(stats.totalSets).toBe(4)
      expect(stats.warmupSets).toBe(1)
      expect(stats.workSets).toBe(3)
      expect(stats.completedSets).toBe(2)
      expect(stats.completedWorkSets).toBe(1)
      expect(stats.completionPercentage).toBe(50)
      expect(stats.workSetsCompletionPercentage).toBe(33)
      expect(stats.canFinish).toBe(false)
      expect(stats.allCompleted).toBe(false)
      expect(stats.allWorkSetsCompleted).toBe(false)
    })
  })
})
