import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ExercisePageV2Client } from '../ExercisePageV2Client'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
}))

// Mock dependencies
vi.mock('@/hooks/useSetScreenLogic')

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    isLoadingWorkout: false,
    workoutError: null,
    workoutSession: { id: '123' },
  }),
}))

vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: () => ({
    isInitializing: false,
    loadingError: null,
    retryInitialization: vi.fn(),
  }),
}))

vi.mock('@/hooks/useExerciseV2Actions', () => ({
  useExerciseV2Actions: () => ({
    handleCompleteSet: vi.fn(),
    handleSkipSet: vi.fn(),
  }),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'lbs' }),
  }),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    loadingStates: new Map(),
  }),
}))

// Mock the new AllSetsViewV2 component
vi.mock('@/components/workout-v2/AllSetsViewV2', () => ({
  AllSetsViewV2: () => <div data-testid="all-sets-view">All Sets View</div>,
}))

// Mock existing components
vi.mock('@/components/workout-v2/CurrentSetCard', () => ({
  CurrentSetCard: ({ exercise }: any) => (
    <div data-testid="current-set-card">
      Current Set Card for {exercise?.Label}
    </div>
  ),
}))

vi.mock('@/components/workout-v2/RestTimer', () => ({
  RestTimer: () => <div>Rest Timer</div>,
}))

describe('ExercisePageV2Client - View Toggle', () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      </QueryClientProvider>
    )
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Reset to default mock implementation
    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: {
        Id: 1,
        Label: 'Bench Press',
        IsFinished: false,
      },
      exercises: [{ Id: 1, Label: 'Bench Press' }],
      currentSetIndex: 0,
      isSaving: false,
      saveError: null,
      showComplete: false,
      showExerciseComplete: false,
      recommendation: {
        ExerciseId: 1,
        Reps: 10,
        Weight: { Lb: 135, Kg: 61 },
        Series: 3,
        WarmupsCount: 2,
      },
      isLoading: false,
      error: null,
      isLastExercise: false,
      isLastSet: false,
      isWarmup: true,
      isFirstWorkSet: false,
      completedSets: [],
      setData: { reps: 5, weight: 95, duration: 0 },
      setSetData: vi.fn(),
      setSaveError: vi.fn(),
      handleSaveSet: vi.fn(),
      refetchRecommendation: vi.fn(),
    } as any)
  })

  it('should default to current set view', () => {
    renderComponent()

    // Should show CurrentSetCard by default
    expect(screen.getByTestId('current-set-card')).toBeInTheDocument()
    expect(screen.queryByTestId('all-sets-view')).not.toBeInTheDocument()
  })

  it('should show view toggle button', () => {
    renderComponent()

    // Should have a toggle button
    const toggleButton = screen.getByRole('button', { name: /view all sets/i })
    expect(toggleButton).toBeInTheDocument()
  })

  it('should toggle to all sets view when button clicked', async () => {
    const user = userEvent.setup()
    renderComponent()

    // Click toggle button
    const toggleButton = screen.getByRole('button', { name: /view all sets/i })
    await user.click(toggleButton)

    // Should now show AllSetsViewV2
    await waitFor(() => {
      expect(screen.getByTestId('all-sets-view')).toBeInTheDocument()
      expect(screen.queryByTestId('current-set-card')).not.toBeInTheDocument()
    })

    // Button text should change
    expect(
      screen.getByRole('button', { name: /minimal view/i })
    ).toBeInTheDocument()
  })

  it('should toggle through all view modes', async () => {
    const user = userEvent.setup()
    renderComponent()

    // Toggle to all sets view
    await user.click(screen.getByRole('button', { name: /view all sets/i }))

    // Toggle to minimal view
    await user.click(screen.getByRole('button', { name: /minimal view/i }))

    // Should show CurrentSetCard without NextSetsPreview
    await waitFor(() => {
      expect(screen.getByTestId('current-set-card')).toBeInTheDocument()
      expect(screen.queryByTestId('all-sets-view')).not.toBeInTheDocument()
    })

    // Toggle back to hybrid view
    await user.click(screen.getByRole('button', { name: /hybrid view/i }))

    // Should show CurrentSetCard with NextSetsPreview again
    await waitFor(() => {
      expect(screen.getByTestId('current-set-card')).toBeInTheDocument()
      expect(screen.queryByTestId('all-sets-view')).not.toBeInTheDocument()
    })
  })

  it('should persist view mode when data updates', async () => {
    const user = userEvent.setup()
    const { rerender } = renderComponent()

    // Toggle to all sets view
    await user.click(screen.getByRole('button', { name: /view all sets/i }))
    expect(screen.getByTestId('all-sets-view')).toBeInTheDocument()

    // Rerender with new props (simulating data update)
    rerender(
      <QueryClientProvider client={queryClient}>
        <NavigationProvider>
          <ExercisePageV2Client exerciseId={1} />
        </NavigationProvider>
      </QueryClientProvider>
    )

    // Should still be in all sets view
    expect(screen.getByTestId('all-sets-view')).toBeInTheDocument()
  })

  it('should show toggle button in all views', async () => {
    const user = userEvent.setup()
    renderComponent()

    // Check in hybrid view (default)
    expect(
      screen.getByRole('button', { name: /view all sets/i })
    ).toBeInTheDocument()

    // Toggle to all sets view
    await user.click(screen.getByRole('button', { name: /view all sets/i }))

    // Check in all sets view
    expect(
      screen.getByRole('button', { name: /minimal view/i })
    ).toBeInTheDocument()

    // Toggle to minimal view
    await user.click(screen.getByRole('button', { name: /minimal view/i }))

    // Check in minimal view
    expect(
      screen.getByRole('button', { name: /hybrid view/i })
    ).toBeInTheDocument()
  })

  it('should maintain set data when toggling views', async () => {
    const user = userEvent.setup()
    renderComponent()

    // Get initial state
    const initialCard = screen.getByTestId('current-set-card')
    expect(initialCard).toBeInTheDocument()

    // Toggle through all views and back
    await user.click(screen.getByRole('button', { name: /view all sets/i }))
    await user.click(screen.getByRole('button', { name: /minimal view/i }))
    await user.click(screen.getByRole('button', { name: /hybrid view/i }))

    // Should maintain the same data
    const finalCard = screen.getByTestId('current-set-card')
    expect(finalCard).toBeInTheDocument()
    expect(finalCard.textContent).toBe(initialCard.textContent)
  })

  it('should disable toggle during save operation', async () => {
    // Mock saving state
    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: {
        Id: 1,
        Label: 'Bench Press',
        IsFinished: false,
      },
      exercises: [{ Id: 1, Label: 'Bench Press' }],
      currentSetIndex: 0,
      isSaving: true, // Set saving state to true
      saveError: null,
      showComplete: false,
      showExerciseComplete: false,
      recommendation: {
        ExerciseId: 1,
        Reps: 10,
        Weight: { Lb: 135, Kg: 61 },
        Series: 3,
        WarmupsCount: 2,
      },
      isLoading: false,
      error: null,
      isLastExercise: false,
      isLastSet: false,
      isWarmup: true,
      isFirstWorkSet: false,
      completedSets: [],
      setData: { reps: 5, weight: 95, duration: 0 },
      setSetData: vi.fn(),
      setSaveError: vi.fn(),
      handleSaveSet: vi.fn(),
      refetchRecommendation: vi.fn(),
    } as any)

    renderComponent()

    const toggleButton = screen.getByRole('button', { name: /view all sets/i })
    expect(toggleButton).toBeDisabled()
  })
})
