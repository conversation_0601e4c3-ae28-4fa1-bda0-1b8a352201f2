'use client'

import { useState, useEffect, useMemo } from 'react'
import { motion, useAnimation } from 'framer-motion'
import { useHaptic } from '@/utils/haptic'
import { useStatCounterAnimation } from '@/hooks/useStatCounterAnimation'
import type { UserStats } from '@/types'

interface SwipeableStatCardProps {
  stats: UserStats | null
  isLoading: boolean
}

interface StatConfig {
  value: number
  label: string
  icon: React.ReactNode
  formatter?: (value: number) => string
}

export function SwipeableStatCard({
  stats,
  isLoading,
}: SwipeableStatCardProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const controls = useAnimation()
  const haptic = useHaptic('light')

  // Estimated max values for loading animation - memoized to prevent re-renders
  const estimatedMaxValues = useMemo(() => [10, 50, 15000], []) // week streak, workouts, lbs lifted

  // Counter animation hook
  const { animatedValues, resetAnimation } = useStatCounterAnimation({
    isLoading,
    hasRealData: !!stats,
    estimatedMaxValues,
  })

  // Define stats configuration
  const statsConfig: StatConfig[] = [
    {
      value: stats?.weekStreak ?? animatedValues[0] ?? 0,
      label: 'Week Streak',
      icon: (
        <svg
          className="w-8 h-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"
          />
        </svg>
      ),
    },
    {
      value: stats?.workoutsCompleted ?? animatedValues[1] ?? 0,
      label: 'Workouts',
      icon: (
        <svg
          className="w-8 h-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
          />
        </svg>
      ),
    },
    {
      value: stats?.lbsLifted ?? animatedValues[2] ?? 0,
      label: 'Lbs Lifted',
      icon: (
        <svg
          className="w-8 h-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"
          />
        </svg>
      ),
      formatter: (value) => value.toLocaleString(),
    },
  ]

  const currentStat = statsConfig[currentIndex]

  // Reset animation when index changes during loading
  useEffect(() => {
    if (isLoading && !stats) {
      resetAnimation()
    }
  }, [currentIndex, isLoading, stats, resetAnimation])

  const handleNext = () => {
    haptic.trigger()
    setCurrentIndex((prev) => (prev + 1) % statsConfig.length)
  }

  const handlePrevious = () => {
    haptic.trigger()
    setCurrentIndex(
      (prev) => (prev - 1 + statsConfig.length) % statsConfig.length
    )
  }

  const handleDragEnd = (_: unknown, info: { offset: { x: number } }) => {
    const swipeThreshold = 50

    if (info.offset.x < -swipeThreshold) {
      // Swiped left - go to next
      handleNext()
    } else if (info.offset.x > swipeThreshold) {
      // Swiped right - go to previous
      handlePrevious()
    }

    // Reset position
    controls.start({ x: 0 })
  }

  const handleIndicatorClick = (index: number) => {
    haptic.trigger()
    setCurrentIndex(index)
  }

  // Don't show separate loading state - let the main render handle it
  // This allows the counter animation to display

  if (!currentStat) {
    return null
  }

  const displayValue = currentStat.formatter
    ? currentStat.formatter(currentStat.value)
    : currentStat.value.toString()

  return (
    <div className="relative">
      <motion.div
        data-testid="swipeable-stat-card"
        drag="x"
        dragConstraints={{ left: -150, right: 150 }}
        onDragEnd={handleDragEnd}
        animate={controls}
        className="bg-surface-secondary rounded-2xl p-6 shadow-lg cursor-grab active:cursor-grabbing"
      >
        <div className="flex flex-col items-center justify-center h-[200px]">
          <div className="text-brand-primary mb-4">{currentStat.icon}</div>
          <div
            className="text-4xl font-bold text-text-primary mb-2"
            data-testid="stat-value"
          >
            {displayValue}
          </div>
          <div className="text-lg text-text-secondary">{currentStat.label}</div>
        </div>

        {/* Swipe hint */}
        <div className="text-center mt-4">
          <p className="text-sm text-text-tertiary">Swipe to view more</p>
        </div>
      </motion.div>

      {/* Stat indicators */}
      <div className="flex justify-center gap-2 mt-4">
        {statsConfig.map((stat, index) => (
          <button
            key={stat.label}
            data-testid="stat-indicator"
            onClick={() => handleIndicatorClick(index)}
            className={`w-2 h-2 rounded-full transition-colors ${
              index === currentIndex ? 'bg-brand-primary' : 'bg-bg-tertiary'
            }`}
            aria-label={`Go to stat ${index + 1}`}
          />
        ))}
      </div>

      {/* Loading message */}
      {isLoading && !stats && (
        <div className="text-center mt-4">
          <p className="text-sm text-text-secondary animate-pulse">
            Loading your workout...
          </p>
        </div>
      )}
    </div>
  )
}
