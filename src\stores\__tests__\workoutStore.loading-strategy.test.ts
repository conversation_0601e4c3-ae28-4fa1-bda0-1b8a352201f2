import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { useWorkoutStore } from '../workoutStore'
import type {
  GetUserProgramInfoResponseModel,
  WorkoutTemplateModel,
  RecommendationModel,
  WorkoutTemplateGroupModel,
} from '@/types'

// Mock the workout API service
vi.mock('@/api/workout', () => ({
  workoutService: {
    getUserWorkoutProgramInfo: vi.fn(),
    getWorkoutDetails: vi.fn(),
    getExerciseRecommendation: vi.fn(),
  },
}))

// Mock logger
vi.mock('@/utils/logger', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
  },
}))

describe('WorkoutStore - Loading Strategy Actions', () => {
  beforeEach(() => {
    // Reset store to initial state
    useWorkoutStore.setState({
      currentWorkout: null,
      currentProgram: null,
      exercises: [],
      exerciseRecommendations: new Map(),
      loadingStates: new Map(),
      errors: new Map(),
      isLoading: false,
      error: '',
    })

    // Clear all mocks
    vi.clearAllMocks()

    // Mock localStorage
    const mockAuth = {
      state: {
        user: {
          email: '<EMAIL>',
        },
      },
    }
    vi.stubGlobal('localStorage', {
      getItem: vi.fn().mockReturnValue(JSON.stringify(mockAuth)),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    })
  })

  afterEach(() => {
    vi.unstubAllGlobals()
  })

  describe('loadWorkoutProgram', () => {
    it('should load workout program info and update state', async () => {
      const mockResponse: {
        GetUserProgramInfoResponseModel: GetUserProgramInfoResponseModel
      } = {
        GetUserProgramInfoResponseModel: {
          RecommendedProgram: {
            Id: 1,
            Label: 'Beginner Program',
            Days: 3,
            IsSystemExercise: true,
          },
          NextWorkoutTemplate: {
            Id: 123,
            Label: 'Push Day',
            IsSystemExercise: true,
            Exercises: null,
          },
        },
      }

      const { workoutService } = await import('@/api/workout')
      vi.mocked(workoutService.getUserWorkoutProgramInfo).mockResolvedValueOnce(
        mockResponse
      )

      const { loadWorkoutProgram } = useWorkoutStore.getState()
      await loadWorkoutProgram()

      const state = useWorkoutStore.getState()
      expect(state.currentWorkout).toEqual(
        mockResponse.GetUserProgramInfoResponseModel.NextWorkoutTemplate
      )
      expect(state.currentProgram).toEqual(
        mockResponse.GetUserProgramInfoResponseModel.RecommendedProgram
      )
      expect(state.isLoading).toBe(false)
      expect(state.error).toBe('')
    })

    it('should handle errors when loading workout program', async () => {
      const { workoutService } = await import('@/api/workout')
      vi.mocked(workoutService.getUserWorkoutProgramInfo).mockRejectedValueOnce(
        new Error('Network error')
      )

      const { loadWorkoutProgram } = useWorkoutStore.getState()
      await loadWorkoutProgram()

      const state = useWorkoutStore.getState()
      expect(state.error).toBe('Network error')
      expect(state.isLoading).toBe(false)
      expect(state.currentWorkout).toBe(null)
    })

    it.skip('should set and clear loading state', async () => {
      const { workoutService } = await import('@/api/workout')

      // Create a manual promise we can control
      let resolvePromise: (value: any) => void
      const controlledPromise = new Promise((resolve) => {
        resolvePromise = resolve
      })

      vi.mocked(workoutService.getUserWorkoutProgramInfo).mockReturnValue(
        controlledPromise as any
      )

      const { loadWorkoutProgram } = useWorkoutStore.getState()

      // Store reference to check loading states
      const store = useWorkoutStore

      // Initially loading should be false
      expect(store.getState().isLoading).toBe(false)

      // Start loading but don't await
      const loadPromise = loadWorkoutProgram()

      // Loading should be true after starting
      expect(store.getState().isLoading).toBe(true)

      // Resolve the promise
      resolvePromise!({
        GetUserProgramInfoResponseModel: {
          RecommendedProgram: null,
          NextWorkoutTemplate: null,
        },
      })

      // Wait for loading to complete
      await loadPromise

      // Loading should be false after completion
      expect(store.getState().isLoading).toBe(false)
    })
  })

  describe('loadWorkoutDetails', () => {
    it('should load workout details and update exercises', async () => {
      const mockWorkout: WorkoutTemplateModel = {
        Id: 123,
        Label: 'Push Day',
        IsSystemExercise: true,
        Exercises: [
          {
            Id: 1,
            Label: 'Bench Press',
            BodyPartId: 2,
            IsBodyweight: false,
            RecoModel: null as any,
          },
          {
            Id: 2,
            Label: 'Shoulder Press',
            BodyPartId: 3,
            IsBodyweight: false,
            RecoModel: null as any,
          },
        ],
      }

      const { workoutService } = await import('@/api/workout')
      vi.mocked(workoutService.getWorkoutDetails).mockResolvedValueOnce(
        mockWorkout
      )

      const { loadWorkoutDetails } = useWorkoutStore.getState()
      await loadWorkoutDetails(123)

      const state = useWorkoutStore.getState()
      expect(state.currentWorkout).toEqual(mockWorkout)
      expect(state.exercises).toEqual(mockWorkout.Exercises)
      expect(state.isLoading).toBe(false)
      expect(state.error).toBe('')
    })

    it('should handle workout with no exercises', async () => {
      const mockWorkout: WorkoutTemplateModel = {
        Id: 123,
        Label: 'Rest Day',
        IsSystemExercise: true,
        Exercises: null,
      }

      const { workoutService } = await import('@/api/workout')
      vi.mocked(workoutService.getWorkoutDetails).mockResolvedValueOnce(
        mockWorkout
      )

      const { loadWorkoutDetails } = useWorkoutStore.getState()
      await loadWorkoutDetails(123)

      const state = useWorkoutStore.getState()
      expect(state.exercises).toEqual([])
    })

    it('should cache workout in todaysWorkout', async () => {
      const mockWorkout: WorkoutTemplateModel = {
        Id: 123,
        Label: 'Push Day',
        IsSystemExercise: true,
        Exercises: [],
      }

      const { workoutService } = await import('@/api/workout')
      vi.mocked(workoutService.getWorkoutDetails).mockResolvedValueOnce(
        mockWorkout
      )

      const setCachedTodaysWorkout = vi.fn()
      useWorkoutStore.setState({ setCachedTodaysWorkout } as any)

      const { loadWorkoutDetails } = useWorkoutStore.getState()
      await loadWorkoutDetails(123)

      expect(setCachedTodaysWorkout).toHaveBeenCalledWith([mockWorkout])
    })
  })

  describe('loadExerciseRecommendation', () => {
    beforeEach(() => {
      // Set up current workout
      useWorkoutStore.setState({
        currentWorkout: {
          Id: 456,
          Label: 'Push Day',
          IsSystemExercise: true,
          Exercises: [],
        },
      })
    })

    it('should load exercise recommendation and store in cache', async () => {
      const mockRecommendation: RecommendationModel = {
        Series: 3,
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        OneRMProgress: 85,
        RecommendationInKg: 61.23,
        OneRMPercentage: 75,
        WarmUpReps1: 10,
        WarmUpReps2: 8,
        WarmUpWeightSet1: { Lb: 95, Kg: 43.09 },
        WarmUpWeightSet2: { Lb: 115, Kg: 52.16 },
        WarmUpsList: [],
        WarmupsCount: 2,
        RpRest: 120,
        NbPauses: 0,
        NbRepsPauses: 0,
        IsEasy: false,
        IsMedium: true,
        IsBodyweight: false,
        Increments: { Lb: 2.5, Kg: 1 },
        Max: { Lb: 200, Kg: 90.72 },
        Min: { Lb: 45, Kg: 20.41 },
        IsNormalSets: true,
        IsDeload: false,
        IsBackOffSet: false,
        BackOffSetWeight: { Lb: 0, Kg: 0 },
        IsMaxChallenge: false,
        IsLightSession: false,
        FirstWorkSetReps: 10,
        FirstWorkSetWeight: { Lb: 135, Kg: 61.23 },
        FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
        IsPyramid: false,
        IsReversePyramid: false,
        HistorySet: [],
        ReferenceSetHistory: {} as any,
        MinReps: 8,
        MaxReps: 12,
        isPlateAvailable: true,
      }

      const { workoutService } = await import('@/api/workout')
      vi.mocked(workoutService.getExerciseRecommendation).mockResolvedValueOnce(
        mockRecommendation
      )

      const { loadExerciseRecommendation, getCacheKey } =
        useWorkoutStore.getState()
      await loadExerciseRecommendation(1)

      const state = useWorkoutStore.getState()
      const cacheKey = getCacheKey('<EMAIL>', 1, 456)

      expect(state.exerciseRecommendations.get(cacheKey)).toEqual(
        mockRecommendation
      )
      expect(state.loadingStates.get(1)).toBe(undefined)
      expect(workoutService.getExerciseRecommendation).toHaveBeenCalledWith({
        Username: '<EMAIL>',
        ExerciseId: 1,
        WorkoutId: 456,
      })
    })

    it('should not load if already loading', async () => {
      const { workoutService } = await import('@/api/workout')
      vi.mocked(workoutService.getExerciseRecommendation).mockClear()

      // Set loading state
      useWorkoutStore.setState((state) => {
        state.loadingStates.set(1, true)
        return state
      })

      const { loadExerciseRecommendation } = useWorkoutStore.getState()
      await loadExerciseRecommendation(1)

      expect(workoutService.getExerciseRecommendation).not.toHaveBeenCalled()
    })

    it('should handle errors and store them', async () => {
      const { workoutService } = await import('@/api/workout')
      vi.mocked(workoutService.getExerciseRecommendation).mockRejectedValueOnce(
        new Error('API Error')
      )

      const { loadExerciseRecommendation } = useWorkoutStore.getState()
      await loadExerciseRecommendation(1)

      const state = useWorkoutStore.getState()
      const error = state.errors.get(1)

      expect(error).toBeDefined()
      expect(error?.message).toBe('API Error')
      expect(state.loadingStates.get(1)).toBe(undefined)
    })

    it('should not load if no current workout', async () => {
      const { workoutService } = await import('@/api/workout')
      vi.mocked(workoutService.getExerciseRecommendation).mockClear()

      // Clear current workout
      useWorkoutStore.setState({ currentWorkout: null })

      const { loadExerciseRecommendation } = useWorkoutStore.getState()
      await loadExerciseRecommendation(1)

      expect(workoutService.getExerciseRecommendation).not.toHaveBeenCalled()
    })

    it('should not load if no auth state', async () => {
      const { workoutService } = await import('@/api/workout')
      vi.mocked(workoutService.getExerciseRecommendation).mockClear()

      // Mock no auth state
      vi.mocked(localStorage.getItem).mockReturnValueOnce(null)

      const { loadExerciseRecommendation } = useWorkoutStore.getState()
      await loadExerciseRecommendation(1)

      expect(workoutService.getExerciseRecommendation).not.toHaveBeenCalled()
    })
  })

  describe('getCacheKey', () => {
    it('should generate correct cache key', () => {
      const { getCacheKey } = useWorkoutStore.getState()
      const key = getCacheKey('<EMAIL>', 123, 456)

      expect(key).toBe('<EMAIL>-123-456')
    })
  })

  describe('Selectors', () => {
    beforeEach(() => {
      // Set up state
      useWorkoutStore.setState({
        currentWorkout: {
          Id: 456,
          Label: 'Push Day',
          IsSystemExercise: true,
          Exercises: [],
        },
        exercises: [
          { Id: 1, Label: 'Bench Press', BodyPartId: 2 } as any,
          { Id: 2, Label: 'Shoulder Press', BodyPartId: 3 } as any,
        ],
        exerciseRecommendations: new Map([
          ['<EMAIL>-1-456', { Reps: 10, Series: 3 } as any],
        ]),
        loadingStates: new Map([[1, true]]),
      })
    })

    describe('getExerciseRecommendation', () => {
      it('should return recommendation for exercise', () => {
        const { getExerciseRecommendation } = useWorkoutStore.getState()
        const recommendation = getExerciseRecommendation(1)

        expect(recommendation).toEqual({ Reps: 10, Series: 3 })
      })

      it('should return undefined if no recommendation', () => {
        const { getExerciseRecommendation } = useWorkoutStore.getState()
        const recommendation = getExerciseRecommendation(999)

        expect(recommendation).toBe(undefined)
      })

      it('should return undefined if no auth', () => {
        vi.mocked(localStorage.getItem).mockReturnValueOnce(null)

        const { getExerciseRecommendation } = useWorkoutStore.getState()
        const recommendation = getExerciseRecommendation(1)

        expect(recommendation).toBe(undefined)
      })
    })

    describe('isExerciseLoading', () => {
      it('should return true if exercise is loading', () => {
        const { isExerciseLoading } = useWorkoutStore.getState()
        expect(isExerciseLoading(1)).toBe(true)
      })

      it('should return false if exercise is not loading', () => {
        const { isExerciseLoading } = useWorkoutStore.getState()
        expect(isExerciseLoading(2)).toBe(false)
      })
    })

    describe('getWorkoutExercises', () => {
      it('should return all exercises', () => {
        const { getWorkoutExercises } = useWorkoutStore.getState()
        const exercises = getWorkoutExercises()

        expect(exercises).toHaveLength(2)
        expect(exercises[0].Label).toBe('Bench Press')
        expect(exercises[1].Label).toBe('Shoulder Press')
      })
    })
  })

  describe('State Persistence', () => {
    it('should persist currentProgram and exerciseRecommendations', () => {
      const program: WorkoutTemplateGroupModel = {
        Id: 1,
        Label: 'Beginner Program',
        Days: 3,
        IsSystemExercise: true,
      } as any // Type cast needed due to missing required fields

      const recommendations = new Map([
        ['<EMAIL>-1-456', { Reps: 10 } as any],
      ])

      useWorkoutStore.setState({
        currentProgram: program,
        exerciseRecommendations: recommendations,
      })

      // Verify state was set correctly
      const state = useWorkoutStore.getState()
      expect(state.currentProgram).toEqual(program)
      expect(
        state.exerciseRecommendations.get('<EMAIL>-1-456')
      ).toEqual({ Reps: 10 })
    })
  })
})
