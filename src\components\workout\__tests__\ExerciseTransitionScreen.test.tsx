import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, act } from '@testing-library/react'
import { ExerciseTransitionScreen } from '../ExerciseTransitionScreen'

describe('ExerciseTransitionScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.runOnlyPendingTimers()
    vi.useRealTimers()
  })

  it('should show checkmark initially', () => {
    const onComplete = vi.fn()
    render(
      <ExerciseTransitionScreen
        exerciseName="Bench Press"
        onComplete={onComplete}
      />
    )

    // Should show success icon wrapper initially
    expect(screen.getByTestId('success-icon-wrapper')).toBeInTheDocument()
    expect(screen.queryByText('Bench Press')).not.toBeInTheDocument()
  })

  it('should show exercise name after 400ms', () => {
    const onComplete = vi.fn()
    render(
      <ExerciseTransitionScreen
        exerciseName="Bench Press"
        onComplete={onComplete}
      />
    )

    // Initially shows checkmark
    expect(screen.getByTestId('success-icon-wrapper')).toBeInTheDocument()

    // After 400ms, should hide checkmark and show exercise name
    act(() => {
      vi.advanceTimersByTime(400)
    })
    expect(screen.queryByTestId('success-icon-wrapper')).not.toBeInTheDocument()
    expect(screen.getByText('Bench Press')).toBeInTheDocument()
  })

  it('should call onComplete after 800ms', () => {
    const onComplete = vi.fn()

    // Mock requestAnimationFrame
    const originalRAF = global.requestAnimationFrame
    global.requestAnimationFrame = vi.fn((cb) => {
      cb()
      return 1
    })
    render(
      <ExerciseTransitionScreen
        exerciseName="Bench Press"
        onComplete={onComplete}
      />
    )

    expect(onComplete).not.toHaveBeenCalled()

    // Advance to 800ms
    act(() => {
      vi.advanceTimersByTime(800)
    })
    expect(onComplete).toHaveBeenCalledTimes(1)

    // Restore
    global.requestAnimationFrame = originalRAF
  })

  it('should show generic message when no exercise name provided', () => {
    const onComplete = vi.fn()
    render(<ExerciseTransitionScreen onComplete={onComplete} />)

    // Should still show checkmark initially
    expect(screen.getByTestId('success-icon-wrapper')).toBeInTheDocument()

    // After 400ms, should show generic message
    act(() => {
      vi.advanceTimersByTime(400)
    })
    expect(screen.queryByTestId('success-icon-wrapper')).not.toBeInTheDocument()
    expect(screen.getByText('Loading exercise...')).toBeInTheDocument()
  })

  it('should cleanup timers on unmount', () => {
    const onComplete = vi.fn()
    const { unmount } = render(
      <ExerciseTransitionScreen
        exerciseName="Bench Press"
        onComplete={onComplete}
      />
    )

    // Unmount before completion
    vi.advanceTimersByTime(300)
    unmount()

    // Advance past completion time
    vi.advanceTimersByTime(500)

    // onComplete should not have been called
    expect(onComplete).not.toHaveBeenCalled()
  })

  it('should use requestAnimationFrame for onComplete to avoid XHR violations', () => {
    const mockRequestAnimationFrame = vi.fn((cb) => {
      cb()
      return 1
    })
    global.requestAnimationFrame = mockRequestAnimationFrame

    const onComplete = vi.fn()
    render(
      <ExerciseTransitionScreen
        exerciseName="Bench Press"
        onComplete={onComplete}
      />
    )

    // Advance to completion
    vi.advanceTimersByTime(800)

    // Should have used requestAnimationFrame
    expect(mockRequestAnimationFrame).toHaveBeenCalled()
    expect(onComplete).toHaveBeenCalled()
  })

  it('should handle missing requestAnimationFrame gracefully', () => {
    // @ts-expect-error - Testing fallback behavior
    delete global.requestAnimationFrame

    const onComplete = vi.fn()
    render(
      <ExerciseTransitionScreen
        exerciseName="Bench Press"
        onComplete={onComplete}
      />
    )

    // Advance to completion
    vi.advanceTimersByTime(800)

    // Should still call onComplete without requestAnimationFrame
    expect(onComplete).toHaveBeenCalled()
  })

  it('should render SuccessIcon with fade-in animation', () => {
    const onComplete = vi.fn()
    render(
      <ExerciseTransitionScreen
        exerciseName="Bench Press"
        onComplete={onComplete}
      />
    )

    // Get the success icon within the wrapper
    const successIconWrapper = screen.getByTestId('success-icon-wrapper')
    const successIcon = successIconWrapper.querySelector(
      '[data-testid="success-icon"]'
    )

    expect(successIcon).toBeInTheDocument()
    expect(successIcon).toHaveClass('animate-fade-in')
  })
})
