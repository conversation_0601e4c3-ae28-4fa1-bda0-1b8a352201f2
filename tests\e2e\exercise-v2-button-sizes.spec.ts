import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from './helpers/auth-helper'
import { WorkoutHelper } from './helpers/workout-helper'

test.describe('ExerciseV2 - <PERSON>ton Sizes', () => {
  test('should have larger plus/minus buttons with proper tap targets', async ({
    page,
  }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 390, height: 844 })

    // Login
    await setupAuthenticatedUser(page)

    // Navigate to workout
    const workoutHelper = new WorkoutHelper(page)
    await workoutHelper.startFirstExerciseV2()

    // Wait for the current set card to be visible
    await page.waitForSelector('[data-testid="current-set-card"]', {
      state: 'visible',
    })

    // Check reps increment/decrement buttons
    const repsIncrement = page.getByLabel('Increase reps')
    const repsDecrement = page.getByLabel('Decrease reps')

    // Verify buttons are visible
    await expect(repsIncrement).toBeVisible()
    await expect(repsDecrement).toBeVisible()

    // Check button sizes (p-3 class adds 12px padding)
    // SVG icons should be w-12 h-12 (48px)
    const repsIncrementBox = await repsIncrement.boundingBox()
    const repsDecrementBox = await repsDecrement.boundingBox()

    // Total size should be approximately 72px (48px icon + 24px padding)
    expect(repsIncrementBox?.width).toBeGreaterThanOrEqual(70)
    expect(repsIncrementBox?.height).toBeGreaterThanOrEqual(70)
    expect(repsDecrementBox?.width).toBeGreaterThanOrEqual(70)
    expect(repsDecrementBox?.height).toBeGreaterThanOrEqual(70)

    // Check weight increment/decrement buttons
    const weightIncrement = page.getByLabel('Increase weight')
    const weightDecrement = page.getByLabel('Decrease weight')

    // Verify buttons are visible
    await expect(weightIncrement).toBeVisible()
    await expect(weightDecrement).toBeVisible()

    // Check button sizes
    const weightIncrementBox = await weightIncrement.boundingBox()
    const weightDecrementBox = await weightDecrement.boundingBox()

    expect(weightIncrementBox?.width).toBeGreaterThanOrEqual(70)
    expect(weightIncrementBox?.height).toBeGreaterThanOrEqual(70)
    expect(weightDecrementBox?.width).toBeGreaterThanOrEqual(70)
    expect(weightDecrementBox?.height).toBeGreaterThanOrEqual(70)

    // Test tap functionality
    // Get initial reps value
    const repsInput = page.locator('#reps-input')
    const initialReps = await repsInput.inputValue()

    // Click increment button
    await repsIncrement.click()

    // Verify reps increased
    const newReps = await repsInput.inputValue()
    expect(parseInt(newReps)).toBe(parseInt(initialReps) + 1)

    // Test weight functionality
    const weightInput = page.locator('#weight-input')
    const initialWeight = await weightInput.inputValue()

    // Click weight increment button
    await weightIncrement.click()

    // Verify weight increased
    const newWeight = await weightInput.inputValue()
    expect(parseFloat(newWeight)).toBeGreaterThan(parseFloat(initialWeight))
  })
})
