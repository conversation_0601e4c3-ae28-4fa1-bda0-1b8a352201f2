import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { CurrentSetCard } from '../CurrentSetCard'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  useAnimation: () => ({
    start: vi.fn(),
  }),
}))

const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsTimeBased: false,
  IsFinished: false,
}

const mockCurrentSet: WorkoutLogSerieModel = {
  Id: 1,
  Reps: 10,
  Weight: { Kg: 80, Lb: 175 },
  IsWarmups: false,
  IsNext: true,
  IsFinished: false,
}

const defaultProps = {
  exercise: mockExercise,
  currentSet: mockCurrentSet,
  setData: { reps: 10, weight: 80, duration: 0 },
  onSetDataChange: vi.fn(),
  onComplete: vi.fn(),
  onSkip: vi.fn(),
  isSaving: false,
  completedSets: 0,
  totalSets: 6,
  unit: 'kg' as const,
}

describe('CurrentSetCard - Button Width', () => {
  it('should have Save set button as wide as the input controls grid', () => {
    // Given: CurrentSetCard is rendered
    render(<CurrentSetCard {...defaultProps} />)

    // When: User views the save button
    const saveButton = screen.getByRole('button', { name: /save set/i })

    // Then: Button should span full width of the grid
    expect(saveButton.parentElement).toHaveClass('w-full')
    expect(saveButton).toHaveClass('w-full')
  })

  it('should not have centered button container', () => {
    // Given: CurrentSetCard is rendered
    render(<CurrentSetCard {...defaultProps} />)

    // When: User views the button container
    const saveButton = screen.getByRole('button', { name: /save set/i })
    const buttonContainer = saveButton.parentElement

    // Then: Container should not use justify-center
    expect(buttonContainer).not.toHaveClass('justify-center')
    expect(buttonContainer).toHaveClass('w-full')
  })

  it('should maintain button visibility on all screen sizes', () => {
    // Given: CurrentSetCard is rendered
    render(<CurrentSetCard {...defaultProps} />)

    // When: User views the button
    const saveButton = screen.getByRole('button', { name: /save set/i })

    // Then: Button should be properly styled for full width
    expect(saveButton).toHaveClass('w-full')
    expect(saveButton).toHaveClass('py-4')
    expect(saveButton).toHaveClass('bg-gradient-metallic-gold')
    expect(saveButton).toHaveClass('text-text-inverse')
    expect(saveButton).toHaveClass('shadow-theme-xl')
  })
})
