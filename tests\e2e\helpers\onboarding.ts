import { Page } from '@playwright/test'

export async function skipOnboarding(page: Page) {
  // Check if onboarding is present and skip it
  const onboardingVisible = await page
    .locator('[data-testid="onboarding"]')
    .isVisible()
    .catch(() => false)

  if (onboardingVisible) {
    await page.locator('[data-testid="skip-onboarding"]').click()
    await page.waitForSelector('[data-testid="onboarding"]', {
      state: 'hidden',
    })
  }
}
