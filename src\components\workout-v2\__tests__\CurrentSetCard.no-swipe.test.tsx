import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { CurrentSetCard } from '../CurrentSetCard'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion to remove motion functionality
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => {
      // Remove drag and animation props - should be static div
      const {
        drag,
        dragConstraints,
        onDragStart,
        onDragEnd,
        animate,
        ...staticProps
      } = props
      return <div {...staticProps}>{children}</div>
    },
  },
}))

const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsTimeBased: false,
  IsFinished: false,
}

const mockCurrentSet: WorkoutLogSerieModel = {
  Id: 1,
  Reps: 10,
  Weight: { Kg: 80, Lb: 175 },
  IsWarmups: false,
  IsNext: true,
  IsFinished: false,
}

const defaultProps = {
  exercise: mockExercise,
  currentSet: mockCurrentSet,
  setData: { reps: 10, weight: 80, duration: 0 },
  onSetDataChange: vi.fn(),
  onComplete: vi.fn(),
  // onSkip should be REMOVED - test will fail initially
  isSaving: false,
  completedSets: 1,
  unit: 'lbs' as const,
}

describe('CurrentSetCard - No Swipe Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should NOT have swipe hint text', () => {
    render(<CurrentSetCard {...defaultProps} />)

    // Test will FAIL initially - swipe hint currently exists
    expect(
      screen.queryByText('Swipe left to skip · right to complete')
    ).not.toBeInTheDocument()
    expect(screen.queryByText(/swipe/i)).not.toBeInTheDocument()
  })

  it('should NOT have drag functionality', () => {
    render(<CurrentSetCard {...defaultProps} />)

    // Test will FAIL initially - drag props currently exist
    const card = screen.getByTestId('current-set-card')
    expect(card).not.toHaveAttribute('draggable')

    // Should not have cursor-grab class
    expect(card).not.toHaveClass('cursor-grab')
    expect(card).not.toHaveClass('cursor-grabbing')
  })

  it('should NOT accept onSkip prop', () => {
    // Test will FAIL initially - onSkip prop currently exists in interface
    const propsWithoutSkip = {
      exercise: mockExercise,
      currentSet: mockCurrentSet,
      setData: { reps: 10, weight: 80, duration: 0 },
      onSetDataChange: vi.fn(),
      onComplete: vi.fn(),
      // onSkip should not be in interface anymore
      isSaving: false,
      completedSets: 1,
      unit: 'lbs' as const,
    }

    // This should compile without TypeScript errors after our changes
    expect(() => render(<CurrentSetCard {...propsWithoutSkip} />)).not.toThrow()
  })

  it('should have only Save button for interaction', () => {
    render(<CurrentSetCard {...defaultProps} />)

    // Test will FAIL initially - component may have swipe interactions
    const saveButton = screen.getByRole('button', { name: /save set/i })
    expect(saveButton).toBeInTheDocument()

    // Should be the primary interaction method
    expect(saveButton).toHaveClass('w-full') // Full width
    expect(saveButton).toHaveClass('bg-gradient-to-r') // Gold gradient styling
  })

  it('should call onComplete when Save button is clicked', async () => {
    const user = userEvent.setup()
    const mockOnComplete = vi.fn()

    render(<CurrentSetCard {...defaultProps} onComplete={mockOnComplete} />)

    const saveButton = screen.getByRole('button', { name: /save set/i })
    await user.click(saveButton)

    expect(mockOnComplete).toHaveBeenCalledTimes(1)
  })

  it('should work properly with wheel pickers without swipe interference', async () => {
    const mockOnSetDataChange = vi.fn()

    render(
      <CurrentSetCard {...defaultProps} onSetDataChange={mockOnSetDataChange} />
    )

    // Test will FAIL initially - swipe might interfere with wheel picker interactions
    const repsWheel = screen.getByTestId('reps-wheel-picker')
    const weightWheel = screen.getByTestId('weight-wheel-picker')

    // Should be able to interact with wheel pickers without swipe conflicts
    expect(repsWheel).toBeInTheDocument()
    expect(weightWheel).toBeInTheDocument()

    // No drag handlers should interfere with wheel picker touch events
    expect(repsWheel.closest('[draggable]')).toBeNull()
    expect(weightWheel.closest('[draggable]')).toBeNull()
  })
})
